"""
HSI Direct Georeferencing Tool - Enhanced Version (LS1)

This package contains the refactored and enhanced version of the HSI direct georeferencing tool,
implementing the recommendations from the research findings and prompts in prompts_LS1.md.

Key improvements:
- Modular architecture with clear separation of concerns
- Rigorous SensorModel class for geometric calculations
- Enhanced ray-DSM intersection with tiling support
- Error propagation and uncertainty estimation
- Comprehensive input validation and logging
- Testing framework integration
"""

__version__ = "1.0.0"
__author__ = "HSI Georeferencing Team"

# Core modules
from .config_models import Config, load_and_validate_config
from .sensor_model import SensorModel
from .utils import setup_logger, CoordinateTransformer
from .dsm_manager import DSMManager
from .error_propagation import ErrorPropagationModule

# Main processing modules
from .georeferencing import GeoreferencingProcessor
from .rgb_creation import RGBGeoTIFFCreator
from .data_synchronization import DataSynchronizer
from .plotting import EnhancedPlotter

__all__ = [
    'Config',
    'load_and_validate_config',
    'SensorModel',
    'setup_logger',
    'CoordinateTransformer',
    'DSMManager',
    'ErrorPropagationModule',
    'GeoreferencingProcessor',
    'RGBGeoTIFFCreator',
    'DataSynchronizer',
    'EnhancedPlotter'
]
