# Recommendations for Enhancing the Direct Georeferencing Tool

This section provides detailed, actionable recommendations for improving the user's direct georeferencing tool. These recommendations are derived from the integrated conceptual model ([`research/04_synthesis/01_integrated_model.md`](../../04_synthesis/01_integrated_model.md)), key insights ([`research/04_synthesis/02_key_insights.md`](../../04_synthesis/02_key_insights.md)), and practical application suggestions ([`research/04_synthesis/03_practical_applications.md`](../../04_synthesis/03_practical_applications.md)).

## I. Sensor Model and Calibration Parameter Enhancements ([`config.toml`](../../config.toml))

**Based on Key Insights 1 & 3; Practical Applications Section 1.**

1.  **Expand Interior Orientation Parameters (IOPs):**
    *   **Action:** Modify [`config.toml`](../../config.toml) to include comprehensive IOPs.
    *   **Details:**
        *   `focal_length_mm`
        *   `pixel_size_um`
        *   `principal_point_x_mm`, `principal_point_y_mm`
        *   Lens distortion coefficients (e.g., <PERSON>'s model: `k1, k2, k3, p1, p2`).
    *   **Rationale:** Crucial for accurate ray generation and correcting sensor imperfections.

2.  **Define Precise Boresight Alignment Parameters:**
    *   **Action:** Add a section in [`config.toml`](../../config.toml) for boresight misalignment angles.
    *   **Details:** `roll_offset_deg`, `pitch_offset_deg`, `yaw_offset_deg` (sensor frame relative to IMU body frame).
    *   **Rationale:** Corrects for angular differences between the sensor and navigation system.

3.  **Specify Detailed Lever Arm Parameters:**
    *   **Action:** Include 3D vector components for lever arms in [`config.toml`](../../config.toml).
    *   **Details:**
        *   GNSS antenna phase center to IMU reference point (e.g., `gnss_to_imu_x_m`, `_y_m`, `_z_m`).
        *   IMU reference point to sensor perspective center (e.g., `imu_to_sensor_x_m`, `_y_m`, `_z_m`).
        *   Ensure these are defined in a consistent body-fixed coordinate system.
    *   **Rationale:** Accurately translates navigation data to the sensor's viewpoint.

4.  **Add DSM Configuration Parameters:**
    *   **Action:** Include DSM path, type (raster/TIN), and optionally, default uncertainty.
    *   **Details:** `dsm_path`, `dsm_type` (`"raster"` or `"tin"`), `dsm_default_vertical_uncertainty_m`.
    *   **Rationale:** Facilitates flexible DSM usage and potential error-aware processing.

## II. Core Georeferencing Algorithm Enhancements ([`georeference_hsi_pixels.py`](../../georeference_hsi_pixels.py))

**Based on Key Insights 2, 3, 4, 6, 7; Practical Applications Section 2.**

5.  **Implement a Rigorous `SensorModel` Class:**
    *   **Action:** Develop a Python class to encapsulate all sensor geometric properties.
    *   **Details:**
        *   Load IOPs, boresight, and lever arm data from the enhanced [`config.toml`](../../config.toml).
        *   Implement methods to calculate precise line-of-sight (LOS) vectors for each pixel, applying lens distortion corrections.
        *   Handle transformations between sensor, body, and navigation (ECEF/geographic) frames.
    *   **Rationale:** Centralizes sensor geometry logic, improving accuracy and maintainability.

6.  **Integrate Advanced Ray-DSM Intersection:**
    *   **Action:** Replace or enhance the current ray-DSM intersection logic.
    *   **Details:**
        *   Strongly recommend using `point_cloud_utils` (which wraps Intel Embree) for high-performance ray-mesh intersection. This requires converting DSM tiles to triangular meshes (e.g., using `PyVista` or `Trimesh`).
        *   Implement efficient tiling strategies for large DSMs, loading and processing only relevant sections.
        *   Consider spatial indexing (e.g., R-tree via `rtree` library) to quickly identify relevant DSM tiles for rays.
    *   **Rationale:** Significantly improves performance and accuracy of ground point determination, especially with complex terrain.

7.  **Ensure Precise Timestamp Synchronization and Interpolation:**
    *   **Action:** Verify and refine the synchronization between HSI scanline timestamps and INS/GNSS data.
    *   **Details:** Use robust interpolation methods for navigation data (e.g., linear interpolation for position, Slerp for attitude) to get precise EOPs for each scanline time.
    *   **Rationale:** Critical for accurate EOP determination at the exact moment of image acquisition.

8.  **Develop an Error Propagation Module:**
    *   **Action:** Add functionality to estimate per-pixel georeferencing uncertainty.
    *   **Details:**
        *   Start with a simplified model based on input data quality (e.g., INS/GNSS covariance, estimated calibration errors, DSM uncertainty from config).
        *   Consider evolving to a more formal analytical propagation (using Jacobians) or a Monte Carlo simulation if higher fidelity uncertainty is required.
    *   **Rationale:** Provides essential quality information for georeferenced products.

9.  **Atmospheric Correction (Optional, Advanced):**
    *   **Action:** If targeting very high accuracy over long slant ranges, consider implementing atmospheric refraction correction.
    *   **Details:** Apply corrections to the LOS vectors based on atmospheric models (e.g., Saastamoinen).
    *   **Rationale:** Improves accuracy for demanding applications.

## III. Software Engineering and Usability Improvements

**Based on Key Insights 5, 9; Practical Applications Sections 2, 3, 4.**

10. **Refactor for Modularity and Readability:**
    *   **Action:** Break down [`georeference_hsi_pixels.py`](../../georeference_hsi_pixels.py) and [`create_georeferenced_rgb.py`](../../create_georeferenced_rgb.py) into smaller, well-defined functions or classes.
    *   **Details:** Separate concerns like data loading, sensor modeling, navigation processing, ray generation, intersection, coordinate transformation, error handling, and output writing.
    *   **Rationale:** Enhances maintainability, testability, and understandability.

11. **Implement Comprehensive Input Validation and Error Handling:**
    *   **Action:** Add robust checks for all inputs.
    *   **Details:** Validate [`config.toml`](../../config.toml) parameters (e.g., using `Pydantic`), check for file existence (NAV data, DSM), verify data formats and consistency (e.g., temporal overlap). Provide informative error messages.
    *   **Rationale:** Improves tool stability and user experience.

12. **Integrate Detailed Logging:**
    *   **Action:** Use Python's `logging` module throughout the scripts.
    *   **Details:** Log key processing steps, parameters used, warnings, errors, and timing information. Allow configurable log levels.
    *   **Rationale:** Essential for debugging, auditing, and understanding processing runs.

13. **Develop a Testing Framework:**
    *   **Action:** Implement unit and integration tests.
    *   **Details:** Use `pytest` to test individual functions/modules (e.g., sensor model calculations, coordinate transformations) and the end-to-end workflow with sample datasets and known ground truth.
    *   **Rationale:** Ensures code correctness, prevents regressions, and facilitates refactoring.

14. **Improve Output and Quality Reporting ([`create_georeferenced_rgb.py`](../../create_georeferenced_rgb.py) and main script):**
    *   **Action:** Enhance output products and provide quality information.
    *   **Details:**
        *   Ensure [`create_georeferenced_rgb.py`](../../create_georeferenced_rgb.py) uses the accurately georeferenced coordinates.
        *   If producing orthorectified images, ensure correct resampling onto a ground grid.
        *   Consider generating auxiliary quality layers (e.g., map of estimated positional uncertainty per pixel).
        *   Include comprehensive metadata in output files.
    *   **Rationale:** Provides users with more valuable and interpretable results.

## IV. Calibration and Validation Workflow

**Based on Key Insights 1, 4, 9; Practical Applications Section 4.**

15. **Establish a Continuous Calibration and Validation Process:**
    *   **Action:** Develop procedures and potentially helper scripts to facilitate regular in-flight calibration/validation.
    *   **Details:** This could involve processing data from flights over GCPs or dedicated calibration fields to refine boresight/lever arm parameters in [`config.toml`](../../config.toml).
    *   **Rationale:** Ensures calibration parameters remain accurate under operational conditions and over time.

16. **Systematic Validation Against Independent Check Points (ICPs):**
    *   **Action:** Implement or encourage a workflow for validating final georeferenced products against high-accuracy ICPs.
    *   **Details:** The tool could optionally ingest ICP coordinates and report residuals.
    *   **Rationale:** Provides an objective measure of end-to-end accuracy.

By systematically addressing these recommendations, the user's direct georeferencing tool can be significantly advanced, leading to more accurate, reliable, and usable HSI data products. Prioritization should be based on the current state of the tool and the most critical accuracy requirements.