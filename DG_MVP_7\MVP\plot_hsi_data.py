import pandas as pd
import matplotlib.pyplot as plt
import toml
import os
import numpy as np

# Plotting functions modified to accept plot_output_dir
def plot_positions(df, plot_output_dir):
    """
    Plots camera positions (pos_x, pos_y, pos_z) over hsi_line_index.
    """
    plt.figure(figsize=(12, 6))
    plt.plot(df['hsi_line_index'], df['pos_x'], label='pos_x')
    plt.plot(df['hsi_line_index'], df['pos_y'], label='pos_y')
    plt.plot(df['hsi_line_index'], df['pos_z'], label='pos_z')
    plt.xlabel("HSI-Zeilenindex")
    plt.ylabel("Position [Einheit der Posen]")
    plt.title("Kamerapositionen über HSI-Zeilenindex")
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(plot_output_dir, "plot_positions_over_index.png"))
    plt.close()

def plot_trajectory_2d(df, plot_output_dir):
    """
    Plots the 2D camera trajectory (pos_y vs. pos_x).
    """
    plt.figure(figsize=(8, 8))
    plt.plot(df['pos_x'], df['pos_y'])
    plt.xlabel("Position X [Einheit]")
    plt.ylabel("Position Y [Einheit]")
    plt.title("2D-Kameratrajektorie (Grundriss)")
    plt.axis('equal') # Ensure aspect ratio is equal for a proper trajectory view
    plt.grid(True)
    plt.savefig(os.path.join(plot_output_dir, "plot_trajectory_2d.png"))
    plt.close()

def plot_orientations(df, plot_output_dir):
    """
    Plots camera orientation (quaternions) over hsi_line_index.
    """
    plt.figure(figsize=(12, 6))
    plt.plot(df['hsi_line_index'], df['quat_x'], label='quat_x')
    plt.plot(df['hsi_line_index'], df['quat_y'], label='quat_y')
    plt.plot(df['hsi_line_index'], df['quat_z'], label='quat_z')
    plt.plot(df['hsi_line_index'], df['quat_w'], label='quat_w')
    plt.xlabel("HSI-Zeilenindex")
    plt.ylabel("Quaternion-Wert")
    plt.title("Kameraorientierung (Quaternionen) über HSI-Zeilenindex")
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(plot_output_dir, "plot_orientation_over_index.png"))
    plt.close()

def plot_interpolation_quality(df, plot_output_dir):
    """
    Plots the time differences to previous and next support poses over hsi_line_index.
    """
    plt.figure(figsize=(12, 6))
    plt.plot(df['hsi_line_index'], df['time_diff_to_prev_ms'], label='Zeitdifferenz zu vorheriger Stützpose')
    plt.plot(df['hsi_line_index'], df['time_diff_to_next_ms'], label='Zeitdifferenz zu nächster Stützpose')
    plt.xlabel("HSI-Zeilenindex")
    plt.ylabel("Zeitlicher Abstand [ms]")
    plt.title("Zeitliche Abstände zu Interpolations-Stützposen")
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(plot_output_dir, "plot_interpolation_quality.png"))
    plt.close()

def plot_yaw_and_flight_direction(df, plot_output_dir):
    """
    Plots YAW angle and flight direction over hsi_line_index.
    """
    plt.figure(figsize=(12, 6))

    # Calculate YAW from quaternions
    q_w = df['quat_w']
    q_x = df['quat_x']
    q_y = df['quat_y']
    q_z = df['quat_z']
    # Yaw (psi) rotation around Z axis - calculated in radians
    yaw_rad = np.arctan2(2 * (q_w * q_z + q_x * q_y), 1 - 2 * (q_y**2 + q_z**2))
    yaw_deg = np.rad2deg(yaw_rad) # Convert to degrees

    # Calculate flight direction from positions - calculated in radians
    # Angle of the change in position (dy, dx), CCW from positive X-axis
    # diff() will introduce NaN at the first element, so we align data by dropping it or using .iloc[1:]
    d_pos_x = df['pos_x'].diff()
    d_pos_y = df['pos_y'].diff()
    
    # Flight direction as Azimuth (degrees clockwise from North, 0-360)
    flight_direction_rad_azimuth = np.arctan2(d_pos_x, d_pos_y)
    flight_direction_deg_azimuth = (np.rad2deg(flight_direction_rad_azimuth) + 360) % 360

    # Plotting
    # We use df['hsi_line_index'].iloc[1:] for flight_direction because diff() reduces length by 1
    plt.plot(df['hsi_line_index'], yaw_deg, label='YAW Winkel (° CCW from sensor X)')
    if not flight_direction_deg_azimuth.empty: # Ensure there's data to plot
        plt.plot(df['hsi_line_index'].iloc[1:], flight_direction_deg_azimuth.iloc[1:], label='Flugrichtung (Azimut ° CW from N)', linestyle='--')
    
    plt.xlabel("HSI-Zeilenindex")
    plt.ylabel("Winkel [°]")
    plt.title("YAW Winkel und Flugrichtung (Azimut) über HSI-Zeilenindex")
    plt.legend()
    plt.savefig(os.path.join(plot_output_dir, "plot_yaw_flight_direction.png"))
    plt.close()

def run_plotting(config_path: str) -> bool:
    try:
        config = toml.load(config_path)
    except FileNotFoundError:
        print(f"Fehler: Konfigurationsdatei {config_path} nicht gefunden.")
        return False
    except Exception as e:
        print(f"Fehler beim Laden der Konfigurationsdatei {config_path}: {e}")
        return False

    # Pfade aus der Konfiguration beziehen
    try:
        output_dir_from_config = config['paths']['output_directory']
        hsi_poses_filename = config['paths']['hsi_poses_csv']
        plot_output_dir = config['paths']['plot_output_directory']
    except KeyError as e:
        print(f"Fehler: Fehlender Schlüssel in der Konfigurationsdatei {config_path}: {e}")
        return False

    hsi_poses_input_path = os.path.join(output_dir_from_config, hsi_poses_filename)

    # Sicherstellen, dass das Ausgabeverzeichnis für Plots existiert
    try:
        os.makedirs(plot_output_dir, exist_ok=True)
    except OSError as e:
        print(f"Fehler beim Erstellen des Verzeichnisses {plot_output_dir}: {e}")
        return False

    # Read the CSV file into a pandas DataFrame
    try:
        data = pd.read_csv(hsi_poses_input_path)
    except FileNotFoundError:
        print(f"Error: The file '{hsi_poses_input_path}' was not found.")
        return False
    except Exception as e:
        print(f"Error reading '{hsi_poses_input_path}': {e}")
        return False

    # Create and save the plots
    try:
        plot_positions(data, plot_output_dir)
        print(f"Saved {os.path.join(plot_output_dir, 'plot_positions_over_index.png')}")

        plot_trajectory_2d(data, plot_output_dir)
        print(f"Saved {os.path.join(plot_output_dir, 'plot_trajectory_2d.png')}")

        plot_orientations(data, plot_output_dir)
        print(f"Saved {os.path.join(plot_output_dir, 'plot_orientation_over_index.png')}")

        plot_interpolation_quality(data, plot_output_dir)
        print(f"Saved {os.path.join(plot_output_dir, 'plot_interpolation_quality.png')}")

        plot_yaw_and_flight_direction(data, plot_output_dir)
        print(f"Saved {os.path.join(plot_output_dir, 'plot_yaw_flight_direction.png')}")

        print(f"All plots generated and saved successfully in '{plot_output_dir}'.")
        return True
    except Exception as e:
        print(f"Fehler beim Erstellen der Plots: {e}")
        return False

if __name__ == "__main__":
    DEFAULT_CONFIG_PATH = 'config.toml'
    print(f"Erstelle Plots der HSI Posen Daten mit Konfiguration: {DEFAULT_CONFIG_PATH}")
    success = run_plotting(config_path=DEFAULT_CONFIG_PATH)
    if success:
        print("Plots erfolgreich erstellt.")
    else:
        print("Fehler bei der Erstellung der Plots.")