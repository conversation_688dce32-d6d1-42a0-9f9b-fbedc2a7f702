"""
Unit tests for DSMManager class.

Tests DSM loading, tiling logic, spatial index queries, and ray-DSM intersection.
Includes tests for the DSM tiling bug fix from LS2_DSM_FIX.
"""

import pytest
import numpy as np
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import rasterio
from rasterio.transform import from_bounds

from ..dsm_manager import DSMManager, DSMTile
from ..config_models import Config


class TestDSMTile:
    """Test DSMTile class functionality."""
    
    def test_dsm_tile_initialization(self):
        """Test DSMTile initialization."""
        # Create sample data
        data = np.array([[100, 101, 102], [103, 104, 105]], dtype=np.float32)
        bounds = rasterio.coords.BoundingBox(0, 0, 3, 2)
        transform = from_bounds(0, 0, 3, 2, 3, 2)
        
        tile = DSMTile("test_tile", bounds, data, transform, -9999.0)
        
        assert tile.tile_id == "test_tile"
        assert tile.bounds == bounds
        assert np.array_equal(tile.data, data)
        assert tile.nodata_value == -9999.0
        assert tile.interpolator is not None
    
    def test_dsm_tile_get_elevation(self):
        """Test elevation retrieval from DSM tile."""
        # Create simple test data
        data = np.array([[100, 101], [102, 103]], dtype=np.float32)
        bounds = rasterio.coords.BoundingBox(0, 0, 2, 2)
        transform = from_bounds(0, 0, 2, 2, 2, 2)
        
        tile = DSMTile("test_tile", bounds, data, transform)
        
        # Test elevation at center of each cell
        elevation = tile.get_elevation(0.5, 1.5)  # Top-left cell
        assert not np.isnan(elevation)
        
        # Test elevation outside bounds
        elevation = tile.get_elevation(-1, -1)
        assert np.isnan(elevation)


class TestDSMManager:
    """Test DSMManager class functionality."""
    
    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration for testing."""
        config = Mock(spec=Config)
        
        # Mock DSM parameters
        config.dsm_parameters.path = "test_dsm.tif"
        config.dsm_parameters.type = "raster"
        config.dsm_parameters.nodata_value = -9999.0
        config.dsm_parameters.default_vertical_uncertainty_m = 0.5
        config.dsm_parameters.ray_dsm_max_search_dist_m = 2000.0
        config.dsm_parameters.ray_dsm_step_m = 5.0
        config.dsm_parameters.ray_dsm_bisection_tolerance_m = 0.01
        
        return config
    
    @pytest.fixture
    def sample_dsm_file(self):
        """Create a sample DSM file for testing."""
        # Create sample DSM data
        height, width = 100, 100
        dsm_data = np.random.uniform(50, 150, (height, width)).astype(np.float32)
        
        # Create temporary GeoTIFF file
        with tempfile.NamedTemporaryFile(suffix='.tif', delete=False) as tmp_file:
            tmp_path = tmp_file.name
        
        # Write DSM data to file
        transform = from_bounds(0, 0, width, height, width, height)
        
        with rasterio.open(
            tmp_path, 'w',
            driver='GTiff',
            height=height, width=width,
            count=1, dtype=dsm_data.dtype,
            crs='EPSG:32632',
            transform=transform,
            nodata=-9999.0
        ) as dst:
            dst.write(dsm_data, 1)
        
        return Path(tmp_path)
    
    def test_dsm_manager_initialization(self, mock_config):
        """Test DSMManager initialization."""
        with patch('rasterio.open') as mock_open:
            # Mock rasterio dataset
            mock_dataset = MagicMock()
            mock_dataset.bounds = rasterio.coords.BoundingBox(0, 0, 100, 100)
            mock_dataset.read.return_value = np.random.uniform(50, 150, (100, 100)).astype(np.float32)
            mock_dataset.transform = from_bounds(0, 0, 100, 100, 100, 100)
            mock_dataset.nodatavals = [-9999.0]
            mock_open.return_value.__enter__.return_value = mock_dataset
            
            dsm_manager = DSMManager(mock_config)
            
            assert dsm_manager.dsm_type == "raster"
            assert dsm_manager.nodata_value == -9999.0
            assert dsm_manager.max_search_dist == 2000.0
            assert dsm_manager.bounds is not None
            assert len(dsm_manager.tiles) > 0
    
    def test_dsm_tiling_bug_fix(self, mock_config):
        """Test the DSM tiling bug fix for proper tile ID mapping."""
        with patch('rasterio.open') as mock_open:
            # Create large DSM to trigger tiling
            height, width = 2048, 2048
            large_dsm = np.random.uniform(50, 150, (height, width)).astype(np.float32)
            
            mock_dataset = MagicMock()
            mock_dataset.bounds = rasterio.coords.BoundingBox(0, 0, width, height)
            mock_dataset.read.return_value = large_dsm
            mock_dataset.transform = from_bounds(0, 0, width, height, width, height)
            mock_dataset.nodatavals = [-9999.0]
            mock_open.return_value.__enter__.return_value = mock_dataset
            
            # Mock rtree availability
            with patch('src.dsm_manager.RTREE_AVAILABLE', True):
                with patch('src.dsm_manager.index.Index') as mock_index:
                    mock_spatial_index = MagicMock()
                    mock_index.return_value = mock_spatial_index
                    
                    dsm_manager = DSMManager(mock_config)
                    
                    # Verify that indexed_tile_ids is properly initialized
                    assert hasattr(dsm_manager, 'indexed_tile_ids')
                    assert isinstance(dsm_manager.indexed_tile_ids, list)
                    
                    # Verify that tiles were created
                    assert len(dsm_manager.tiles) > 1  # Should create multiple tiles
                    
                    # Verify that indexed_tile_ids has the same number of entries as spatial index inserts
                    expected_tiles = len(dsm_manager.tiles)
                    assert len(dsm_manager.indexed_tile_ids) == expected_tiles
                    
                    # Verify that all tile IDs in indexed_tile_ids exist in tiles dict
                    for tile_id in dsm_manager.indexed_tile_ids:
                        assert tile_id in dsm_manager.tiles
                    
                    # Verify spatial index insert calls
                    assert mock_spatial_index.insert.call_count == expected_tiles
    
    def test_get_relevant_tiles_with_spatial_index(self, mock_config):
        """Test get_relevant_tiles method with spatial index."""
        with patch('rasterio.open') as mock_open:
            # Setup mock DSM
            mock_dataset = MagicMock()
            mock_dataset.bounds = rasterio.coords.BoundingBox(0, 0, 100, 100)
            mock_dataset.read.return_value = np.random.uniform(50, 150, (100, 100)).astype(np.float32)
            mock_dataset.transform = from_bounds(0, 0, 100, 100, 100, 100)
            mock_dataset.nodatavals = [-9999.0]
            mock_open.return_value.__enter__.return_value = mock_dataset
            
            with patch('src.dsm_manager.RTREE_AVAILABLE', True):
                with patch('src.dsm_manager.index.Index') as mock_index:
                    mock_spatial_index = MagicMock()
                    mock_spatial_index.intersection.return_value = [0, 1]  # Return some tile indices
                    mock_index.return_value = mock_spatial_index
                    
                    dsm_manager = DSMManager(mock_config)
                    
                    # Manually set up indexed_tile_ids for testing
                    dsm_manager.indexed_tile_ids = ["tile_0_0", "tile_0_1024"]
                    dsm_manager.tiles["tile_0_0"] = Mock()
                    dsm_manager.tiles["tile_0_1024"] = Mock()
                    
                    # Test get_relevant_tiles
                    relevant_tiles = dsm_manager.get_relevant_tiles(50, 50)
                    
                    # Verify that spatial index was queried
                    mock_spatial_index.intersection.assert_called_once()
                    
                    # Verify that correct tiles were returned
                    assert len(relevant_tiles) == 2
    
    def test_get_elevation(self, mock_config):
        """Test elevation retrieval."""
        with patch('rasterio.open') as mock_open:
            # Create simple test DSM
            test_dsm = np.array([[100, 101], [102, 103]], dtype=np.float32)
            
            mock_dataset = MagicMock()
            mock_dataset.bounds = rasterio.coords.BoundingBox(0, 0, 2, 2)
            mock_dataset.read.return_value = test_dsm
            mock_dataset.transform = from_bounds(0, 0, 2, 2, 2, 2)
            mock_dataset.nodatavals = [-9999.0]
            mock_open.return_value.__enter__.return_value = mock_dataset
            
            dsm_manager = DSMManager(mock_config)
            
            # Test elevation retrieval
            elevation = dsm_manager.get_elevation(1, 1)
            assert not np.isnan(elevation)
            
            # Test elevation outside bounds
            elevation = dsm_manager.get_elevation(-10, -10)
            assert np.isnan(elevation)
    
    def test_calculate_ray_dsm_intersection_simple(self, mock_config):
        """Test ray-DSM intersection with simple geometry."""
        with patch('rasterio.open') as mock_open:
            # Create flat DSM at elevation 100
            flat_dsm = np.full((10, 10), 100.0, dtype=np.float32)
            
            mock_dataset = MagicMock()
            mock_dataset.bounds = rasterio.coords.BoundingBox(0, 0, 10, 10)
            mock_dataset.read.return_value = flat_dsm
            mock_dataset.transform = from_bounds(0, 0, 10, 10, 10, 10)
            mock_dataset.nodatavals = [-9999.0]
            mock_open.return_value.__enter__.return_value = mock_dataset
            
            dsm_manager = DSMManager(mock_config)
            
            # Test ray from above pointing down
            ray_origin = np.array([5.0, 5.0, 200.0])  # Above the DSM
            ray_direction = np.array([0.0, 0.0, -1.0])  # Pointing straight down
            
            x, y, z = dsm_manager.calculate_ray_dsm_intersection(ray_origin, ray_direction)
            
            # Should intersect at approximately (5, 5, 100)
            assert not np.isnan(x)
            assert not np.isnan(y)
            assert not np.isnan(z)
            assert abs(x - 5.0) < 1.0
            assert abs(y - 5.0) < 1.0
            assert abs(z - 100.0) < 5.0  # Allow some tolerance
    
    def test_calculate_ray_dsm_intersection_no_intersection(self, mock_config):
        """Test ray-DSM intersection with no intersection."""
        with patch('rasterio.open') as mock_open:
            # Create DSM
            test_dsm = np.full((10, 10), 100.0, dtype=np.float32)
            
            mock_dataset = MagicMock()
            mock_dataset.bounds = rasterio.coords.BoundingBox(0, 0, 10, 10)
            mock_dataset.read.return_value = test_dsm
            mock_dataset.transform = from_bounds(0, 0, 10, 10, 10, 10)
            mock_dataset.nodatavals = [-9999.0]
            mock_open.return_value.__enter__.return_value = mock_dataset
            
            dsm_manager = DSMManager(mock_config)
            
            # Test ray pointing away from DSM
            ray_origin = np.array([5.0, 5.0, 200.0])
            ray_direction = np.array([0.0, 0.0, 1.0])  # Pointing up, away from DSM
            
            x, y, z = dsm_manager.calculate_ray_dsm_intersection(ray_origin, ray_direction)
            
            # Should return NaN for no intersection
            assert np.isnan(x)
            assert np.isnan(y)
            assert np.isnan(z)
    
    def test_get_uncertainty_at_point(self, mock_config):
        """Test uncertainty retrieval at a point."""
        with patch('rasterio.open') as mock_open:
            mock_dataset = MagicMock()
            mock_dataset.bounds = rasterio.coords.BoundingBox(0, 0, 10, 10)
            mock_dataset.read.return_value = np.full((10, 10), 100.0, dtype=np.float32)
            mock_dataset.transform = from_bounds(0, 0, 10, 10, 10, 10)
            mock_dataset.nodatavals = [-9999.0]
            mock_open.return_value.__enter__.return_value = mock_dataset
            
            dsm_manager = DSMManager(mock_config)
            
            uncertainty = dsm_manager.get_uncertainty_at_point(5.0, 5.0)
            
            # Should return default uncertainty
            assert uncertainty == 0.5
    
    def test_load_tin_dsm_not_implemented(self, mock_config):
        """Test that TIN DSM loading raises NotImplementedError."""
        mock_config.dsm_parameters.type = "tin"
        
        with pytest.raises(NotImplementedError, match="TIN DSM support not yet implemented"):
            DSMManager(mock_config)


def test_placeholder():
    """Placeholder test to ensure pytest runs."""
    assert True
