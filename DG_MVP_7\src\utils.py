"""
Utility functions and classes for the HSI Direct Georeferencing Tool

This module provides common utility functions used across the georeferencing pipeline,
including logging setup, coordinate transformations, and validation helpers.
"""

import logging
import numpy as np
from pathlib import Path
from typing import Optional, Tuple, Union, Any, Dict
from scipy.spatial.transform import Rotation
import pyproj
from pyproj import CRS, Transformer


def setup_logger(name: str, level: str = "INFO", log_file: Optional[Path] = None) -> logging.Logger:
    """
    Set up a logger with consistent formatting.
    
    Args:
        name: Logger name
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional log file path
        
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)
    
    # Avoid adding multiple handlers if logger already exists
    if logger.handlers:
        return logger
    
    # Set level
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    logger.setLevel(numeric_level)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(numeric_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler if specified
    if log_file:
        log_file.parent.mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


class CoordinateTransformer:
    """
    Handles coordinate transformations between different reference systems.
    
    This class provides methods for transforming coordinates between:
    - Sensor frame
    - IMU body frame
    - ECEF (Earth-Centered, Earth-Fixed)
    - Geographic (lat/lon/height)
    - Map projections (UTM, etc.)
    """
    
    def __init__(self, output_epsg: int = 32632):
        """
        Initialize coordinate transformer.
        
        Args:
            output_epsg: EPSG code for output coordinate system
        """
        self.output_epsg = output_epsg
        self.output_crs = CRS.from_epsg(output_epsg)
        self.geographic_crs = CRS.from_epsg(4326)  # WGS84
        self.ecef_crs = CRS.from_epsg(4978)  # WGS84 ECEF
        
        # Create transformers
        self.geo_to_output = Transformer.from_crs(
            self.geographic_crs, self.output_crs, always_xy=True
        )
        self.output_to_geo = Transformer.from_crs(
            self.output_crs, self.geographic_crs, always_xy=True
        )
        self.ecef_to_geo = Transformer.from_crs(
            self.ecef_crs, self.geographic_crs, always_xy=True
        )
        self.geo_to_ecef = Transformer.from_crs(
            self.geographic_crs, self.ecef_crs, always_xy=True
        )
    
    def geographic_to_output(self, lon: float, lat: float, height: float = 0.0) -> Tuple[float, float, float]:
        """
        Transform from geographic coordinates to output projection.
        
        Args:
            lon: Longitude in degrees
            lat: Latitude in degrees
            height: Height in meters (default: 0.0)
            
        Returns:
            Tuple of (x, y, z) in output coordinate system
        """
        x, y, z = self.geo_to_output.transform(lon, lat, height)
        return x, y, z
    
    def output_to_geographic(self, x: float, y: float, z: float = 0.0) -> Tuple[float, float, float]:
        """
        Transform from output projection to geographic coordinates.
        
        Args:
            x: X coordinate in output system
            y: Y coordinate in output system
            z: Z coordinate in output system (default: 0.0)
            
        Returns:
            Tuple of (lon, lat, height) in degrees and meters
        """
        lon, lat, height = self.output_to_geo.transform(x, y, z)
        return lon, lat, height


def create_rotation_matrix(roll_deg: float, pitch_deg: float, yaw_deg: float, 
                          order: str = 'ZYX') -> np.ndarray:
    """
    Create rotation matrix from Euler angles.
    
    Args:
        roll_deg: Roll angle in degrees
        pitch_deg: Pitch angle in degrees
        yaw_deg: Yaw angle in degrees
        order: Rotation order (default: 'ZYX')
        
    Returns:
        3x3 rotation matrix
    """
    rotation = Rotation.from_euler(order.lower(), [yaw_deg, pitch_deg, roll_deg], degrees=True)
    return rotation.as_matrix()


def normalize_vector(vector: np.ndarray) -> np.ndarray:
    """
    Normalize a vector to unit length.
    
    Args:
        vector: Input vector
        
    Returns:
        Normalized vector
    """
    norm = np.linalg.norm(vector)
    if norm == 0:
        return vector
    return vector / norm


def validate_file_exists(file_path: Union[str, Path], description: str = "File") -> bool:
    """
    Validate that a file exists and is readable.
    
    Args:
        file_path: Path to file
        description: Description for error messages
        
    Returns:
        True if file exists and is readable, False otherwise
    """
    path = Path(file_path)
    if not path.exists():
        logging.error(f"{description} not found: {path.resolve()}")
        return False
    if not path.is_file():
        logging.error(f"{description} is not a file: {path.resolve()}")
        return False
    if not path.stat().st_size > 0:
        logging.warning(f"{description} is empty: {path.resolve()}")
    return True


def validate_directory_exists(dir_path: Union[str, Path], description: str = "Directory", 
                             create_if_missing: bool = False) -> bool:
    """
    Validate that a directory exists.
    
    Args:
        dir_path: Path to directory
        description: Description for error messages
        create_if_missing: Whether to create directory if it doesn't exist
        
    Returns:
        True if directory exists or was created, False otherwise
    """
    path = Path(dir_path)
    if not path.exists():
        if create_if_missing:
            try:
                path.mkdir(parents=True, exist_ok=True)
                logging.info(f"Created {description}: {path.resolve()}")
                return True
            except OSError as e:
                logging.error(f"Failed to create {description} {path.resolve()}: {e}")
                return False
        else:
            logging.error(f"{description} not found: {path.resolve()}")
            return False
    if not path.is_dir():
        logging.error(f"{description} is not a directory: {path.resolve()}")
        return False
    return True


def safe_divide(numerator: Union[float, np.ndarray], denominator: Union[float, np.ndarray], 
                default: float = 0.0) -> Union[float, np.ndarray]:
    """
    Safely divide two numbers, returning default value for division by zero.
    
    Args:
        numerator: Numerator
        denominator: Denominator
        default: Default value for division by zero
        
    Returns:
        Result of division or default value
    """
    if isinstance(denominator, np.ndarray):
        result = np.full_like(denominator, default, dtype=float)
        mask = denominator != 0
        result[mask] = numerator[mask] / denominator[mask]
        return result
    else:
        return numerator / denominator if denominator != 0 else default


def calculate_distance_3d(point1: np.ndarray, point2: np.ndarray) -> float:
    """
    Calculate 3D Euclidean distance between two points.
    
    Args:
        point1: First point as [x, y, z]
        point2: Second point as [x, y, z]
        
    Returns:
        Distance in same units as input coordinates
    """
    return np.linalg.norm(point2 - point1)


def interpolate_timestamps(target_time: float, times: np.ndarray, values: np.ndarray) -> Optional[np.ndarray]:
    """
    Interpolate values at a target timestamp.
    
    Args:
        target_time: Target timestamp
        times: Array of timestamps
        values: Array of values corresponding to timestamps
        
    Returns:
        Interpolated values or None if target_time is outside range
    """
    if target_time < times[0] or target_time > times[-1]:
        return None
    
    # Find surrounding indices
    idx = np.searchsorted(times, target_time)
    if idx == 0:
        return values[0]
    if idx >= len(times):
        return values[-1]
    
    # Linear interpolation
    t0, t1 = times[idx-1], times[idx]
    v0, v1 = values[idx-1], values[idx]
    
    alpha = (target_time - t0) / (t1 - t0)
    return v0 + alpha * (v1 - v0)
