# Potential Information Sources: Improving Direct Georeferencing for HSI Linescan Cameras

This document lists potential sources of information that will be consulted during the research process. The primary tool for accessing and synthesizing information from these sources will be Perplexity AI, augmented by targeted searches in academic databases and relevant organizational websites.

## 1. Academic Databases and Search Engines

*   **Perplexity AI:** Leveraged for broad and targeted searches across the web, including academic papers, technical reports, and expert discussions.
*   **Google Scholar:** For searching scholarly literature across many disciplines and sources.
*   **IEEE Xplore:** Access to technical literature in electrical engineering, computer science, and electronics, particularly relevant for sensor technology, signal processing, and remote sensing.
*   **Scopus:** Abstract and citation database of peer-reviewed literature.
*   **Web of Science:** Comprehensive research platform covering sciences, social sciences, arts, and humanities.
*   **ScienceDirect:** Leading platform for peer-reviewed scholarly literature, primarily from Elsevier.
*   **SpringerLink:** Access to journals, books, series, protocols, reference works, and proceedings.
*   **MDPI (Multidisciplinary Digital Publishing Institute):** Publisher of open-access scientific journals, often featuring remote sensing and geoinformatics research.
*   **ResearchGate & Academia.edu:** Platforms for researchers to share papers and connect, useful for accessing pre-prints and less formally published work.

## 2. Journals and Conferences

Key journals and conference proceedings in photogrammetry, remote sensing, geoinformatics, and computer vision will be prioritized. Examples include:

*   *ISPRS Journal of Photogrammetry and Remote Sensing*
*   *Remote Sensing of Environment*
*   *IEEE Transactions on Geoscience and Remote Sensing (TGRS)*
*   *Photogrammetric Engineering & Remote Sensing (PE&RS)*
*   *Remote Sensing* (MDPI Journal)
*   *International Journal of Remote Sensing*
*   *ISPRS Annals of the Photogrammetry, Remote Sensing and Spatial Information Sciences*
*   *ISPRS Archives of the Photogrammetry, Remote Sensing and Spatial Information Sciences* (Proceedings of ISPRS Congresses and Symposia)
*   Proceedings of conferences like IGARSS (International Geoscience and Remote Sensing Symposium), ASPRS (American Society for Photogrammetry and Remote Sensing) Annual Conference, SPIE Remote Sensing.

## 3. Books and Textbooks

Classic and modern textbooks on photogrammetry, digital image processing, remote sensing, and geospatial data analysis. Examples:

*   *Manual of Photogrammetry* (ASPRS)
*   *Elements of Photogrammetry with Applications in GIS* by Wolf, Dewitt, and Wilkinson
*   *Digital Photogrammetry* by Kraus
*   Books on sensor modeling, calibration, and geometric correction.
*   Books on computational geometry and ray tracing.

## 4. Technical Reports and White Papers

*   Reports from government agencies (e.g., USGS, ESA, DLR).
*   White papers and technical documentation from sensor manufacturers (e.g., Headwall Photonics, Specim, Resonon, HySpex/NEO).
*   Documentation from GPS/IMU system manufacturers (e.g., NovAtel, Applanix, VectorNav).
*   Technical reports from research institutions specializing in remote sensing.

## 5. Software Documentation and Communities

*   Documentation for relevant open-source Python libraries (e.g., GDAL, Rasterio, NumPy, SciPy, Shapely, PyProj, Trimesh, PyVista, rtree).
*   Documentation for commercial photogrammetry and remote sensing software (to understand established workflows and algorithms, e.g., Agisoft Metashape, Pix4Dmapper, ENVI, ERDAS IMAGINE).
*   Online forums and communities (e.g., Stack Exchange/GIS Stack Exchange, GitHub discussions) for practical insights and problem-solving approaches.

## 6. Standards Organizations

*   ISPRS (International Society for Photogrammetry and Remote Sensing) working groups and publications.
*   OGC (Open Geospatial Consortium) standards relevant to sensor models and geospatial data.

## 7. Specific Search Keywords (Initial List)

A combination of keywords will be used, including but not limited to:

*   "direct georeferencing airborne linescan"
*   "HSI linescan sensor model"
*   "ray tracing DSM optimization"
*   "pixel line of sight DSM intersection"
*   "boresight calibration linescanner"
*   "lever arm correction airborne remote sensing"
*   "error propagation direct georeferencing"
*   "accuracy assessment HSI georeferencing"
*   "Python photogrammetry libraries"
*   "robust DSM intersection algorithm"
*   "occlusion handling ray tracing terrain"
*   "georeferencing hyperspectral pushbroom"
*   "integrated sensor orientation linescan"

This list of sources and keywords will be refined and expanded as the research progresses and specific knowledge gaps are identified.