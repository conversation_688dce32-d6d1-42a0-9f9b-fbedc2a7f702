"""
Unit tests for DataSynchronizer class.

Tests data synchronization logic, Slerp interpolation, and validation.
"""

import pytest
import numpy as np
import pandas as pd
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, mock_open
from scipy.spatial.transform import Rotation

from ..data_synchronization import DataSynchronizer, run_data_synchronization
from ..config_models import Config


class TestDataSynchronizer:
    """Test DataSynchronizer class functionality."""
    
    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration for testing."""
        config = Mock(spec=Config)
        
        # Mock paths
        config.paths.output_directory = "output/"
        config.paths.hsi_data_directory = "data/HSI/"
        config.paths.consolidated_webodm_poses_csv = "consolidated_poses.csv"
        config.paths.hsi_poses_csv = "hsi_poses.csv"
        config.paths.hsi_base_filename = "test_hsi"
        
        # Mock processing options
        config.processing_options.log_level = "INFO"
        
        return config
    
    @pytest.fixture
    def sample_consolidated_poses(self):
        """Create sample consolidated poses DataFrame."""
        return pd.DataFrame({
            'pos_x': [1000.0, 1001.0, 1002.0, 1003.0],
            'pos_y': [2000.0, 2001.0, 2002.0, 2003.0],
            'pos_z': [100.0, 101.0, 102.0, 103.0],
            'rot_x': [0.1, 0.15, 0.2, 0.25],
            'rot_y': [0.2, 0.25, 0.3, 0.35],
            'rot_z': [0.3, 0.35, 0.4, 0.45],
            'rot_w': [0.9, 0.85, 0.8, 0.75],
            'timestamp': [1000.0, 1001.0, 1002.0, 1003.0]
        })
    
    def test_data_synchronizer_initialization(self, mock_config):
        """Test DataSynchronizer initialization."""
        synchronizer = DataSynchronizer(mock_config)
        
        assert synchronizer.config == mock_config
        assert synchronizer.logger is not None
    
    def test_load_consolidated_poses_success(self, mock_config, sample_consolidated_poses):
        """Test successful loading of consolidated poses."""
        synchronizer = DataSynchronizer(mock_config)
        
        with patch('pandas.read_csv', return_value=sample_consolidated_poses):
            with patch('pathlib.Path.exists', return_value=True):
                poses_df = synchronizer.load_consolidated_poses()
        
        assert poses_df is not None
        assert len(poses_df) == 4
        assert 'timestamp' in poses_df.columns
    
    def test_load_consolidated_poses_missing_columns(self, mock_config):
        """Test loading poses with missing required columns."""
        synchronizer = DataSynchronizer(mock_config)
        
        # DataFrame missing required columns
        incomplete_df = pd.DataFrame({
            'pos_x': [1000.0, 1001.0],
            'pos_y': [2000.0, 2001.0]
            # Missing pos_z, rot_*, timestamp
        })
        
        with patch('pandas.read_csv', return_value=incomplete_df):
            with patch('pathlib.Path.exists', return_value=True):
                poses_df = synchronizer.load_consolidated_poses()
        
        assert poses_df is None
    
    def test_load_consolidated_poses_no_valid_timestamps(self, mock_config):
        """Test loading poses with no valid timestamps."""
        synchronizer = DataSynchronizer(mock_config)
        
        # DataFrame with NaN timestamps
        poses_with_nan = pd.DataFrame({
            'pos_x': [1000.0, 1001.0],
            'pos_y': [2000.0, 2001.0],
            'pos_z': [100.0, 101.0],
            'rot_x': [0.1, 0.2],
            'rot_y': [0.2, 0.3],
            'rot_z': [0.3, 0.4],
            'rot_w': [0.9, 0.8],
            'timestamp': [np.nan, np.nan]
        })
        
        with patch('pandas.read_csv', return_value=poses_with_nan):
            with patch('pathlib.Path.exists', return_value=True):
                poses_df = synchronizer.load_consolidated_poses()
        
        assert poses_df is None
    
    def test_extract_hsi_timestamps_success(self, mock_config):
        """Test successful extraction of HSI timestamps."""
        synchronizer = DataSynchronizer(mock_config)
        
        # Mock HSI header content
        header_content = """
        lines = 100
        samples = 200
        bands = 300
        """
        
        with patch('builtins.open', mock_open(read_data=header_content)):
            with patch('pathlib.Path.exists', return_value=True):
                hsi_timestamps = synchronizer.extract_hsi_timestamps()
        
        assert hsi_timestamps is not None
        assert len(hsi_timestamps) == 100  # Number of lines
        assert hsi_timestamps[0] == 0.0  # First timestamp
        assert hsi_timestamps[-1] == 99 * 0.02  # Last timestamp (99 * 20ms)
    
    def test_extract_hsi_timestamps_missing_lines(self, mock_config):
        """Test HSI timestamp extraction with missing lines information."""
        synchronizer = DataSynchronizer(mock_config)
        
        # Header without lines information
        header_content = """
        samples = 200
        bands = 300
        """
        
        with patch('builtins.open', mock_open(read_data=header_content)):
            with patch('pathlib.Path.exists', return_value=True):
                hsi_timestamps = synchronizer.extract_hsi_timestamps()
        
        assert hsi_timestamps is None
    
    def test_interpolate_poses_linear_positions(self, mock_config, sample_consolidated_poses):
        """Test pose interpolation with linear interpolation for positions."""
        synchronizer = DataSynchronizer(mock_config)
        
        # Create HSI timestamps that fall between pose timestamps
        hsi_timestamps = np.array([1000.5, 1001.5, 1002.5])  # Between pose timestamps
        
        interpolated_df = synchronizer.interpolate_poses(sample_consolidated_poses, hsi_timestamps)
        
        assert interpolated_df is not None
        assert len(interpolated_df) == 3
        
        # Check that positions are interpolated
        assert 'pos_x' in interpolated_df.columns
        assert 'pos_y' in interpolated_df.columns
        assert 'pos_z' in interpolated_df.columns
        
        # First interpolated position should be between first two poses
        assert 1000.0 < interpolated_df.iloc[0]['pos_x'] < 1001.0
        assert 2000.0 < interpolated_df.iloc[0]['pos_y'] < 2001.0
        assert 100.0 < interpolated_df.iloc[0]['pos_z'] < 101.0
    
    def test_interpolate_poses_slerp_attitudes(self, mock_config):
        """Test pose interpolation with Slerp for attitudes."""
        synchronizer = DataSynchronizer(mock_config)
        
        # Create poses with valid quaternions
        poses_df = pd.DataFrame({
            'pos_x': [1000.0, 1001.0],
            'pos_y': [2000.0, 2001.0],
            'pos_z': [100.0, 101.0],
            'rot_x': [0.0, 0.0],
            'rot_y': [0.0, 0.0],
            'rot_z': [0.0, 0.707],  # 90-degree rotation around Z
            'rot_w': [1.0, 0.707],
            'timestamp': [1000.0, 1002.0]
        })
        
        hsi_timestamps = np.array([1001.0])  # Midpoint
        
        interpolated_df = synchronizer.interpolate_poses(poses_df, hsi_timestamps)
        
        assert interpolated_df is not None
        assert len(interpolated_df) == 1
        
        # Check that quaternions are normalized
        quat = interpolated_df.iloc[0][['rot_x', 'rot_y', 'rot_z', 'rot_w']].values
        quat_norm = np.linalg.norm(quat)
        assert abs(quat_norm - 1.0) < 1e-6
    
    def test_interpolate_poses_no_temporal_overlap(self, mock_config, sample_consolidated_poses):
        """Test pose interpolation with no temporal overlap."""
        synchronizer = DataSynchronizer(mock_config)
        
        # HSI timestamps outside pose time range
        hsi_timestamps = np.array([500.0, 600.0, 700.0])  # Before pose timestamps
        
        interpolated_df = synchronizer.interpolate_poses(sample_consolidated_poses, hsi_timestamps)
        
        assert interpolated_df is None
    
    def test_interpolate_poses_slerp_fallback(self, mock_config):
        """Test pose interpolation with Slerp fallback to linear."""
        synchronizer = DataSynchronizer(mock_config)
        
        # Create poses with potentially problematic quaternions
        poses_df = pd.DataFrame({
            'pos_x': [1000.0, 1001.0],
            'pos_y': [2000.0, 2001.0],
            'pos_z': [100.0, 101.0],
            'rot_x': [0.0, 0.0],
            'rot_y': [0.0, 0.0],
            'rot_z': [0.0, 0.0],
            'rot_w': [0.0, 0.0],  # Invalid quaternions (zero norm)
            'timestamp': [1000.0, 1002.0]
        })
        
        hsi_timestamps = np.array([1001.0])
        
        # Mock Slerp to raise an exception
        with patch('scipy.spatial.transform.Slerp', side_effect=Exception("Slerp failed")):
            interpolated_df = synchronizer.interpolate_poses(poses_df, hsi_timestamps)
        
        # Should still succeed with linear interpolation fallback
        assert interpolated_df is not None
        assert len(interpolated_df) == 1
    
    def test_save_synchronized_poses(self, mock_config):
        """Test saving synchronized poses to CSV."""
        synchronizer = DataSynchronizer(mock_config)
        
        # Create sample synchronized poses DataFrame
        synchronized_df = pd.DataFrame({
            'hsi_line_index': [0, 1, 2],
            'timestamp': [1000.0, 1001.0, 1002.0],
            'pos_x': [1000.0, 1001.0, 1002.0],
            'pos_y': [2000.0, 2001.0, 2002.0],
            'pos_z': [100.0, 101.0, 102.0],
            'rot_x': [0.1, 0.15, 0.2],
            'rot_y': [0.2, 0.25, 0.3],
            'rot_z': [0.3, 0.35, 0.4],
            'rot_w': [0.9, 0.85, 0.8]
        })
        
        with patch('pandas.DataFrame.to_csv') as mock_to_csv:
            with patch('pathlib.Path.mkdir'):
                success = synchronizer.save_synchronized_poses(synchronized_df)
        
        assert success is True
        mock_to_csv.assert_called_once()
    
    def test_run_synchronization_success(self, mock_config, sample_consolidated_poses):
        """Test complete synchronization process."""
        synchronizer = DataSynchronizer(mock_config)
        
        hsi_timestamps = np.array([1000.5, 1001.5, 1002.5])
        
        # Mock all the individual methods
        with patch.object(synchronizer, 'validate_inputs', return_value=True):
            with patch.object(synchronizer, 'load_consolidated_poses', return_value=sample_consolidated_poses):
                with patch.object(synchronizer, 'extract_hsi_timestamps', return_value=hsi_timestamps):
                    with patch.object(synchronizer, 'interpolate_poses') as mock_interpolate:
                        with patch.object(synchronizer, 'save_synchronized_poses', return_value=True):
                            # Mock interpolate_poses to return a valid DataFrame
                            mock_interpolate.return_value = pd.DataFrame({'test': [1, 2, 3]})
                            
                            success = synchronizer.run_synchronization()
        
        assert success is True
    
    def test_run_synchronization_validation_failure(self, mock_config):
        """Test synchronization process with validation failure."""
        synchronizer = DataSynchronizer(mock_config)
        
        with patch.object(synchronizer, 'validate_inputs', return_value=False):
            success = synchronizer.run_synchronization()
        
        assert success is False
    
    def test_validate_inputs_missing_files(self, mock_config):
        """Test input validation with missing files."""
        synchronizer = DataSynchronizer(mock_config)
        
        with patch('src.utils.validate_file_exists', return_value=False):
            with patch('src.utils.validate_directory_exists', return_value=True):
                result = synchronizer.validate_inputs()
        
        assert result is False
    
    def test_run_data_synchronization_function(self, mock_config):
        """Test the run_data_synchronization function."""
        with patch('src.data_synchronization.DataSynchronizer') as mock_synchronizer_class:
            mock_synchronizer = Mock()
            mock_synchronizer.run_synchronization.return_value = True
            mock_synchronizer_class.return_value = mock_synchronizer
            
            success = run_data_synchronization(mock_config)
        
        assert success is True
        mock_synchronizer_class.assert_called_once_with(mock_config)
        mock_synchronizer.run_synchronization.assert_called_once()


def test_placeholder():
    """Placeholder test to ensure pytest runs."""
    assert True
