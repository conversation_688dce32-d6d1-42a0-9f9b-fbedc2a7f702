# Prompts for Layer LS3

## General Guidance for LS3 Implementation
Based on `reflection_LS2.md`, please adhere to the following during LS3 implementation:
- **Style**: Maintain consistent logging across all modules. Ensure docstrings are comprehensive, updated with any logic changes, and specify algorithms used (e.g., for `calculate_ray_dsm_intersection`). Consistently source parameters from configuration objects rather than hardcoding.
- **Security**: Continue robust file path validation (e.g., using `validate_file_exists`, `validate_directory_exists` from [`src/utils.py`](src/utils.py)). Ensure resilient error handling for parsing operations like JSON.
- **Testing**: For all new fixes and features, create or update corresponding unit tests. Aim to increase overall test coverage towards the >80% target, especially for critical modules. Ensure tests cover edge cases identified and fixed.

---

## Prompt [LS3_HSI_TIMESTAMPS_FIX]

### Context
Issue 1 from `reflection_LS2.md` (High Severity): The `extract_hsi_timestamps` method in [`src/data_synchronization.py`](src/data_synchronization.py:167-177) currently generates synthetic HSI timestamps. This is a placeholder and will lead to incorrect data synchronization. The `scores_LS2.json` shows Correctness at 60.0, which needs improvement.

### Objective
Implement the logic to extract actual HSI line timestamps and the HSI data's start time within the `extract_hsi_timestamps` method of the `DataSynchronizer` class.

### Focus Areas
- [`src/data_synchronization.py`](src/data_synchronization.py)

### Code Reference
```python
# src/data_synchronization.py:167-177
            # For now, generate synthetic timestamps
            # In a real implementation, this would extract actual timestamps from HSI data
            # Assume 50 Hz acquisition rate (20ms per line)
            line_interval = 0.02  # seconds
            start_time = 0.0  # This should be extracted from actual HSI data
            
            hsi_timestamps = np.arange(num_lines) * line_interval + start_time
            
            self.logger.info(f"Generated {len(hsi_timestamps)} HSI timestamps")
            self.logger.info(f"HSI timestamp range: {hsi_timestamps[0]:.3f} to {hsi_timestamps[-1]:.3f}")
            self.logger.warning("Using synthetic timestamps - should be replaced with actual HSI timestamps")
```

### Requirements
1.  Modify `extract_hsi_timestamps` in [`src/data_synchronization.py`](src/data_synchronization.py) to parse actual HSI line timestamps.
2.  The method should be able to read timestamps from HSI data files or associated metadata files (e.g., header, dedicated timing file). The specific mechanism for this might need to be configurable or inferable. Assume for now that HSI data might come with a separate metadata file or embedded timing information that needs a new parsing utility.
3.  The `start_time` for HSI data acquisition must also be derived from actual data, not hardcoded.
4.  Update logging to reflect the source and nature of the extracted timestamps. Remove the warning about synthetic timestamps upon successful implementation.
5.  Add unit tests for the new timestamp extraction logic, covering various scenarios (e.g., different HSI data/metadata formats if applicable, missing timestamp data).

### Expected Improvements
- Increase **Correctness** score (target: 70+) by ensuring accurate data synchronization.
- Improve **Testability** and **Coverage** by adding new tests.

---

## Prompt [LS3_FILENAME_MATCHING_ENHANCEMENT]

### Context
Issue 2 from `reflection_LS2.md` (Medium Severity): The HAIP file matching logic in `DataConsolidator.consolidate_poses` ([`src/data_consolidation.py:233-239`](src/data_consolidation.py:233-239)) uses a simple, error-prone substring check. This can lead to incorrect association of pose data. The `reflection_LS2.md` also noted this as a performance optimization opportunity. Current Correctness is 60.0 and Performance is 65.0.

### Objective
Implement a more robust and potentially configurable filename matching strategy in `DataConsolidator.consolidate_poses` and optimize the lookup if many HAIP files are present.

### Focus Areas
- [`src/data_consolidation.py`](src/data_consolidation.py)

### Code Reference
```python
# src/data_consolidation.py:235-239
                    for haip_data in haip_data_list:
                        haip_filename = haip_data.get('filename', '')
                        # Simple filename matching (could be enhanced)
                        if filename in haip_filename or haip_filename in filename:
                            corresponding_haip = haip_data
                            break
```

### Requirements
1.  Replace the current substring matching in [`src/data_consolidation.py`](src/data_consolidation.py) with a more robust method. Consider one or more of the following, potentially selectable via configuration:
    *   Exact filename matching based on the stem (e.g., `Path(filename).stem == Path(haip_filename).stem`).
    *   Regular expression matching, where the regex pattern is provided in the configuration.
2.  If performance with many HAIP files is a concern, implement an optimization, such as pre-processing HAIP files into a dictionary keyed by their standardized filenames for O(1) average-case lookup.
3.  Ensure the new matching logic is clearly documented.
4.  Add unit tests for the new filename matching strategies, including cases that would fail with the old logic and cases testing the performance optimization if implemented.

### Expected Improvements
- Increase **Correctness** score (target: 70+) by reducing incorrect data associations.
- Increase **Performance** score (target: 70-75) by optimizing file lookups.
- Improve **Testability** and **Coverage**.

---

## Prompt [LS3_DSM_INTERSECTION_ROBUSTNESS_FIX]

### Context
Issue 3 from `reflection_LS2.md` (Medium Severity): The `elevation_difference` helper function within `calculate_ray_dsm_intersection` in [`src/dsm_manager.py:311-319`](src/dsm_manager.py:311-319) uses a fixed offset for NaN DSM values, which may not be robust in all scenarios (e.g., low flight altitudes), potentially confusing the `brentq` root-finding algorithm. Edge case handling score is 55.

### Objective
Improve the robustness of the ray-DSM intersection algorithm in [`src/dsm_manager.py`](src/dsm_manager.py) when encountering NaN DSM values.

### Focus Areas
- [`src/dsm_manager.py`](src/dsm_manager.py) (specifically `calculate_ray_dsm_intersection` and its helper `elevation_difference`)

### Code Reference
```python
# src/dsm_manager.py:311-319
        def elevation_difference(t: float) -> float:
            """Calculate difference between ray height and DSM elevation at parameter t."""
            point = ray_origin + t * ray_direction
            x, y, z_ray = point[0], point[1], point[2]
            z_dsm = self.get_elevation(x, y)
            
            if np.isnan(z_dsm):
                return z_ray - (ray_origin[2] - 10000)  # Large negative value
            return z_ray - z_dsm
```

### Requirements
1.  Modify the `elevation_difference` function in [`src/dsm_manager.py`](src/dsm_manager.py) to handle NaN `z_dsm` values more robustly.
2.  Instead of `z_ray - (ray_origin[2] - 10000)`, return a value that guarantees correct behavior for the `brentq` algorithm. For example:
    *   Return `float('-inf')` if the ray is expected to be above `z_dsm` at the start of a valid segment.
    *   Return `float('inf')` if the ray is expected to be below `z_dsm` at the start of a valid segment.
    *   Alternatively, the search logic in `calculate_ray_dsm_intersection` could be adapted to explicitly skip or handle segments where `z_dsm` is NaN.
3.  Ensure the chosen solution is well-commented, explaining the rationale.
4.  Update or add unit tests for `calculate_ray_dsm_intersection` to specifically cover scenarios with NaN DSM values, including edge cases where the old logic might have failed (e.g., low `ray_origin[2]`).

### Expected Improvements
- Increase **Correctness** score, particularly "edge_case_handling" (target: 65+).
- Improve **Testability** and **Coverage**.

---

## Prompt [LS3_GEOREFERENCING_TEST_COVERAGE]

### Context
Issue 4 from `reflection_LS2.md` (Medium Severity): The module [`src/georeferencing.py`](src/georeferencing.py), containing the critical `GeoreferencingProcessor`, lacks dedicated unit tests. The prompt [`LS2_TEST_EXPANSION`](prompts_LS2.md:165) specifically requested this. Current overall Coverage score is 70.0, below the target of >80%.

### Objective
Create a comprehensive suite of unit tests for the `GeoreferencingProcessor` class and its methods in [`src/georeferencing.py`](src/georeferencing.py).

### Focus Areas
- Create a new test file: [`src/tests/test_georeferencing.py`](src/tests/test_georeferencing.py)
- Target module: [`src/georeferencing.py`](src/georeferencing.py)

### Requirements
1.  Create a new test file [`src/tests/test_georeferencing.py`](src/tests/test_georeferencing.py).
2.  Implement unit tests for the `GeoreferencingProcessor` class.
3.  Tests should cover:
    *   Initialization of the processor.
    *   The `_process_pixel` method, including different outcomes (valid intersection, no intersection).
    *   The `process_flight_line` method, including handling of multiple pixels and aggregation of results.
4.  Mock dependencies such as `SensorModel`, `DSMManager`, and `DataSynchronizer` appropriately to isolate the logic of `GeoreferencingProcessor`.
5.  Ensure tests cover edge cases and error conditions if any are identified within `GeoreferencingProcessor`.

### Expected Improvements
- Significantly increase **Coverage** score (target: >80% for this module, contributing to overall >75%).
- Improve overall code **Correctness** and **Robustness** by validating this critical component.

---

## Prompt [LS3_DSMTILE_INTERPOLATOR_ZERO_DIVISION_FIX]

### Context
Issue 5 from `reflection_LS2.md` (Low Severity): The `DSMTile._create_interpolator` method in [`src/dsm_manager.py:60-61`](src/dsm_manager.py:60-61) could potentially encounter issues if `transform.a` (pixel width) or `transform.e` (pixel height) from the GeoTIFF transform are zero. The logic for `y_coords` also implicitly assumes `transform.e != 0`. Current edge case handling score is 55.

### Objective
Add checks for zero pixel dimensions (`transform.a`, `transform.e`) in `DSMTile._create_interpolator` and handle these cases gracefully.

### Focus Areas
- [`src/dsm_manager.py`](src/dsm_manager.py) (specifically `DSMTile._create_interpolator`)

### Code Reference
```python
# src/dsm_manager.py:59-69
        height, width = self.data.shape
        x_coords = np.array([self.transform.c + self.transform.a * (i + 0.5) for i in range(width)])
        y_coords_raw = np.array([self.transform.f + self.transform.e * (i + 0.5) for i in range(height)])
        
        # Handle different Y coordinate orientations
        if self.transform.e < 0:
            y_coords = y_coords_raw[::-1]
            data_for_interp = self.data[::-1, :]
        else: # Assumes transform.e > 0 implicitly
            y_coords = y_coords_raw
            data_for_interp = self.data.copy()
```

### Requirements
1.  In `DSMTile._create_interpolator` ([`src/dsm_manager.py`](src/dsm_manager.py)), add checks at the beginning for `self.transform.a == 0` or `self.transform.e == 0`.
2.  If these values are zero, either:
    *   Raise a specific error (e.g., `ValueError`) indicating an invalid GeoTIFF transform for interpolation.
    *   Or, if there's a sensible way to handle a singular dimension (e.g., if the interpolator can be formed for a 1D line/point, though unlikely for `RegularGridInterpolator` with 2D data), implement that. Raising an error is likely safer.
3.  Explicitly handle the `self.transform.e == 0` case in the conditional logic for `y_coords` orientation, ensuring it doesn't fall through or cause an error. (e.g., add an `elif self.transform.e > 0:` and an `else: # transform.e == 0` block).
4.  Add unit tests for `DSMTile` that specifically test these new checks with malformed/degenerate transform affine parameters.

### Expected Improvements
- Increase **Correctness** score, particularly "edge_case_handling" (target: 65+).
- Improve **Robustness** of DSM tile processing.
- Improve **Testability** and **Coverage**.