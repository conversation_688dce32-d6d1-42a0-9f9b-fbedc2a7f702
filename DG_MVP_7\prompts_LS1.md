# Prompts for Layer LS1: HSI Direct Georeferencing Tool Enhancement

This document contains a series of prompts to guide the refactoring and enhancement of the HSI Direct Georeferencing MVP tool. These prompts are based on the research findings in [`research/05_final_report/05_recommendations.md`](research/05_final_report/05_recommendations.md) and [`research/04_synthesis/03_practical_applications.md`](research/04_synthesis/03_practical_applications.md), and a review of the MVP scripts located in the `MVP/` directory.

## A. Configuration (`MVP/config.toml`)

### Prompt [LS1_CONFIG_IOPS]

**Context:**
The current [`MVP/config.toml`](MVP/config.toml:1) has minimal sensor parameters. Recommendation I.1 and Practical Application 1 emphasize the need for comprehensive Interior Orientation Parameters (IOPs) for accurate ray generation.

**Task:**
Expand the [`MVP/config.toml`](MVP/config.toml:1) file to include a dedicated section for detailed Interior Orientation Parameters (IOPs).

**Requirements:**
- Create a new TOML table, e.g., `[sensor_model.interior_orientation]`.
- Add the following IOPs with example values:
    - `focal_length_mm` (e.g., `35.0`)
    - `pixel_size_um` (e.g., `4.5`)
    - `principal_point_x_mm` (e.g., `0.01`)
    - `principal_point_y_mm` (e.g., `-0.005`)
    - Lens distortion coefficients (e.g., Brown's model): `k1`, `k2`, `k3`, `p1`, `p2` (e.g., `k1 = 1.2e-5`, etc.).
- Ensure clear comments explaining each parameter and its units.

**Previous Issues:**
- Lack of detailed IOPs in the current MVP configuration, limiting georeferencing accuracy.

**Expected Output:**
An updated [`MVP/config.toml`](MVP/config.toml:1) file with the new `[sensor_model.interior_orientation]` section and all specified parameters.

---

### Prompt [LS1_CONFIG_BORESIGHT]

**Context:**
Recommendation I.2 and Practical Application 1 highlight the importance of precise boresight alignment parameters for correcting angular differences between the sensor and the navigation system. The current [`MVP/config.toml`](MVP/config.toml:31-35) has `boresight_roll_deg`, `boresight_pitch_deg`, `boresight_yaw_deg` under `[parameters.georeferencing]`. This should be standardized.

**Task:**
Standardize and ensure clarity for boresight alignment parameters in [`MVP/config.toml`](MVP/config.toml:1).

**Requirements:**
- Create a specific TOML table, e.g., `[sensor_model.boresight_alignment_deg]`.
- Move/ensure the following parameters are within this new section:
    - `roll_offset_deg`
    - `pitch_offset_deg`
    - `yaw_offset_deg`
- These represent the angular offsets of the sensor frame relative to the IMU body frame.
- Provide clear comments defining the rotation order and direction if necessary (e.g., "Rotation from IMU body frame to sensor frame").

**Previous Issues:**
- Boresight parameters are present but could be better organized and documented for clarity.

**Expected Output:**
An updated [`MVP/config.toml`](MVP/config.toml:1) with the `[sensor_model.boresight_alignment_deg]` section.

---

### Prompt [LS1_CONFIG_LEVERARM]

**Context:**
Recommendation I.3 and Practical Application 1 stress the need for detailed 3D vector components for lever arms. The current [`MVP/config.toml`](MVP/config.toml:40-43) has `lever_arm_x_m`, `_y_m`, `_z_m` but doesn't specify which lever arm this refers to (e.g., GNSS to IMU, or IMU to Sensor). The recommendations suggest two distinct lever arms.

**Task:**
Define detailed and distinct lever arm parameters in [`MVP/config.toml`](MVP/config.toml:1).

**Requirements:**
- Create a TOML table, e.g., `[sensor_model.lever_arms_meters]`.
- Include parameters for two lever arms:
    1.  GNSS antenna phase center to IMU reference point:
        - `gnss_to_imu_x_m`
        - `gnss_to_imu_y_m`
        - `gnss_to_imu_z_m`
    2.  IMU reference point to sensor perspective center:
        - `imu_to_sensor_x_m`
        - `imu_to_sensor_y_m`
        - `imu_to_sensor_z_m`
- Ensure comments specify that these vectors are defined in a consistent body-fixed coordinate system (e.g., FRD).
- The existing `lever_arm_x_m` etc. should be mapped or replaced by this more detailed structure.

**Previous Issues:**
- Current lever arm definition in config is ambiguous and incomplete.

**Expected Output:**
An updated [`MVP/config.toml`](MVP/config.toml:1) with the `[sensor_model.lever_arms_meters]` section containing parameters for both lever arms.

---

### Prompt [LS1_CONFIG_DSM]

**Context:**
Recommendation I.4 and Practical Application 1 suggest adding DSM configuration parameters to [`MVP/config.toml`](MVP/config.toml:1) for flexible DSM usage. The current config has `dsm_file` under `[paths]` and some ray-DSM intersection parameters under `[parameters.georeferencing]`.

**Task:**
Consolidate and expand DSM-related configurations in [`MVP/config.toml`](MVP/config.toml:1).

**Requirements:**
- Create a new TOML table, e.g., `[dsm_parameters]`.
- Include:
    - `path`: Path to the DSM file (can reference the existing `paths.dsm_file`).
    - `type`: DSM type (e.g., `"raster"` or `"tin"`).
    - `default_vertical_uncertainty_m` (optional, e.g., `0.5`).
    - `nodata_value` (optional, if the DSM uses a specific nodata value that needs to be handled).
- Move existing ray-DSM intersection parameters (`ray_dsm_max_search_dist_m`, `ray_dsm_step_m`, `ray_dsm_bisection_tolerance_m`) from `[parameters.georeferencing]` to this new `[dsm_parameters]` section for better organization.

**Previous Issues:**
- DSM parameters are somewhat scattered and incomplete.

**Expected Output:**
An updated [`MVP/config.toml`](MVP/config.toml:1) with a `[dsm_parameters]` section containing all specified DSM configurations.

---

### Prompt [LS1_CONFIG_PROCESSING]

**Context:**
Practical Application 1 suggests adding general processing and output options to [`MVP/config.toml`](MVP/config.toml:1) for better control over the pipeline.

**Task:**
Add a new section to [`MVP/config.toml`](MVP/config.toml:1) for general processing options.

**Requirements:**
- Create a new TOML table, e.g., `[processing_options]`.
- Include parameters such as:
    - `log_level`: (e.g., `"INFO"`, `"DEBUG"`) for controlling script logging verbosity.
    - `enable_atmospheric_correction`: (boolean, default `false`, as it's an advanced feature).
    - `coordinate_reference_system_epsg_output`: For defining a default output CRS if not specified elsewhere (e.g., `32632`).
- Add other flags as deemed necessary for controlling optional processing steps that might be introduced.

**Previous Issues:**
- Lack of centralized control for pipeline behavior like logging or optional corrections.

**Expected Output:**
An updated [`MVP/config.toml`](MVP/config.toml:1) with the new `[processing_options]` section.

---

### Prompt [LS1_CONFIG_VALIDATION]

**Context:**
Recommendation III.11 emphasizes implementing comprehensive input validation, including for [`MVP/config.toml`](MVP/config.toml:1) parameters. Using a library like Pydantic is suggested.

**Task:**
Design and implement a Pydantic model structure for validating the entire [`MVP/config.toml`](MVP/config.toml:1) structure and its parameters. This model will be used by the Python scripts to load and validate the configuration.

**Requirements:**
- Define Pydantic models corresponding to each section of the enhanced `config.toml` (paths, sensor_model (IOPs, boresight, lever_arms), dsm_parameters, parameters.georeferencing (if any remain), parameters.rgb_geotiff_creation, parameters.plotting, processing_options).
- Include type hints and validation rules (e.g., value ranges, choices for string parameters like `dsm_type`).
- The Pydantic models should be defined in a new utility module (e.g., `config_models.py` or `utils.py`).
- Modify the configuration loading mechanism in all relevant Python scripts (e.g., [`MVP/main_pipeline.py`](MVP/main_pipeline.py:1), and individual scripts if run standalone) to use these Pydantic models for parsing and validation. If validation fails, the script should raise an informative error.

**Previous Issues:**
- Configuration parameters are loaded directly with basic error handling (KeyError, FileNotFoundError), but no systematic validation of types or values.

**Expected Output:**
- A new Python module (e.g., `MVP/config_models.py`) containing Pydantic models for config validation.
- Updated Python scripts that use these models to load and validate `config.toml`.

---

## B. Core Georeferencing (`MVP/georeference_hsi_pixels.py`)

### Prompt [LS1_GEO_SENSORMODEL_CLASS]

**Context:**
Recommendation II.5 and Practical Application 2.69 strongly advise implementing a rigorous `SensorModel` class to encapsulate sensor geometric properties, improving accuracy and maintainability. [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1) currently handles sensor geometry in a more distributed manner.

**Task:**
Create a new Python class named `SensorModel` within [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1) or a new dedicated module (e.g., `sensor_model.py`).

**Requirements:**
- The class constructor `__init__` should accept all necessary sensor parameters (IOPs, boresight, lever arms) which will be loaded from the enhanced `config.toml`.
- Initialize internal attributes to store these parameters.
- Placeholder methods for:
    - `calculate_los_vector(pixel_index)`: To calculate line-of-sight vector for a given pixel.
    - `apply_lens_distortion(vector)`: To apply lens distortion corrections.
    - `transform_sensor_to_body(vector)`: To transform a vector from sensor to body frame.
    - `transform_body_to_sensor(vector)`: To transform a vector from body to sensor frame.
- The class should be well-documented with docstrings.

**Previous Issues:**
- Sensor geometry logic is not centralized, making it harder to manage and update.
- Current `parse_sensor_model` in [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:78-112) reads angles but doesn't fully encapsulate a geometric model.

**Expected Output:**
A new `SensorModel` class definition with constructor and placeholder methods as described.

---

### Prompt [LS1_GEO_SENSORMODEL_LOAD]

**Context:**
Following the creation of the `SensorModel` class (Prompt `LS1_GEO_SENSORMODEL_CLASS`), it needs to be populated with parameters from the enhanced `config.toml`.

**Task:**
Modify the `SensorModel` class and the main georeferencing script ([`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1)) to load IOPs, boresight, and lever arm data from the `config.toml` file into an instance of `SensorModel`.

**Requirements:**
- The `SensorModel` constructor should be updated to accept a configuration object (e.g., a Pydantic model instance from Prompt `LS1_CONFIG_VALIDATION`).
- Inside the constructor, parse the relevant sections (e.g., `sensor_model.interior_orientation`, `sensor_model.boresight_alignment_deg`, `sensor_model.lever_arms_meters`) and store the parameters.
- The main part of [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:248-608) should instantiate `SensorModel` once after loading the config.
- The existing `parse_sensor_model` function in [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:78-112) which reads the per-pixel viewing angles should be integrated. This data should also be loaded by/into the `SensorModel` instance, potentially as an array of per-pixel LOS vectors in the sensor frame *before* lens distortion.

**Previous Issues:**
- Sensor parameters are read piecemeal or hardcoded implicitly.

**Expected Output:**
- `SensorModel` class capable of initializing from a config object.
- [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1) instantiating and using this `SensorModel`.

---

### Prompt [LS1_GEO_SENSORMODEL_LOS]

**Context:**
Recommendation II.5. The `SensorModel` class needs to calculate precise Line-of-Sight (LOS) vectors for each pixel, applying lens distortion corrections. The current [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1) uses `vinkelx_rad` and `vinkely_rad` from a sensor model file.

**Task:**
Implement the `calculate_los_vector` and `apply_lens_distortion` methods within the `SensorModel` class.

**Requirements:**
- `calculate_los_vector(pixel_index)`:
    - Takes a pixel index (column number) as input.
    - Uses the pre-loaded per-pixel viewing angles (from the sensor model file, e.g., `vinkelx_rad_all_pixels`, `vinkely_rad_all_pixels` in the current script, which should now be part of `SensorModel`).
    - Potentially uses `focal_length_mm` and `pixel_size_um` to form an initial vector if the sensor model file provides angles rather than vectors. A common convention for linescanners is that `vinkelx` is the across-track scan angle and `vinkely` is often assumed to be 0 (nadir looking in along-track). Clarify how `vinkelx_rad` and `vinkely_rad` form the 3D LOS vector in the sensor's coordinate system (e.g., `[tan(vinkelx), tan(vinkely), -1]` or similar, then normalize).
    - Calls `apply_lens_distortion` on this initial vector.
    - Returns the corrected 3D LOS vector in the sensor coordinate system.
- `apply_lens_distortion(vector)`:
    - Takes a 3D LOS vector (before distortion correction) and principal point/distortion coefficients (`k1, k2, k3, p1, p2`) from the `SensorModel`'s stored IOPs.
    - Implements the Brown's lens distortion model (or chosen model) to correct the vector.
    - Returns the distortion-corrected 3D LOS vector.
- The current `scale_vinkel_x` and `offset_vinkel_x` from [`MVP/config.toml`](MVP/config.toml:37-38) should be applied to `vinkelx_rad` before it's used to form the LOS vector.

**Previous Issues:**
- Lens distortion is not explicitly handled.
- LOS vector formation from sensor model file angles could be more robustly defined within the model.

**Expected Output:**
Implemented `calculate_los_vector` and `apply_lens_distortion` methods in the `SensorModel` class.

---

### Prompt [LS1_GEO_SENSORMODEL_TRANSFORM]

**Context:**
Recommendation II.5. The `SensorModel` needs to handle transformations between sensor, body, and navigation frames. [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1) currently computes `R_sensor_to_body` using boresight angles from config.

**Task:**
Implement and refine frame transformation methods within the `SensorModel` class, and ensure lever arms are correctly applied.

**Requirements:**
- `transform_sensor_to_body(vector)`:
    - Takes a vector in the sensor frame.
    - Uses the boresight alignment parameters (e.g., `roll_offset_deg`, `pitch_offset_deg`, `yaw_offset_deg`) to compute the rotation matrix from sensor to body frame (`R_sensor_to_body`). This logic might already exist in [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:480-482) and can be moved/adapted.
    - Applies this rotation to the input vector.
    - Returns the vector in the IMU body frame.
- The main georeferencing logic in [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1) needs to correctly use these transformations:
    1.  Pixel LOS vector (from `SensorModel.calculate_los_vector`) is in sensor frame.
    2.  Transform LOS to body frame using `SensorModel.transform_sensor_to_body`.
    3.  Rotate this body-frame LOS vector by the drone's attitude (world to body rotation, `R_world_to_body`, obtained from `interp_rotation_obj.as_matrix()`) to get the LOS vector in the world/navigation frame (`d_world = R_body_to_world @ d_body = (R_world_to_body).T @ d_body`).
    4.  The origin of the ray (`P_sensor`) needs to be calculated:
        `P_sensor = P_GNSS_antenna_world + R_body_to_world @ (L_IMU_to_sensor_body - L_GNSS_to_IMU_body)`
        where `P_GNSS_antenna_world` is the interpolated position from `hsi_poses.csv`, `L_IMU_to_sensor_body` and `L_GNSS_to_IMU_body` are the lever arms from `config.toml` (ensure correct signs and coordinate system). The current script uses `effective_lever_arm_body` which seems to be `IMU_to_Sensor` only. This needs to be expanded for the full GNSS-IMU-Sensor chain.
- Ensure all coordinate systems (sensor, body, navigation/world) are clearly defined and consistently used. The current script mentions FRD for IMU body.

**Previous Issues:**
- Lever arm application might be simplified or not fully account for the GNSS-IMU-Sensor chain.
- Frame transformation logic can be better encapsulated.

**Expected Output:**
- Implemented transformation methods in `SensorModel`.
- Updated georeferencing logic in [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1) to correctly use these methods and apply full lever arm corrections.

---

### Prompt [LS1_GEO_RAYDSM_INTEGRATION]

**Context:**
Recommendation II.6 and Practical Application 2.72 suggest integrating advanced Ray-DSM intersection, potentially using libraries like `point_cloud_utils` (Intel Embree) or robust ray marching. The current [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:114-246) has a `calculate_ray_dsm_intersection` function using `RegularGridInterpolator` and a custom ray marching / bisection method.

**Task:**
Refactor and enhance the ray-DSM intersection logic in [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1).

**Requirements:**
- **Option 1 (Recommended for performance with complex DSMs):**
    - Investigate and integrate `point_cloud_utils` or a similar library leveraging Intel Embree.
    - This would require converting relevant DSM tiles into triangular meshes (e.g., using `PyVista` or `Trimesh`). The `SensorModel` or a new `DSMManager` class could handle this.
- **Option 2 (Enhance existing):**
    - If sticking with the current `RegularGridInterpolator` approach:
        - Improve robustness of the `calculate_ray_dsm_intersection` function.
        - Handle edge cases more effectively (ray starting outside DSM, parallel to surface, grazing incidence).
        - Optimize the ray marching and bisection steps.
        - Ensure `nodata_value` from DSM metadata is correctly handled (current code has logic for this).
- The choice between Option 1 and 2 can be based on performance needs and complexity of typical DSMs. The prompt should aim for Option 1 if feasible, or significant improvements to Option 2.
- Ensure the intersection function returns the 3D ground coordinates (X, Y, Z).

**Previous Issues:**
- The current custom ray-DSM intersection might be slow for large/complex DSMs and could have robustness issues in edge cases.

**Expected Output:**
- A significantly improved or replaced ray-DSM intersection module/function in [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1).
- If Option 1 is chosen, this might involve adding new helper functions/classes for mesh conversion.

---

### Prompt [LS1_GEO_RAYDSM_TILING]

**Context:**
Recommendation II.6. For large DSMs, efficient tiling strategies and spatial indexing are crucial. The current DSM loading in [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:338-383) loads the entire DSM.

**Task:**
Implement DSM tiling and spatial indexing to handle large DSMs efficiently.

**Requirements:**
- If the DSM is very large, it should not be loaded entirely into memory at once.
- Implement a strategy to break the DSM into tiles.
- Use a spatial index (e.g., R-tree via the `rtree` library) for the DSM tiles.
- For each ray, query the spatial index to quickly identify and load only the relevant DSM tile(s) for intersection.
- This might involve creating a `DSMManager` class to handle DSM loading, tiling, indexing, and providing data to the intersection function.

**Previous Issues:**
- Entire DSM is loaded, which is not scalable for large datasets.

**Expected Output:**
- Modified DSM handling logic in [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1) (or a new `DSMManager` class) that implements tiling and spatial indexing for efficient processing of large DSMs.

---

### Prompt [LS1_GEO_TIMESYNC]

**Context:**
Recommendation II.7 and Practical Application 2.81 emphasize precise timestamp synchronization and robust interpolation for navigation data. [`MVP/synchronize_hsi_webodm.py`](MVP/synchronize_hsi_webodm.py:1) performs this, using linear interpolation for position and Slerp for attitude.

**Task:**
Review and refine the timestamp synchronization and EOP interpolation logic, currently in [`MVP/synchronize_hsi_webodm.py`](MVP/synchronize_hsi_webodm.py:188-270) (`interpolate_pose` function), ensuring its robustness and accuracy. The interpolated EOPs are used by [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1).

**Requirements:**
- Verify that the Slerp implementation for attitude is correct and handles edge cases (e.g., minimal number of poses for Slerp).
- Ensure linear interpolation for position is appropriate or if higher-order methods should be considered for specific dynamics.
- Confirm that timestamps from all sources (HSI, GNSS/IMU) are consistently handled (e.g., all converted to a common precise format like nanoseconds UTC).
- The `interpolate_pose` function in [`MVP/synchronize_hsi_webodm.py`](MVP/synchronize_hsi_webodm.py:188-270) seems robust in handling edge cases (HSI timestamp outside WebODM range). Maintain this robustness.
- Consider moving the core interpolation logic into a shared utility if it's extensively used or becomes complex.

**Previous Issues:**
- While Slerp is used, a detailed review for robustness under various conditions is beneficial.

**Expected Output:**
- Verified and potentially refined EOP interpolation logic, ensuring high precision for each HSI scanline time. Documentation of the interpolation methods used.

---

### Prompt [LS1_GEO_ERROR_PROPAGATION]

**Context:**
Recommendation II.8 and Practical Application 2.84 suggest developing an error propagation module to estimate per-pixel georeferencing uncertainty. This is currently not implemented.

**Task:**
Implement an initial, simplified error propagation module in [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1) to estimate per-pixel georeferencing uncertainty.

**Requirements:**
- Start with a simplified model based on input data quality:
    - Read estimated uncertainties for INS/GNSS data (if available in `hsi_poses.csv` or could be added to config as typical values).
    - Read estimated calibration errors for IOPs, boresight, lever arm (from `config.toml` as new parameters, e.g., `[sensor_model.uncertainties]`).
    - Use `dsm_default_vertical_uncertainty_m` from `config.toml`.
- Combine these uncertainties (e.g., Root Sum Square - RSS) to produce a qualitative or simplified quantitative estimate of positional uncertainty (e.g., X, Y, Z uncertainty) for each georeferenced pixel.
- Add these uncertainty estimates as new columns in the output `georeferenced_pixels.csv` file.
- This is an initial step; more formal methods (Jacobians, Monte Carlo) can be future enhancements.

**Previous Issues:**
- No estimation of georeferencing uncertainty.

**Expected Output:**
- [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1) modified to calculate and output per-pixel uncertainty estimates.
- New uncertainty parameters added to `config.toml`.

---

### Prompt [LS1_GEO_MODULARITY_MAIN]

**Context:**
Recommendation III.10 and Practical Application 2.66 call for refactoring [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1) for modularity by breaking it into smaller, well-defined functions or classes. The current `run_georeferencing` function is very long.

**Task:**
Refactor the `run_georeferencing` function in [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:248-608) into smaller, well-defined functions.

**Requirements:**
- Identify logical blocks within `run_georeferencing` and extract them into separate functions. Examples:
    - `load_configuration_georef(config_path)`
    - `initialize_sensor_model(config, num_samples_hdr, sensor_model_path)` (might use parts of existing `parse_sensor_model` and integrate with `SensorModel` class)
    - `load_pose_data(poses_file_path, num_lines_hdr)`
    - `initialize_dsm_handler(config)` (if `DSMManager` class is created)
    - `process_scanline(line_index, pose_data, sensor_model, dsm_handler, config_params)`
    - `save_georeferenced_results(results, output_file_path)`
- The main loop over HSI lines ([`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:498-589)) should call `process_scanline`.
- Ensure each new function has a clear purpose, inputs, and outputs.
- Add docstrings to all new functions.
- Aim to keep individual functions concise (e.g., under 100-150 lines if possible).

**Previous Issues:**
- The `run_georeferencing` function is monolithic (over 350 lines), reducing readability and maintainability.

**Expected Output:**
A refactored [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1) with a more modular structure and smaller, focused functions.

---

### Prompt [LS1_GEO_INPUT_VALIDATION]

**Context:**
Recommendation III.11. The script [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1) needs robust checks for all inputs beyond basic file existence.

**Task:**
Implement comprehensive input validation at the beginning of `run_georeferencing` in [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1).

**Requirements:**
- Validate `config.toml` parameters using the Pydantic models (from Prompt `LS1_CONFIG_VALIDATION`).
- Check for file existence and readability: HSI header, sensor model file, poses CSV, DSM file (if used).
- Verify data consistency:
    - Number of poses in `hsi_poses.csv` matches `lines` in HSI header.
    - Number of entries in sensor model file matches `samples` in HSI header.
    - Temporal overlap between HSI data and pose data (e.g., HSI timestamps are within the range of pose timestamps).
- Provide informative error messages if validation fails, and exit gracefully.

**Previous Issues:**
- Input validation is basic (mostly file existence checks).

**Expected Output:**
Enhanced input validation logic in [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1).

---

### Prompt [LS1_GEO_LOGGING]

**Context:**
Recommendation III.12 and Practical Application 2.93. Integrate detailed logging using Python's `logging` module throughout [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1).

**Task:**
Replace `print()` statements with Python's `logging` module in [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1).

**Requirements:**
- Import the `logging` module.
- Configure a logger (e.g., at the beginning of `run_georeferencing` or in a shared utility).
- Set log level based on `processing_options.log_level` from `config.toml`.
- Log key processing steps (e.g., "Loading configuration", "Initializing SensorModel", "Processing line X", "Ray-DSM intersection for pixel Y", "Saving results").
- Log parameters used, warnings (e.g., "No DSM intersection found for pixel Z"), and errors.
- Include timing information for major processing stages if feasible.

**Previous Issues:**
- Uses `print()` for messages, which is less flexible than `logging`.

**Expected Output:**
[`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1) using the `logging` module for all informational messages, warnings, and errors.

---

## C. Data Consolidation & Synchronization (`MVP/create_consolidated_webodm_poses.py`, `MVP/synchronize_hsi_webodm.py`)

### Prompt [LS1_CONSOLIDATE_ROBUSTNESS]

**Context:**
The script [`MVP/create_consolidated_webodm_poses.py`](MVP/create_consolidated_webodm_poses.py:1) consolidates WebODM poses. Its robustness can be improved regarding data validation and error handling.

**Task:**
Enhance the robustness of [`MVP/create_consolidated_webodm_poses.py`](MVP/create_consolidated_webodm_poses.py:1).

**Requirements:**
- **Input Validation:**
    - Before processing, validate the structure of `shots.geojson` (e.g., presence of `features`, `properties`, `filename`, `translation`, `rotation`).
    - Validate the format of `.haip` files (e.g., expected timestamp key format).
    - Check for consistency (e.g., if a `.haip` file corresponding to a `shots.geojson` entry is missing).
- **Error Handling:**
    - Improve error messages to be more specific about the file and data causing issues.
    - Handle potential `ValueError` during timestamp conversion more gracefully (current code logs a warning and skips, which is good; ensure this is consistent).
    - Decide on a strategy for partial success: if some entries fail, should the script still produce an output with valid entries, or fail entirely? (Currently, it seems to continue and report warnings).
- **Configuration Loading:** Ensure it uses Pydantic models for config validation if `config.toml` usage is expanded for this script. (Currently, it loads specific keys).

**Previous Issues:**
- Error handling is present but could be more comprehensive for different types of malformed inputs.
- Validation of input file structures could be more explicit.

**Expected Output:**
An updated [`MVP/create_consolidated_webodm_poses.py`](MVP/create_consolidated_webodm_poses.py:1) with improved input validation and error handling logic.

---

### Prompt [LS1_CONSOLIDATE_LOGGING]

**Context:**
Similar to other scripts, [`MVP/create_consolidated_webodm_poses.py`](MVP/create_consolidated_webodm_poses.py:1) uses `print()` for output.

**Task:**
Replace `print()` statements with Python's `logging` module in [`MVP/create_consolidated_webodm_poses.py`](MVP/create_consolidated_webodm_poses.py:1).

**Requirements:**
- Import and configure the `logging` module.
- Use logger for informational messages, warnings (e.g., "Skipping feature due to missing data", "HAIP file not found"), and errors.
- Log summary information (e.g., "Processed X features, found Y valid entries").
- Control log level via `config.toml` if `processing_options` are adopted by this script.

**Previous Issues:**
- Relies on `print()` for messaging.

**Expected Output:**
[`MVP/create_consolidated_webodm_poses.py`](MVP/create_consolidated_webodm_poses.py:1) utilizing the `logging` module.

---

### Prompt [LS1_SYNC_ROBUSTNESS]

**Context:**
The script [`MVP/synchronize_hsi_webodm.py`](MVP/synchronize_hsi_webodm.py:1) synchronizes HSI and WebODM data. Its robustness, especially around data loading and interpolation, can be further improved.

**Task:**
Enhance the robustness of [`MVP/synchronize_hsi_webodm.py`](MVP/synchronize_hsi_webodm.py:1).

**Requirements:**
- **Data Loading:**
    - In `load_hsi_data`: Improve handling of malformed lines in sync file (current code warns and skips, which is good). Add check if `total_lines_from_hdr` is zero or negative.
    - In `load_webodm_data`: Add checks for empty CSV or CSVs missing critical columns before attempting to process rows.
- **Interpolation (`interpolate_pose`):**
    - The Slerp fallback for `ValueError` (e.g., non-unit quaternions if rotvec conversion fails) is a simple choice; consider if a more sophisticated fallback or error reporting is needed.
    - Ensure numerical stability in calculations.
- **HDR Parsing (`parse_hdr_file`):**
    - Make parsing of `OffsetBetweenMainAntennaAndTargetPoint` more robust to variations in spacing or format if encountered.
- **Configuration Loading:** Ensure it uses Pydantic models for config validation.

**Previous Issues:**
- While generally robust, specific edge cases in input data or interpolation could be handled with more detailed checks or logging.

**Expected Output:**
An updated [`MVP/synchronize_hsi_webodm.py`](MVP/synchronize_hsi_webodm.py:1) with improved data validation, error handling, and potentially more robust interpolation fallbacks.

---

### Prompt [LS1_SYNC_LEVERARM_CONFIG]

**Context:**
[`MVP/synchronize_hsi_webodm.py`](MVP/synchronize_hsi_webodm.py:11-46) (`parse_hdr_file`) attempts to read lever arm from the HSI header. The main georeferencing script will use lever arms from `config.toml`. This synchronization script doesn't directly use lever arms for its output poses (which are camera perspective center poses), but it's good practice for it to be aware of the authoritative source of such parameters if it were to, for example, output IMU poses. For now, ensure it correctly logs what it finds.

**Task:**
Ensure [`MVP/synchronize_hsi_webodm.py`](MVP/synchronize_hsi_webodm.py:1) correctly logs information about lever arms found in the HDR and acknowledges that the `config.toml` will be the primary source for georeferencing.

**Requirements:**
- When `parse_hdr_file` extracts lever arm data, log this clearly.
- Add a log message indicating that while HDR lever arm is noted, the georeferencing step will prioritize `config.toml` values.
- This prompt is mostly about ensuring clarity and consistency in how parameters are sourced and logged, rather than changing core logic of this script's output.

**Previous Issues:**
- Potential confusion if lever arm in HDR differs from config and it's not clear which one is used downstream.

**Expected Output:**
Updated logging in [`MVP/synchronize_hsi_webodm.py`](MVP/synchronize_hsi_webodm.py:1) regarding lever arm parameters.

---

### Prompt [LS1_SYNC_LOGGING]

**Context:**
[`MVP/synchronize_hsi_webodm.py`](MVP/synchronize_hsi_webodm.py:1) uses `print()` for output.

**Task:**
Replace `print()` statements with Python's `logging` module in [`MVP/synchronize_hsi_webodm.py`](MVP/synchronize_hsi_webodm.py:1).

**Requirements:**
- Import and configure the `logging` module.
- Use logger for all messages, including informational (e.g., "Loading HSI data", "Loaded X WebODM poses"), warnings (e.g., "Could not interpolate pose for HSI line Y"), and errors.
- Log summary information (e.g., "Successfully synchronized Z HSI lines").
- Control log level via `config.toml`.

**Previous Issues:**
- Relies on `print()` for messaging.

**Expected Output:**
[`MVP/synchronize_hsi_webodm.py`](MVP/synchronize_hsi_webodm.py:1) utilizing the `logging` module.

---

## D. RGB Geotiff Creation (`MVP/create_georeferenced_rgb.py`)

### Prompt [LS1_RGB_ACCURACY]

**Context:**
Recommendation III.14. [`MVP/create_georeferenced_rgb.py`](MVP/create_georeferenced_rgb.py:1) creates an RGB GeoTIFF. It must use the accurately georeferenced pixel coordinates from the enhanced `georeferenced_pixels.csv` output by the improved [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1).

**Task:**
Verify and ensure that [`MVP/create_georeferenced_rgb.py`](MVP/create_georeferenced_rgb.py:1) correctly reads and utilizes the `X_ground`, `Y_ground`, (and potentially `Z_ground`) columns from the `georeferenced_pixels.csv` file.

**Requirements:**
- The script already loads `georeferenced_pixels.csv`. Confirm that the columns `hsi_line_index`, `pixel_index`, `X_ground`, `Y_ground` are used for establishing the link between HSI pixel values and their ground coordinates.
- The resampling process (currently using KDTree for nearest neighbor) should map HSI spectral data to the target GeoTIFF grid based on these accurate coordinates.
- No specific code change might be needed if it already does this, but a thorough review is required.

**Previous Issues:**
- The accuracy of the output GeoTIFF depends entirely on the accuracy of the input `georeferenced_pixels.csv`. This prompt ensures the link is correctly utilized.

**Expected Output:**
Confirmation or minor adjustments to [`MVP/create_georeferenced_rgb.py`](MVP/create_georeferenced_rgb.py:1) to ensure it uses the precise georeferenced coordinates.

---

### Prompt [LS1_RGB_ORTHORECTIFICATION]

**Context:**
Recommendation III.14. If the output is intended to be an orthorectified image, the resampling must correctly account for terrain relief using the Z_ground values. The current nearest neighbor resampling might not be a full orthorectification if Z_ground varies significantly and isn't used to adjust pixel placement onto a regular grid.

**Task:**
Evaluate if the current resampling in [`MVP/create_georeferenced_rgb.py`](MVP/create_georeferenced_rgb.py:1) achieves orthorectification. If not, and if orthorectification is a goal, implement a more suitable resampling method.

**Requirements:**
- The current method uses KDTree on (X_ground, Y_ground) to find the nearest HSI pixel for each target grid cell. This is a form of georeferencing but might not be a true ortho if Z varies.
- For true orthorectification, each target grid cell's center (X, Y) on a flat map projection needs to be projected onto the DSM to find its Z_ground, then the corresponding HSI pixel viewing that (X,Y,Z_ground) point should be found. This is complex.
- **Simpler approach for now**: If `georeferenced_pixels.csv` contains accurate `X_ground, Y_ground, Z_ground` for each HSI pixel, the current resampling method is essentially "direct georeferencing to a grid".
- **Focus for this prompt**: Ensure the `Z_ground` from `georeferenced_pixels.csv` (if available and accurate from DSM intersection) is considered if a more sophisticated resampling than nearest neighbor (e.g., one that handles multiple source pixels contributing to a target pixel, or accounts for pixel footprint size changes due to relief) is implemented.
- For LS1, improving the accuracy of `X_ground, Y_ground` input is key. The current resampling might be sufficient if inputs are good. Add comments clarifying the nature of the current resampling (direct georeferencing to grid vs. full orthorectification).

**Previous Issues:**
- The term "georeferenced RGB" might imply orthorectification, which the current method might approximate but not fully achieve in variable terrain without explicit Z handling in resampling.

**Expected Output:**
- Clarifying comments in [`MVP/create_georeferenced_rgb.py`](MVP/create_georeferenced_rgb.py:1) about the resampling method.
- Potentially, if `Z_ground` is reliable, explore if it can slightly refine the nearest neighbor search or weighting if multiple points are close.

---

### Prompt [LS1_RGB_QUALITY_LAYERS]

**Context:**
Recommendation III.14 and Practical Application 3.105 suggest generating auxiliary quality layers, such as a map of estimated positional uncertainty per pixel.

**Task:**
Modify [`MVP/create_georeferenced_rgb.py`](MVP/create_georeferenced_rgb.py:1) to optionally generate an additional single-band GeoTIFF representing a quality metric, such as the estimated positional uncertainty.

**Requirements:**
- If the `georeferenced_pixels.csv` file includes uncertainty columns (e.g., `pos_uncertainty_m` from Prompt `LS1_GEO_ERROR_PROPAGATION`), this script should be able to read it.
- Resample this uncertainty value onto the target GeoTIFF grid using a similar method as for RGB bands (e.g., nearest neighbor, or average if multiple source pixels contribute).
- Save this as a separate single-band GeoTIFF file (e.g., `georeferenced_uncertainty_LS1.tif`).
- The output CRS and transform should match the RGB GeoTIFF.
- Add a configuration option in `config.toml` (e.g., `[parameters.rgb_geotiff_creation] generate_uncertainty_layer = true`).

**Previous Issues:**
- No quality layers are generated.

**Expected Output:**
- [`MVP/create_georeferenced_rgb.py`](MVP/create_georeferenced_rgb.py:1) capable of generating an uncertainty GeoTIFF.
- New configuration option in `config.toml`.

---

### Prompt [LS1_RGB_METADATA]

**Context:**
Recommendation III.14. Output GeoTIFFs should include comprehensive metadata.

**Task:**
Enhance [`MVP/create_georeferenced_rgb.py`](MVP/create_georeferenced_rgb.py:1) to write more comprehensive metadata to the output GeoTIFF files.

**Requirements:**
- When opening the GeoTIFF for writing using `rasterio`, include metadata tags such as:
    - `DESCRIPTION`: "Georeferenced RGB composite from HSI data."
    - `ACQUISITION_DATE`: If derivable from HSI filenames or metadata.
    - `PROCESSING_SOFTWARE`: "HSI Direct Georeferencing Tool vX.Y"
    - `SOURCE_HSI_FILE`: Base HSI filename.
    - `CONFIG_PARAMETERS_USED`: A summary or hash of key config parameters.
    - `RGB_BAND_WAVELENGTHS_NM`: e.g., "R:XXXnm, G:YYYnm, B:ZZZnm"
    - `GEOREFERENCING_METHOD`: "Direct georeferencing with Ray-DSM intersection."
- Refer to `rasterio` documentation for writing tags.

**Previous Issues:**
- Output GeoTIFF might have minimal metadata.

**Expected Output:**
[`MVP/create_georeferenced_rgb.py`](MVP/create_georeferenced_rgb.py:1) writing detailed metadata tags to the output GeoTIFF.

---

### Prompt [LS1_RGB_LOGGING]

**Context:**
[`MVP/create_georeferenced_rgb.py`](MVP/create_georeferenced_rgb.py:1) uses `print()` for output.

**Task:**
Replace `print()` statements with Python's `logging` module in [`MVP/create_georeferenced_rgb.py`](MVP/create_georeferenced_rgb.py:1).

**Requirements:**
- Import and configure the `logging` module.
- Use logger for all messages (informational, warnings, errors).
- Log key steps like "Loading HSI data", "Determining target raster dimensions", "Resampling progress", "Normalizing bands", "Saving GeoTIFF".
- Control log level via `config.toml`.

**Previous Issues:**
- Relies on `print()` for messaging.

**Expected Output:**
[`MVP/create_georeferenced_rgb.py`](MVP/create_georeferenced_rgb.py:1) utilizing the `logging` module.

---

## E. Main Pipeline & General

### Prompt [LS1_PIPELINE_ORCHESTRATION]

**Context:**
The [`MVP/main_pipeline.py`](MVP/main_pipeline.py:1) script orchestrates the execution of the different processing steps. As modules are refactored and their interfaces potentially change (e.g., to accept Pydantic config objects), the main pipeline needs to be updated.

**Task:**
Update [`MVP/main_pipeline.py`](MVP/main_pipeline.py:9-61) (`run_complete_pipeline` function) to correctly call and manage the refactored modules.

**Requirements:**
- Ensure that the `config_path` is correctly passed to each sub-module function (`run_consolidation`, `run_synchronization`, `run_georeferencing`, `run_create_rgb_geotiff`, `run_plotting`).
- If sub-modules now expect a parsed Pydantic config object instead of a path, the main pipeline should load and validate the config once (using Pydantic models from `LS1_CONFIG_VALIDATION`) and pass the config object.
- Verify that the sequence of operations remains logical.
- Update any assumptions about return values or side effects of the sub-modules if they change.

**Previous Issues:**
- The pipeline might break if sub-module interfaces change without corresponding updates here.

**Expected Output:**
An updated [`MVP/main_pipeline.py`](MVP/main_pipeline.py:1) that correctly orchestrates the enhanced processing modules, including proper configuration handling.

---

### Prompt [LS1_PIPELINE_ERROR_HANDLING]

**Context:**
The [`MVP/main_pipeline.py`](MVP/main_pipeline.py:1) currently checks the boolean return status of each step and aborts on critical failures or marks the pipeline as not fully successful on warnings. This error handling can be made more robust.

**Task:**
Improve error handling and reporting in the `run_complete_pipeline` function of [`MVP/main_pipeline.py`](MVP/main_pipeline.py:1).

**Requirements:**
- Use Python's `logging` module for all messages within the pipeline script itself.
- If a critical step fails (e.g., `run_consolidation`, `run_synchronization`, `run_georeferencing`), log a clear error message and ensure the pipeline terminates gracefully, returning `False`.
- For non-critical steps (e.g., `run_create_rgb_geotiff`, `run_plotting`), if they fail, log a warning, set `pipeline_successful = False`, but allow the pipeline to continue to subsequent non-dependent steps if appropriate.
- Consider adding `try-except` blocks around calls to sub-modules to catch unexpected exceptions from them, log these, and then decide whether to terminate or continue.

**Previous Issues:**
- Error reporting relies on `print` and basic boolean checks.

**Expected Output:**
Enhanced error handling and logging in [`MVP/main_pipeline.py`](MVP/main_pipeline.py:1).

---

### Prompt [LS1_TESTING_FRAMEWORK]

**Context:**
Recommendation III.13. A testing framework (`pytest`) should be developed for unit and integration tests.

**Task:**
Outline the structure for a testing framework using `pytest` and create initial (empty or very basic) test files for key modules.

**Requirements:**
- Create a `tests/` directory in the `MVP/` folder (or project root if preferred, adjust paths accordingly).
- Inside `tests/`, create initial test files, e.g.:
    - `test_config_loading.py` (for testing Pydantic config models and loading).
    - `test_sensor_model.py` (for unit testing the `SensorModel` class methods).
    - `test_georeference_pixels.py` (for testing parts of the georeferencing logic, e.g., ray-DSM intersection with mock DSM data).
    - `test_synchronization.py` (for testing interpolation logic).
    - `test_rgb_creation.py` (for testing band selection, normalization).
- Each test file should import `pytest` and have a placeholder for future tests (e.g., a simple `test_placeholder()` function that passes).
- Add a `pytest.ini` or `pyproject.toml` configuration for `pytest` if needed (e.g., to define source directories).
- This prompt is about setting up the structure, not writing comprehensive tests yet.

**Previous Issues:**
- No formal testing framework in place.

**Expected Output:**
- A `tests/` directory with initial `pytest` test files.
- Basic `pytest` configuration if necessary.

---

### Prompt [LS1_MODULARITY_UTILS]

**Context:**
As modules are refactored, common utility functions might emerge (e.g., coordinate transformations, logging setup, specific math operations). Recommendation III.10 promotes modularity.

**Task:**
Create a `utils.py` module within the `MVP/` directory and identify any existing or newly needed utility functions to place there.

**Requirements:**
- Create `MVP/utils.py`.
- Potential candidates for utility functions:
    - Centralized logging setup function `setup_logger(name, level)`.
    - Generic coordinate transformation functions if not part of `SensorModel` (e.g., ECEF to Geographic, if needed frequently outside sensor context).
    - Functions for robustly parsing specific data formats if used in multiple places.
    - Pydantic models for config validation (from `LS1_CONFIG_VALIDATION`) could reside here or in their own `config_models.py`.
- Refactor existing scripts to import and use these utility functions where appropriate.

**Previous Issues:**
- Utility-like code might be duplicated or tightly coupled within specific scripts.

**Expected Output:**
- A new `MVP/utils.py` module.
- Relevant utility functions moved or created there.
- Other scripts updated to use the new utility module.

---

### Prompt [LS1_DOCSTRINGS_COMMENTS]

**Context:**
Maintaining code quality requires good documentation. Recommendation III.10 (readability) and general best practices.

**Task:**
Ensure all new and significantly modified functions and classes across all Python scripts have comprehensive docstrings and clarifying comments.

**Requirements:**
- **Docstrings:**
    - For classes: Explain the purpose of the class, key attributes, and an overview of methods.
    - For functions/methods: Describe what the function does, its parameters (name, type, description), and what it returns (type, description). Follow a standard format (e.g., Google style, NumPy style).
- **Comments:**
    - Add inline comments to explain complex logic, assumptions, or non-obvious steps.
    - Update or remove outdated comments.
- This applies to all scripts: [`MVP/config.toml`](MVP/config.toml:1) (TOML comments), [`MVP/create_consolidated_webodm_poses.py`](MVP/create_consolidated_webodm_poses.py:1), [`MVP/synchronize_hsi_webodm.py`](MVP/synchronize_hsi_webodm.py:1), [`MVP/georeference_hsi_pixels.py`](MVP/georeference_hsi_pixels.py:1), [`MVP/create_georeferenced_rgb.py`](MVP/create_georeferenced_rgb.py:1), [`MVP/main_pipeline.py`](MVP/main_pipeline.py:1), [`MVP/plot_hsi_data.py`](MVP/plot_hsi_data.py:1), and any new modules like `utils.py` or `sensor_model.py`.

**Previous Issues:**
- Docstring and comment coverage may be inconsistent across the MVP scripts.

**Expected Output:**
All Python code updated with improved docstrings and comments. TOML configuration file also well-commented.

---

### Prompt [LS1_PLOTTING_ENHANCEMENTS]

**Context:**
The [`MVP/plot_hsi_data.py`](MVP/plot_hsi_data.py:1) script generates various plots for quality assessment. With the introduction of per-pixel uncertainty (Prompt `LS1_GEO_ERROR_PROPAGATION` and `LS1_RGB_QUALITY_LAYERS`), new visualizations would be beneficial.

**Task:**
Enhance [`MVP/plot_hsi_data.py`](MVP/plot_hsi_data.py:1) to visualize new quality metrics, particularly the estimated georeferencing uncertainty.

**Requirements:**
- Add a new plotting function, e.g., `plot_georeferencing_uncertainty(georeferenced_pixels_df, plot_output_dir)`.
- This function should read the uncertainty columns (e.g., `pos_uncertainty_m` or `X_uncertainty_m`, `Y_uncertainty_m`, `Z_uncertainty_m`) from the `georeferenced_pixels.csv` file.
- Create plots such as:
    - Histogram of uncertainty values.
    - Uncertainty values plotted over HSI line index or pixel index.
    - A 2D scatter plot of georeferenced points (X_ground, Y_ground) colored by their uncertainty magnitude.
- Update `run_plotting` in [`MVP/plot_hsi_data.py`](MVP/plot_hsi_data.py:108-167) to call this new function.
- Ensure the main pipeline ([`MVP/main_pipeline.py`](MVP/main_pipeline.py:1)) correctly calls the updated `run_plotting`.

**Previous Issues:**
- Plotting is limited to pose data and interpolation quality, not end-product georeferencing uncertainty.

**Expected Output:**
- Updated [`MVP/plot_hsi_data.py`](MVP/plot_hsi_data.py:1) with new functions to plot uncertainty metrics.
- Example plots generated if sample data with uncertainty is available.