"""
Unit tests for utility functions.

Tests all utility functions in the utils module.
"""

import pytest
import numpy as np
import tempfile
import logging
from pathlib import Path
from unittest.mock import patch

from ..utils import (
    setup_logger,
    CoordinateTransformer,
    create_rotation_matrix,
    normalize_vector,
    validate_file_exists,
    validate_directory_exists,
    safe_divide,
    calculate_distance_3d,
    interpolate_timestamps
)


class TestSetupLogger:
    """Test setup_logger function."""
    
    def test_setup_logger_basic(self):
        """Test basic logger setup."""
        logger = setup_logger("test_logger", "INFO")
        
        assert logger.name == "test_logger"
        assert logger.level == logging.INFO
        assert len(logger.handlers) > 0
    
    def test_setup_logger_with_file(self):
        """Test logger setup with file output."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "test.log"
            logger = setup_logger("test_logger_file", "DEBUG", log_file)
            
            assert logger.level == logging.DEBUG
            assert len(logger.handlers) >= 2  # Console + file handlers
            assert log_file.exists()
    
    def test_setup_logger_existing_logger(self):
        """Test that existing logger is returned unchanged."""
        logger1 = setup_logger("existing_logger", "INFO")
        logger2 = setup_logger("existing_logger", "DEBUG")
        
        # Should return the same logger instance
        assert logger1 is logger2
        assert logger1.level == logging.INFO  # Level should not change


class TestCoordinateTransformer:
    """Test CoordinateTransformer class."""
    
    def test_coordinate_transformer_initialization(self):
        """Test CoordinateTransformer initialization."""
        transformer = CoordinateTransformer(32632)
        
        assert transformer.output_epsg == 32632
        assert transformer.output_crs is not None
        assert transformer.geographic_crs is not None
        assert transformer.ecef_crs is not None
    
    def test_geographic_to_output_transformation(self):
        """Test geographic to output coordinate transformation."""
        transformer = CoordinateTransformer(32632)  # UTM Zone 32N
        
        # Test point in Germany (should be in UTM Zone 32N)
        lon, lat, height = 9.0, 51.0, 100.0
        x, y, z = transformer.geographic_to_output(lon, lat, height)
        
        # UTM coordinates should be reasonable for this location
        assert 400000 < x < 600000  # Typical UTM easting for this longitude
        assert 5600000 < y < 5700000  # Typical UTM northing for this latitude
        assert abs(z - height) < 1.0  # Height should be preserved approximately
    
    def test_output_to_geographic_transformation(self):
        """Test output to geographic coordinate transformation."""
        transformer = CoordinateTransformer(32632)
        
        # Test UTM coordinates
        x, y, z = 500000.0, 5650000.0, 100.0
        lon, lat, height = transformer.output_to_geographic(x, y, z)
        
        # Should be reasonable geographic coordinates
        assert 8.0 < lon < 10.0  # Longitude in Germany
        assert 50.0 < lat < 52.0  # Latitude in Germany
        assert abs(height - z) < 1.0  # Height should be preserved approximately
    
    def test_coordinate_transformation_roundtrip(self):
        """Test roundtrip coordinate transformation."""
        transformer = CoordinateTransformer(32632)
        
        # Original geographic coordinates
        original_lon, original_lat, original_height = 9.0, 51.0, 100.0
        
        # Transform to output and back
        x, y, z = transformer.geographic_to_output(original_lon, original_lat, original_height)
        back_lon, back_lat, back_height = transformer.output_to_geographic(x, y, z)
        
        # Should be very close to original
        assert abs(back_lon - original_lon) < 1e-6
        assert abs(back_lat - original_lat) < 1e-6
        assert abs(back_height - original_height) < 1e-3


class TestRotationMatrix:
    """Test create_rotation_matrix function."""
    
    def test_create_rotation_matrix_identity(self):
        """Test rotation matrix creation with zero angles."""
        R = create_rotation_matrix(0.0, 0.0, 0.0)
        
        # Should be identity matrix
        np.testing.assert_array_almost_equal(R, np.eye(3))
    
    def test_create_rotation_matrix_single_axis(self):
        """Test rotation matrix creation for single axis rotations."""
        # 90-degree rotation around Z-axis
        R_z = create_rotation_matrix(0.0, 0.0, 90.0)
        
        # Test that X-axis rotates to Y-axis
        x_axis = np.array([1.0, 0.0, 0.0])
        rotated_x = R_z @ x_axis
        expected_y = np.array([0.0, 1.0, 0.0])
        
        np.testing.assert_array_almost_equal(rotated_x, expected_y, decimal=10)
    
    def test_create_rotation_matrix_properties(self):
        """Test rotation matrix properties."""
        R = create_rotation_matrix(30.0, 45.0, 60.0)
        
        # Should be orthogonal (R @ R.T = I)
        np.testing.assert_array_almost_equal(R @ R.T, np.eye(3), decimal=10)
        
        # Should have determinant = 1
        assert abs(np.linalg.det(R) - 1.0) < 1e-10
    
    def test_create_rotation_matrix_different_orders(self):
        """Test rotation matrix with different rotation orders."""
        R_zyx = create_rotation_matrix(10.0, 20.0, 30.0, 'ZYX')
        R_xyz = create_rotation_matrix(10.0, 20.0, 30.0, 'XYZ')
        
        # Different orders should give different results
        assert not np.allclose(R_zyx, R_xyz)


class TestVectorOperations:
    """Test vector operation functions."""
    
    def test_normalize_vector_unit_vector(self):
        """Test normalizing a unit vector."""
        vector = np.array([1.0, 0.0, 0.0])
        normalized = normalize_vector(vector)
        
        np.testing.assert_array_almost_equal(normalized, vector)
        assert abs(np.linalg.norm(normalized) - 1.0) < 1e-10
    
    def test_normalize_vector_arbitrary_vector(self):
        """Test normalizing an arbitrary vector."""
        vector = np.array([3.0, 4.0, 0.0])
        normalized = normalize_vector(vector)
        
        expected = np.array([0.6, 0.8, 0.0])
        np.testing.assert_array_almost_equal(normalized, expected)
        assert abs(np.linalg.norm(normalized) - 1.0) < 1e-10
    
    def test_normalize_vector_zero_vector(self):
        """Test normalizing a zero vector."""
        vector = np.array([0.0, 0.0, 0.0])
        normalized = normalize_vector(vector)
        
        # Should return the zero vector unchanged
        np.testing.assert_array_equal(normalized, vector)
    
    def test_calculate_distance_3d(self):
        """Test 3D distance calculation."""
        point1 = np.array([0.0, 0.0, 0.0])
        point2 = np.array([3.0, 4.0, 0.0])
        
        distance = calculate_distance_3d(point1, point2)
        
        assert abs(distance - 5.0) < 1e-10  # 3-4-5 triangle
    
    def test_calculate_distance_3d_same_points(self):
        """Test 3D distance calculation for same points."""
        point = np.array([1.0, 2.0, 3.0])
        
        distance = calculate_distance_3d(point, point)
        
        assert abs(distance) < 1e-10


class TestFileValidation:
    """Test file and directory validation functions."""
    
    def test_validate_file_exists_valid_file(self):
        """Test file validation with existing file."""
        with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
            tmp_path = Path(tmp_file.name)
            tmp_file.write(b"test content")
        
        try:
            result = validate_file_exists(tmp_path, "Test file")
            assert result is True
        finally:
            tmp_path.unlink(missing_ok=True)
    
    def test_validate_file_exists_missing_file(self):
        """Test file validation with missing file."""
        missing_file = Path("nonexistent_file.txt")
        
        result = validate_file_exists(missing_file, "Missing file")
        
        assert result is False
    
    def test_validate_directory_exists_valid_directory(self):
        """Test directory validation with existing directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            result = validate_directory_exists(temp_dir, "Test directory")
            assert result is True
    
    def test_validate_directory_exists_missing_directory_create(self):
        """Test directory validation with creation of missing directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            missing_dir = Path(temp_dir) / "new_directory"
            
            result = validate_directory_exists(missing_dir, "New directory", create_if_missing=True)
            
            assert result is True
            assert missing_dir.exists()
            assert missing_dir.is_dir()
    
    def test_validate_directory_exists_missing_directory_no_create(self):
        """Test directory validation without creation of missing directory."""
        missing_dir = Path("nonexistent_directory")
        
        result = validate_directory_exists(missing_dir, "Missing directory", create_if_missing=False)
        
        assert result is False


class TestMathUtilities:
    """Test mathematical utility functions."""
    
    def test_safe_divide_normal_division(self):
        """Test safe division with normal values."""
        result = safe_divide(10.0, 2.0)
        assert result == 5.0
    
    def test_safe_divide_division_by_zero(self):
        """Test safe division by zero."""
        result = safe_divide(10.0, 0.0, default=99.0)
        assert result == 99.0
    
    def test_safe_divide_array_input(self):
        """Test safe division with array inputs."""
        numerator = np.array([10.0, 20.0, 30.0])
        denominator = np.array([2.0, 0.0, 5.0])
        
        result = safe_divide(numerator, denominator, default=-1.0)
        
        expected = np.array([5.0, -1.0, 6.0])
        np.testing.assert_array_equal(result, expected)
    
    def test_interpolate_timestamps_within_range(self):
        """Test timestamp interpolation within range."""
        times = np.array([1.0, 2.0, 3.0, 4.0])
        values = np.array([10.0, 20.0, 30.0, 40.0])
        
        # Interpolate at t=2.5 (between 2.0 and 3.0)
        result = interpolate_timestamps(2.5, times, values)
        
        assert result == 25.0  # Linear interpolation: 20 + 0.5 * (30 - 20)
    
    def test_interpolate_timestamps_at_boundary(self):
        """Test timestamp interpolation at boundaries."""
        times = np.array([1.0, 2.0, 3.0])
        values = np.array([10.0, 20.0, 30.0])
        
        # At exact timestamp
        result = interpolate_timestamps(2.0, times, values)
        assert result == 20.0
        
        # At first timestamp
        result = interpolate_timestamps(1.0, times, values)
        assert result == 10.0
        
        # At last timestamp
        result = interpolate_timestamps(3.0, times, values)
        assert result == 30.0
    
    def test_interpolate_timestamps_outside_range(self):
        """Test timestamp interpolation outside range."""
        times = np.array([1.0, 2.0, 3.0])
        values = np.array([10.0, 20.0, 30.0])
        
        # Before range
        result = interpolate_timestamps(0.5, times, values)
        assert result is None
        
        # After range
        result = interpolate_timestamps(3.5, times, values)
        assert result is None
    
    def test_interpolate_timestamps_vector_values(self):
        """Test timestamp interpolation with vector values."""
        times = np.array([1.0, 2.0, 3.0])
        values = np.array([[1.0, 2.0], [3.0, 4.0], [5.0, 6.0]])
        
        result = interpolate_timestamps(2.5, times, values)
        
        expected = np.array([4.0, 5.0])  # Linear interpolation of vectors
        np.testing.assert_array_equal(result, expected)


def test_placeholder():
    """Placeholder test to ensure pytest runs."""
    assert True
