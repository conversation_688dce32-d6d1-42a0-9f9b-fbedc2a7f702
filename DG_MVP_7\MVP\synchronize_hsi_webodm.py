import json
import numpy as np
import pandas as pd
from scipy.spatial.transform import Rotation, Slerp
# from datetime import datetime, timezone # Not explicitly used after changes
import os # Keep for os.path.join if still needed, but prefer Path
import toml
import logging
from pathlib import Path
from typing import Optional, List, Dict, Tuple, Union
from pydantic import ValidationError

# Assuming config_models.py is in the same directory (MVP)
try:
    from config_models import Config
except ImportError: # Fallback for direct execution if <PERSON> is not in PYTHONPATH
    from .config_models import Config

logger = logging.getLogger(__name__)

# --- Helper Functions ---

def parse_hdr_file(hdr_file_path: Path) -> Optional[Dict[str, Any]]:
    """
    Parses the ENVI header file to extract metadata.
    Specifically looks for 'lines' and 'OffsetBetweenMainAntennaAndTargetPoint'.
    """
    metadata: Dict[str, Any] = {}
    try:
        with open(hdr_file_path, "r", encoding='utf-8') as f: # Added encoding
            for line in f:
                line = line.strip()
                if "=" in line:
                    key_value = line.split("=", 1)
                    key = key_value[0].strip()
                    value = key_value[1].strip().replace("{", "").replace("}", "")
                    if key == "lines":
                        metadata["lines"] = int(value)
                    elif key == "OffsetBetweenMainAntennaAndTargetPoint (x,y,z)":
                        try:
                            coords_str = (
                                value.replace("(", "").replace(")", "").split(",")
                            )
                            metadata["lever_arm_xyz_mm"] = [
                                int(c.strip()) for c in coords_str
                            ]
                        except ValueError:
                            logger.warning(f"Could not parse lever arm: {value} in {hdr_file_path}")
                            metadata["lever_arm_xyz_mm"] = None
    except FileNotFoundError:
        logger.error(f"Header file not found: {hdr_file_path}")
        return None
    except Exception as e:
        logger.error(f"Error parsing header file {hdr_file_path}: {e}")
        return None
    if "lines" not in metadata:
        logger.warning(f"'lines' not found in {hdr_file_path}")
    return metadata


def convert_hsi_timestamp_to_ns(timestamp_str: str) -> int:
    """
    Converts HSI timestamps (assumed to be in microseconds) to nanoseconds.
    """
    return int(timestamp_str) * 1000  # HSI sync file timestamps are in microseconds


def load_hsi_data(sync_file_path: Path, total_lines_from_hdr: int) -> List[Tuple[int, int]]:
    """
    Loads HSI synchronization data.
    Timestamps are converted to nanoseconds.
    Frame/Line numbers are 1-based and in reverse order in the file.
    Corrects line numbers to be 0-based and in acquisition order.
    Returns a list of tuples: (corrected_hsi_line_index, hsi_timestamp_ns)
    sorted by timestamp.
    """
    hsi_data: List[Dict[str, int]] = []
    try:
        with open(sync_file_path, "r", encoding='utf-8') as f: # Added encoding
            lines = f.readlines()
            if not lines or "Frame/Line" not in lines[0]:
                logger.error(f"Invalid HSI sync file format: {sync_file_path}. Header missing or incorrect.")
                return []

            for line_idx, line_content in enumerate(lines[1:], start=2): # Skip header
                parts = line_content.strip().split()
                if len(parts) == 2:
                    try:
                        original_line_num = int(parts[0])
                        timestamp_ns = convert_hsi_timestamp_to_ns(parts[1])
                        hsi_data.append(
                            {
                                "original_line_num": original_line_num,
                                "timestamp_ns": timestamp_ns,
                            }
                        )
                    except ValueError:
                        logger.warning(
                            f"Skipping invalid line (ValueError) in HSI sync file {sync_file_path} at line {line_idx}: {line_content.strip()}"
                        )
                        continue
                else:
                    logger.warning(
                        f"Skipping malformed line in HSI sync file {sync_file_path} at line {line_idx}: {line_content.strip()}"
                    )

        if not hsi_data:
            logger.warning(f"No data parsed from HSI sync file: {sync_file_path}")
            return []

        hsi_data.sort(key=lambda x: x["timestamp_ns"])

        processed_hsi_data: List[Tuple[int, int]] = []
        for i, record in enumerate(hsi_data):
            corrected_hsi_line_index = i # 0-based index after sorting by timestamp
            if corrected_hsi_line_index >= total_lines_from_hdr:
                logger.warning(
                    f"Corrected HSI line index {corrected_hsi_line_index} (from original line {record['original_line_num']}) "
                    f"exceeds total lines from HDR ({total_lines_from_hdr-1}). This might indicate a mismatch."
                )
            processed_hsi_data.append(
                (corrected_hsi_line_index, record["timestamp_ns"])
            )

        if len(processed_hsi_data) != total_lines_from_hdr:
            logger.warning(
                f"Number of lines in sync file ({len(processed_hsi_data)}) does not match 'lines' in HDR ({total_lines_from_hdr})."
            )
        return processed_hsi_data

    except FileNotFoundError:
        logger.error(f"HSI sync file not found: {sync_file_path}")
        return []
    except Exception as e:
        logger.error(f"Error loading HSI data from {sync_file_path}: {e}", exc_info=True)
        return []


def load_webodm_data(webodm_csv_path: Path) -> List[Dict[str, Any]]:
    """
    Loads WebODM pose data from the consolidated CSV file.
    Extracts timestamp, translation, and rotation.
    Timestamp ('haip_timestamp_ns') is already in nanoseconds.
    Returns a list of dictionaries: {'timestamp_ns': ..., 'translation': ..., 'rotation_vec': ...}
    sorted by timestamp.
    """
    webodm_poses: List[Dict[str, Any]] = []
    try:
        df = pd.read_csv(webodm_csv_path)
        required_cols = [
            "haip_timestamp_ns",
            "pos_x", "pos_y", "pos_z",
            "rot_1", "rot_2", "rot_3",
        ]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            logger.error(
                f"Missing required columns in {webodm_csv_path}: {', '.join(missing_cols)}"
            )
            return []

        df = df.sort_values(by="haip_timestamp_ns").reset_index(drop=True) # Ensure sorted and clean index

        for index, row in df.iterrows():
            try:
                timestamp_ns = int(row["haip_timestamp_ns"])
                translation = np.array([float(row["pos_x"]), float(row["pos_y"]), float(row["pos_z"])])
                rotation_vec = np.array([float(row["rot_1"]), float(row["rot_2"]), float(row["rot_3"])])
                webodm_poses.append(
                    {
                        "timestamp_ns": timestamp_ns,
                        "translation": translation,
                        "rotation_vec": rotation_vec,
                        "original_filename": row.get("image_filename", "N/A"),
                    }
                )
            except ValueError as ve:
                logger.warning(
                    f"Skipping row {index} in {webodm_csv_path} due to data conversion error: {ve}. Row data: {row.to_dict()}"
                )
                continue
            except Exception as e: # Catch any other unexpected error for this row
                logger.warning(
                    f"Skipping row {index} in {webodm_csv_path} due to unexpected error: {e}. Row data: {row.to_dict()}"
                )
                continue
        return webodm_poses

    except FileNotFoundError:
        logger.error(f"WebODM CSV file not found: {webodm_csv_path}")
        return []
    except pd.errors.EmptyDataError:
        logger.error(f"WebODM CSV file is empty: {webodm_csv_path}")
        return []
    except Exception as e:
        logger.error(f"Error loading WebODM data from {webodm_csv_path}: {e}", exc_info=True)
        return []


def interpolate_pose(hsi_timestamp_ns: int, webodm_poses: List[Dict[str, Any]]) -> Tuple[Optional[np.ndarray], Optional[Rotation], float, float]:
    """
    Interpolates camera pose (translation and rotation) for a given HSI timestamp.
    Linear interpolation for translation, SLERP for rotation.
    Calculates the absolute time differences to the previous (ts_before) and next (ts_after) WebODM timestamps.
    Handles edge cases where HSI timestamp is outside WebODM timestamp range.
    Returns: (translation, rotation_object, delta_t_prev_ns, delta_t_next_ns)
    """
    if not webodm_poses:
        logger.warning("interpolate_pose called with empty webodm_poses list.")
        return None, None, np.nan, np.nan

    p_before: Optional[Dict[str, Any]] = None
    p_after: Optional[Dict[str, Any]] = None

    # Find bracketing poses
    for pose in webodm_poses: # Assumes webodm_poses is sorted by timestamp
        if pose["timestamp_ns"] <= hsi_timestamp_ns:
            p_before = pose
        else:
            p_after = pose
            break
    
    # Handle edge cases: HSI timestamp is before the first WebODM pose
    if p_before is None:
        logger.debug(f"HSI timestamp {hsi_timestamp_ns} is before the first WebODM pose. Using first pose.")
        first_pose = webodm_poses[0]
        ts_first_webodm = first_pose["timestamp_ns"]
        delta_t_prev_ns = np.nan
        delta_t_next_ns = float(abs(hsi_timestamp_ns - ts_first_webodm))
        try:
            return (
                first_pose["translation"],
                Rotation.from_rotvec(first_pose["rotation_vec"]),
                delta_t_prev_ns,
                delta_t_next_ns,
            )
        except ValueError as e:
            logger.error(f"Error creating Rotation from first pose's rot_vec {first_pose['rotation_vec']}: {e}")
            return None, None, delta_t_prev_ns, delta_t_next_ns


    # Handle edge cases: HSI timestamp is after the last WebODM pose
    if p_after is None:
        logger.debug(f"HSI timestamp {hsi_timestamp_ns} is after the last WebODM pose. Using last pose.")
        last_pose = webodm_poses[-1]
        ts_last_webodm = last_pose["timestamp_ns"]
        delta_t_prev_ns = float(abs(hsi_timestamp_ns - ts_last_webodm))
        delta_t_next_ns = np.nan
        try:
            return (
                last_pose["translation"],
                Rotation.from_rotvec(last_pose["rotation_vec"]),
                delta_t_prev_ns,
                delta_t_next_ns,
            )
        except ValueError as e:
            logger.error(f"Error creating Rotation from last pose's rot_vec {last_pose['rotation_vec']}: {e}")
            return None, None, delta_t_prev_ns, delta_t_next_ns

    ts_b = p_before["timestamp_ns"]
    ts_a = p_after["timestamp_ns"]
    delta_t_prev_ns = float(abs(hsi_timestamp_ns - ts_b))
    delta_t_next_ns = float(abs(hsi_timestamp_ns - ts_a))

    # If timestamps are identical, or HSI timestamp matches one of the poses
    if ts_b == ts_a or ts_b == hsi_timestamp_ns:
        logger.debug(f"HSI timestamp {hsi_timestamp_ns} matches WebODM pose at {ts_b}. Using p_before.")
        try:
            return (
                p_before["translation"],
                Rotation.from_rotvec(p_before["rotation_vec"]),
                delta_t_prev_ns,
                delta_t_next_ns,
            )
        except ValueError as e:
            logger.error(f"Error creating Rotation from p_before's rot_vec {p_before['rotation_vec']}: {e}")
            return None, None, delta_t_prev_ns, delta_t_next_ns
    
    if ts_a == hsi_timestamp_ns: # Should be caught by p_before logic if sorted, but as a safeguard
        logger.debug(f"HSI timestamp {hsi_timestamp_ns} matches WebODM pose at {ts_a}. Using p_after.")
        try:
            return (
                p_after["translation"],
                Rotation.from_rotvec(p_after["rotation_vec"]),
                delta_t_prev_ns,
                delta_t_next_ns,
            )
        except ValueError as e:
            logger.error(f"Error creating Rotation from p_after's rot_vec {p_after['rotation_vec']}: {e}")
            return None, None, delta_t_prev_ns, delta_t_next_ns

    # Interpolation
    t_diff_total = float(ts_a - ts_b)
    if t_diff_total == 0: # Should have been caught by ts_b == ts_a, but defensive
        logger.warning(f"Zero time difference between bracketing poses for HSI ts {hsi_timestamp_ns}. Using p_before.")
        try:
            return p_before["translation"], Rotation.from_rotvec(p_before["rotation_vec"]), delta_t_prev_ns, delta_t_next_ns
        except ValueError as e:
            logger.error(f"Error creating Rotation from p_before's rot_vec {p_before['rotation_vec']} (zero diff case): {e}")
            return None, None, delta_t_prev_ns, delta_t_next_ns

    alpha = (hsi_timestamp_ns - ts_b) / t_diff_total
    interp_translation = (1 - alpha) * p_before["translation"] + alpha * p_after["translation"]

    try:
        rot_before = Rotation.from_rotvec(p_before["rotation_vec"])
        rot_after = Rotation.from_rotvec(p_after["rotation_vec"])
        
        # Ensure rotations are not too close, which can cause Slerp issues with some libraries/versions
        # Scipy's Slerp seems robust, but this is a common check.
        # If they are nearly identical, just pick one.
        if np.allclose(rot_before.as_quat(), rot_after.as_quat()):
            interp_rotation = rot_before
        else:
            key_rots = Rotation.concatenate([rot_before, rot_after])
            key_times = [0, 1] # Slerp expects normalized times [0,1] for the interval
            slerp_interpolator = Slerp(key_times, key_rots)
            interp_rotation = slerp_interpolator([alpha])[0]
            
    except ValueError as e:
        logger.error(f"Error converting rotation vector for SLERP: {e}. "
                     f"p_before rot_vec: {p_before.get('rotation_vec')}, p_after rot_vec: {p_after.get('rotation_vec')}. "
                     f"Falling back to nearest rotation.")
        # Fallback: use the rotation of the closer WebODM pose
        interp_rotation = rot_before if alpha < 0.5 else rot_after # type: ignore

    return interp_translation, interp_rotation, delta_t_prev_ns, delta_t_next_ns

def load_config_for_sync(config_path_str: str) -> Optional[Config]:
    """Loads and validates the TOML configuration file for synchronization script."""
    config_path = Path(config_path_str)
    if not config_path.is_absolute():
        base_path = Path(__file__).parent
        config_path = base_path / config_path_str
        logger.info(f"Relative config path '{config_path_str}' resolved to '{config_path.resolve()}' for sync script.")
    try:
        logger.info(f"Attempting to load configuration from: {config_path.resolve()}")
        config_data = toml.load(config_path)
        validated_config = Config(**config_data)
        logger.info("Configuration loaded and validated successfully for sync.")
        
        log_level_str = validated_config.processing_options.log_level
        numeric_level = getattr(logging, log_level_str.upper(), logging.INFO)
        if not logging.getLogger().handlers:
            logging.basicConfig(level=numeric_level, format='%(asctime)s - %(levelname)s - %(module)s - %(message)s')
        else:
            logging.getLogger().setLevel(numeric_level)
        logger.setLevel(numeric_level)
        logger.info(f"Logging level set to: {log_level_str} for sync script.")
        return validated_config
    except FileNotFoundError:
        logger.error(f"Configuration file not found at {config_path.resolve()}")
        return None
    except ValidationError as e:
        logger.error(f"Configuration validation error: {e}")
        return None
    except Exception as e:
        logger.error(f"An unexpected error occurred while loading or validating the configuration: {e}")
        return None

def run_synchronization(config_or_path: Union[str, Config]) -> bool:
    """
    Main processing logic for HSI and WebODM data synchronization.
    Reads configuration, loads data, interpolates poses, and saves results.
    Returns True if successful, False otherwise.
    """
    if isinstance(config_or_path, str):
        config = load_config_for_sync(config_or_path)
        if config is None:
            return False
    elif isinstance(config_or_path, Config):
        config = config_or_path
        if not logging.getLogger().handlers:
             logging.basicConfig(level=config.processing_options.log_level.upper(), format='%(asctime)s - %(levelname)s - %(module)s - %(message)s')
        logger.setLevel(config.processing_options.log_level.upper())
    else:
        logger.error("Invalid argument type for config_or_path. Must be a path string or Config object.")
        return False

    logger.info(f"Starting HSI-WebODM synchronization with config from: {config.paths.hsi_data_directory}") # Example of using validated config

    # --- Path Definitions from Configuration ---
    base_hsi_data_dir = Path(config.paths.hsi_data_directory)
    hsi_base_filename = config.paths.hsi_base_filename
    # Assuming _sync.txt is derived by removing _cont part if present
    hsi_sync_filename_stem = hsi_base_filename.replace("_cont", "") if "_cont" in hsi_base_filename else hsi_base_filename
    hsi_sync_file = base_hsi_data_dir / f"{hsi_sync_filename_stem}_sync.txt"
    hsi_hdr_file = base_hsi_data_dir / f"{hsi_base_filename}.hdr"

    base_output_dir = Path(config.paths.output_directory)
    consolidated_poses_filename = config.paths.consolidated_webodm_poses_csv
    webodm_csv_file = base_output_dir / consolidated_poses_filename
    hsi_poses_output_filename = config.paths.hsi_poses_csv
    output_csv_file = base_output_dir / hsi_poses_output_filename
    
    # Ensure output directory from config exists
    try:
        base_output_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Ensured output directory exists: {base_output_dir.resolve()}")
    except OSError as e:
        logger.error(f"Error creating output directory {base_output_dir.resolve()}: {e}")
        return False

    # 1. Load HSI Metadata
    logger.info(f"Loading HSI header file: {hsi_hdr_file}")
    hsi_metadata = parse_hdr_file(hsi_hdr_file)
    if hsi_metadata is None or "lines" not in hsi_metadata:
        logger.error("Could not load essential HSI metadata (number of lines). Aborting synchronization.")
        return False
    total_hsi_lines = hsi_metadata["lines"]
    # Lever arm from HDR is informational here, primary source should be config.toml for georeferencing
    lever_arm_mm = hsi_metadata.get("lever_arm_xyz_mm")
    logger.info(f"Total HSI lines from HDR: {total_hsi_lines}")
    if lever_arm_mm:
        logger.info(f"Lever arm (x,y,z) in mm from HDR: {lever_arm_mm} (Informational, config.toml is primary source for georef)")
    else:
        logger.info("Lever arm information not found or not parsed from HDR.")

    # 2. Load HSI Synchronization Data
    logger.info(f"Loading HSI sync data from: {hsi_sync_file}")
    hsi_sync_data = load_hsi_data(hsi_sync_file, total_hsi_lines)
    if not hsi_sync_data:
        logger.error("No HSI sync data loaded. Aborting synchronization.")
        return False
    logger.info(f"Loaded {len(hsi_sync_data)} HSI linescans with timestamps.")

    # 3. Load WebODM Pose Data
    logger.info(f"Loading WebODM pose data from: {webodm_csv_file}")
    webodm_poses = load_webodm_data(webodm_csv_file)
    if not webodm_poses:
        logger.error("No WebODM pose data loaded. Aborting synchronization.")
        return False
    logger.info(f"Loaded {len(webodm_poses)} WebODM poses.")

    # 4. Synchronize and Interpolate
    logger.info("Synchronizing HSI linescans with WebODM poses...")
    output_data = []
    for hsi_line_index, hsi_ts_ns in hsi_sync_data:
        (
            interp_translation,
            interp_rotation_obj,
            time_diff_prev_ns,
            time_diff_next_ns,
        ) = interpolate_pose(hsi_ts_ns, webodm_poses)

        if interp_translation is not None and interp_rotation_obj is not None:
            quat = interp_rotation_obj.as_quat()  # [x, y, z, w]
            time_diff_prev_ms = time_diff_prev_ns / 1_000_000.0 if not np.isnan(time_diff_prev_ns) else np.nan
            time_diff_next_ms = time_diff_next_ns / 1_000_000.0 if not np.isnan(time_diff_next_ns) else np.nan
            output_data.append(
                {
                    "hsi_line_index": hsi_line_index,
                    "hsi_timestamp_ns": hsi_ts_ns,
                    "pos_x": interp_translation[0],
                    "pos_y": interp_translation[1],
                    "pos_z": interp_translation[2],
                    "quat_x": quat[0],
                    "quat_y": quat[1],
                    "quat_z": quat[2],
                    "quat_w": quat[3],
                    "time_diff_to_prev_ms": time_diff_prev_ms,
                    "time_diff_to_next_ms": time_diff_next_ms,
                }
            )
        else:
            logger.warning(
                f"Could not interpolate pose for HSI line {hsi_line_index} (timestamp {hsi_ts_ns})"
            )

    # 5. Save Output
    if not output_data:
        logger.warning("No data to save after synchronization. Output file will not be created.")
        return True # Considered success if no errors, but no data to output.

    df_output = pd.DataFrame(output_data)
    try:
        df_output.to_csv(output_csv_file, index=False)
        logger.info(f"Successfully synchronized and saved HSI poses to {output_csv_file}")
    except Exception as e:
        logger.error(f"Error saving output CSV to {output_csv_file}: {e}", exc_info=True)
        return False

    logger.info("HSI-WebODM synchronization script finished successfully.")
    return True


if __name__ == "__main__":
    # Setup basic logging for standalone execution
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(module)s - %(message)s')

    DEFAULT_CONFIG_PATH = 'config.toml' # Assumes it's in the same MVP directory
    logger.info(f"Running HSI Pose Synchronization with configuration: {DEFAULT_CONFIG_PATH}")
    
    success = run_synchronization(config_or_path=DEFAULT_CONFIG_PATH)
    
    if success:
        logger.info("HSI Pose Synchronization completed successfully.")
    else:
        logger.error("Error during HSI Pose Synchronization.")
