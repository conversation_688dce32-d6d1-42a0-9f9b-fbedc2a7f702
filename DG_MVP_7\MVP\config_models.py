from typing import List, Optional, Union
from pydantic import BaseModel, FilePath, DirectoryPath, validator, conlist

class PathsConfig(BaseModel):
    hsi_data_directory: DirectoryPath
    webodm_data_directory: DirectoryPath
    output_directory: DirectoryPath
    plot_output_directory: str # Relative to output_directory, will be joined later
    hsi_base_filename: str
    sensor_model_file: str # Relative to hsi_data_directory
    shots_geojson_file: str # Relative to webodm_data_directory
    haip_files_subdirectory: str # Relative to webodm_data_directory
    dsm_file: FilePath # This is now superseded by dsm_parameters.path but kept for now if old scripts use it
    consolidated_webodm_poses_csv: str
    hsi_poses_csv: str
    georeferenced_pixels_csv: str
    georeferenced_rgb_tif: str

    @validator('sensor_model_file', 'shots_geojson_file', 'haip_files_subdirectory', 'plot_output_directory', pre=True, always=True)
    def ensure_not_absolute(cls, v, field):
        if isinstance(v, str) and (v.startswith('/') or v.startswith('\\') or ':' in v):
            # A basic check for absolute paths, might need refinement based on OS
            # For plot_output_directory, it's a relative path string, not a DirectoryPath yet
            if field.name != 'plot_output_directory' and not Path(v).is_absolute(): # type: ignore
                 pass # allow relative paths
            elif field.name == 'plot_output_directory': # plot_output_directory is just a string
                pass
            else:
                raise ValueError(f"{field.name} must be a relative path string")
        return v

class InteriorOrientationConfig(BaseModel):
    focal_length_mm: float
    pixel_size_um: float
    principal_point_x_mm: float
    principal_point_y_mm: float
    k1: float
    k2: float
    k3: float
    p1: float
    p2: float

class BoresightAlignmentDegConfig(BaseModel):
    roll_offset_deg: float
    pitch_offset_deg: float
    yaw_offset_deg: float

class LeverArmsMetersConfig(BaseModel):
    gnss_to_imu_x_m: float
    gnss_to_imu_y_m: float
    gnss_to_imu_z_m: float
    imu_to_sensor_x_m: float
    imu_to_sensor_y_m: float
    imu_to_sensor_z_m: float

class SensorModelConfig(BaseModel):
    interior_orientation: InteriorOrientationConfig
    boresight_alignment_deg: BoresightAlignmentDegConfig
    lever_arms_meters: LeverArmsMetersConfig
    # Optional: Add a section for uncertainties if implementing LS1_GEO_ERROR_PROPAGATION
    # uncertainties: Optional[SensorUncertaintiesConfig] = None 

class WebODMConsolidationParams(BaseModel):
    haip_timestamp_key: str

class DSMParametersConfig(BaseModel):
    path: FilePath
    type: str # e.g., "raster", "tin"
    default_vertical_uncertainty_m: Optional[float] = None
    nodata_value: Optional[Union[float, int]] = None # Can be float or int
    ray_dsm_max_search_dist_m: float
    ray_dsm_step_m: float
    ray_dsm_bisection_tolerance_m: float

    @validator('type')
    def type_must_be_valid(cls, value):
        if value not in ["raster", "tin"]:
            raise ValueError('DSM type must be "raster" or "tin"')
        return value

class GeoreferencingParams(BaseModel):
    scale_vinkel_x: float
    offset_vinkel_x: float
    z_ground_calculation_method: str
    z_ground_offset_meters: Optional[float] = None # Optional if dsm_intersection is always used
    z_ground_fixed_value_meters: Optional[float] = None # Optional

    @validator('z_ground_calculation_method')
    def z_ground_method_valid(cls, value):
        # "dsm_path" is deprecated in favor of dsm_parameters
        valid_methods = ["avg_pose_z_minus_offset", "fixed_value", "dsm_intersection"]
        if value not in valid_methods:
            raise ValueError(f"z_ground_calculation_method must be one of {valid_methods}")
        return value

class RGBGeotiffCreationParams(BaseModel):
    target_wavelength_R_nm: float
    target_wavelength_G_nm: float
    target_wavelength_B_nm: float
    target_resolution_meters: float
    output_epsg_code: int
    normalization_method: str

    @validator('normalization_method')
    def normalization_method_valid(cls, value):
        if value not in ["min_max", "percentile_2_98"]:
            raise ValueError('normalization_method must be "min_max" or "percentile_2_98"')
        return value

class PlottingParams(BaseModel):
    # create_position_plot: Optional[bool] = False # Example
    # create_trajectory_plot: Optional[bool] = False # Example
    pass # No specific params yet, but table can exist

class ProcessingOptions(BaseModel):
    log_level: str
    enable_atmospheric_correction: bool
    coordinate_reference_system_epsg_output: int

    @validator('log_level')
    def log_level_valid(cls, value):
        if value.upper() not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
            raise ValueError('log_level must be one of DEBUG, INFO, WARNING, ERROR, CRITICAL')
        return value.upper()

class ParametersConfig(BaseModel):
    webodm_consolidation: WebODMConsolidationParams
    georeferencing: GeoreferencingParams
    rgb_geotiff_creation: RGBGeotiffCreationParams
    plotting: PlottingParams # Keep as is, can be an empty table

class Config(BaseModel):
    project_name: Optional[str] = None
    paths: PathsConfig
    sensor_model: SensorModelConfig
    parameters: ParametersConfig
    dsm_parameters: DSMParametersConfig
    processing_options: ProcessingOptions

# Example of how to load and validate:
# import toml
# from pathlib import Path
#
# def load_config(config_path: Path) -> Config:
#     try:
#         config_data = toml.load(config_path)
#         return Config(**config_data)
#     except FileNotFoundError:
#         print(f"Error: Configuration file not found at {config_path}")
#         raise
#     except Exception as e: # Catch Pydantic validation errors and others
#         print(f"Error loading or validating configuration: {e}")
#         raise
#
# if __name__ == "__main__":
#     # This is an example, assuming the script is run from the workspace root
#     # and config.toml is in the MVP directory.
#     try:
#         conf_path = Path("MVP/config.toml")
#         print(f"Attempting to load config from: {conf_path.resolve()}")
#         config = load_config(conf_path)
#         print("Configuration loaded and validated successfully!")
#         print(f"Log level: {config.processing_options.log_level}")
#         print(f"Focal length: {config.sensor_model.interior_orientation.focal_length_mm}")
#     except Exception as e:
#         print(f"Failed to load/validate config: {e}")