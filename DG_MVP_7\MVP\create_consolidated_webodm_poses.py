import json
import csv
import os
import toml
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from pydantic import ValidationError

# Assuming config_models.py is in the same directory (MVP)
try:
    from config_models import Config
except ImportError: # Fallback for direct execution if <PERSON> is not in PYTHONPATH
    from .config_models import Config


logger = logging.getLogger(__name__)

def load_config_for_consolidation(config_path_str: str) -> Optional[Config]:
    """Loads and validates the TOML configuration file."""
    config_path = Path(config_path_str)
    if not config_path.is_absolute():
        base_path = Path(__file__).parent
        config_path = base_path / config_path_str
        logger.info(f"Relative config path '{config_path_str}' resolved to '{config_path.resolve()}' for consolidation script.")

    try:
        logger.info(f"Attempting to load configuration from: {config_path.resolve()}")
        config_data = toml.load(config_path)
        validated_config = Config(**config_data)
        logger.info("Configuration loaded and validated successfully for consolidation.")
        
        # Set log level based on config
        log_level_str = validated_config.processing_options.log_level
        numeric_level = getattr(logging, log_level_str.upper(), logging.INFO)
        # Configure logger for this module if not already configured by main_pipeline
        if not logging.getLogger().handlers: # Check if root logger has handlers
            logging.basicConfig(level=numeric_level, format='%(asctime)s - %(levelname)s - %(module)s - %(message)s')
        else:
            logging.getLogger().setLevel(numeric_level)
        logger.setLevel(numeric_level) # Also set this module's logger level
        logger.info(f"Logging level set to: {log_level_str} for consolidation script.")
        return validated_config
    except FileNotFoundError:
        logger.error(f"Configuration file not found at {config_path.resolve()}")
        return None
    except ValidationError as e:
        logger.error(f"Configuration validation error: {e}")
        return None
    except Exception as e:
        logger.error(f"An unexpected error occurred while loading or validating the configuration: {e}")
        return None

def run_consolidation(config_or_path: Union[str, Config]) -> bool:
    if isinstance(config_or_path, str):
        config = load_config_for_consolidation(config_or_path)
        if config is None:
            return False
    elif isinstance(config_or_path, Config):
        config = config_or_path
        # Ensure logger is set up if called directly with a Config object
        if not logging.getLogger().handlers:
             logging.basicConfig(level=config.processing_options.log_level.upper(), format='%(asctime)s - %(levelname)s - %(module)s - %(message)s')
        logger.setLevel(config.processing_options.log_level.upper())
    else:
        logger.error("Invalid argument type for config_or_path. Must be a path string or Config object.")
        return False

    # Construct absolute paths from base directories and relative file paths
    # Ensure base directories are Path objects for correct joining
    base_webodm_data_dir = Path(config.paths.webodm_data_directory)
    base_output_dir = Path(config.paths.output_directory)

    shots_geojson_path = base_webodm_data_dir / config.paths.shots_geojson_file
    haip_dir_path = base_webodm_data_dir / config.paths.haip_files_subdirectory
    output_csv_path = base_output_dir / config.paths.consolidated_webodm_poses_csv
    
    haip_ts_key = config.parameters.webodm_consolidation.haip_timestamp_key

    # Ensure output directory exists
    try:
        base_output_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Ensured output directory exists: {base_output_dir.resolve()}")
    except OSError as e:
        logger.error(f"Error creating output directory {base_output_dir.resolve()}: {e}")
        return False

    consolidated_data = []
    header = [
        'image_filename', 'haip_timestamp_ns',
        'pos_x', 'pos_y', 'pos_z',
        'rot_1', 'rot_2', 'rot_3'
    ]

    try:
        with open(shots_geojson_path, 'r', encoding='utf-8') as f:
            shots_data = json.load(f)
    except FileNotFoundError:
        logger.error(f"{shots_geojson_path} not found.")
        return False
    except json.JSONDecodeError:
        logger.error(f"Could not decode JSON from {shots_geojson_path}.")
        return False
    except Exception as e:
        logger.error(f"An unexpected error occurred while reading {shots_geojson_path}: {e}")
        return False

    if 'features' not in shots_data or not isinstance(shots_data['features'], list):
        logger.error(f"'features' key not found or not a list in {shots_geojson_path}.")
        return False

    for feature_idx, feature in enumerate(shots_data['features']):
        if not isinstance(feature, dict):
            logger.warning(f"Feature at index {feature_idx} is not a dictionary. Skipping.")
            continue
            
        properties = feature.get('properties', {})
        if not isinstance(properties, dict):
            logger.warning(f"Properties for feature at index {feature_idx} is not a dictionary. Skipping.")
            continue

        image_filename = properties.get('filename')
        translation = properties.get('translation')
        rotation = properties.get('rotation')

        if not image_filename or not isinstance(image_filename, str):
            logger.warning(f"Missing or invalid 'filename' for feature at index {feature_idx}. Skipping.")
            continue
        if not (isinstance(translation, list) and len(translation) == 3 and all(isinstance(v, (int, float)) for v in translation)):
            logger.warning(f"Missing or invalid 'translation' for image {image_filename}. Skipping.")
            continue
        if not (isinstance(rotation, list) and len(rotation) == 3 and all(isinstance(v, (int, float)) for v in rotation)):
            logger.warning(f"Missing or invalid 'rotation' for image {image_filename}. Skipping.")
            continue
            
        base_image_filename, _ = os.path.splitext(image_filename)
        haip_filename = base_image_filename + '.haip'
        haip_filepath = haip_dir_path / haip_filename
        
        timestamp_rgb_ns = None
        try:
            found_rgb_timestamp_key = False
            raw_timestamp_value_str = None

            if not haip_filepath.is_file():
                logger.warning(f"HAIP file {haip_filepath} not found for image {image_filename}. Skipping this entry.")
                continue

            with open(haip_filepath, 'r', encoding='utf-8') as hf:
                for line_num, line in enumerate(hf, 1):
                    stripped_line = line.strip()
                    if stripped_line.startswith(haip_ts_key + ':'):
                        parts = stripped_line.split(':', 1)
                        if len(parts) == 2:
                            raw_timestamp_value_str = parts[1].strip()
                            try:
                                timestamp_rgb_original = float(raw_timestamp_value_str)
                                timestamp_rgb_ns = int(timestamp_rgb_original * 1000) # Assuming original is in ms
                                found_rgb_timestamp_key = True
                                logger.debug(f"Found timestamp {timestamp_rgb_ns} ns for {image_filename} from HAIP.")
                                break
                            except ValueError:
                                logger.warning(f"Could not convert '{haip_ts_key}' timestamp value '{raw_timestamp_value_str}' to number in {haip_filepath} (line {line_num}) for image {image_filename}. Skipping this entry.")
                                timestamp_rgb_ns = None # Ensure it's None if conversion failed
                                found_rgb_timestamp_key = True # Key was found, but value was bad
                                break
            
            if not found_rgb_timestamp_key:
                logger.warning(f"'{haip_ts_key}:' key line not found in {haip_filepath} for image {image_filename}. Skipping this entry.")
                continue
            
            if found_rgb_timestamp_key and timestamp_rgb_ns is None:
                # This case (ValueError during conversion) was already logged.
                continue

        except Exception as e: # Catch any other errors during HAIP file processing
            logger.warning(f"An unexpected error occurred while processing {haip_filepath} for image {image_filename}: {e}. Skipping this entry.")
            continue
            
        row: Dict[str, Any] = {
            'image_filename': image_filename,
            'haip_timestamp_ns': timestamp_rgb_ns,
            'pos_x': translation[0],
            'pos_y': translation[1],
            'pos_z': translation[2],
            'rot_1': rotation[0],
            'rot_2': rotation[1],
            'rot_3': rotation[2]
        }
        consolidated_data.append(row)

    if not consolidated_data:
        logger.info("No data processed. Output CSV will not be created or will be empty.")
        # Create an empty CSV with header if no data, to signify processing occurred.
        try:
            with open(output_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=header)
                writer.writeheader()
            logger.info(f"Created empty {output_csv_path} as no data was consolidated.")
            return True # Script ran, no data found is not a script failure.
        except IOError:
            logger.error(f"Could not write empty CSV to {output_csv_path}.")
            return False


    try:
        with open(output_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=header)
            writer.writeheader()
            writer.writerows(consolidated_data)
        logger.info(f"Successfully created {output_csv_path} with {len(consolidated_data)} entries.")
        return True
    except IOError:
        logger.error(f"Could not write to {output_csv_path}.")
        return False
    except Exception as e:
        logger.error(f"An unexpected error occurred while writing to {output_csv_path}: {e}")
        return False

if __name__ == "__main__":
    # Setup basic logging for standalone execution
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(module)s - %(message)s')
    
    DEFAULT_CONFIG_PATH = 'config.toml' # Assumes it's in the same MVP directory
    logger.info(f"Running WebODM Pose Consolidation with configuration: {DEFAULT_CONFIG_PATH}")
    
    # For standalone, we pass the path string. The function will handle loading.
    success = run_consolidation(config_or_path=DEFAULT_CONFIG_PATH)
    
    if success:
        logger.info("WebODM Pose Consolidation completed successfully.")
    else:
        logger.error("Error during WebODM Pose Consolidation.")
        print("Fehler bei der Konsolidierung der WebODM Posen.")