# Globale Einstellungen oder Projektinformationen (optional)
project_name = "HSI Direct Georeferencing PoC"

[paths]
# Basisverzeichnisse (relativ zum Projekt-Hauptverzeichnis)
hsi_data_directory = "data/HSI/"
webodm_data_directory = "data/WebODM/"
output_directory = "." # Hauptverzeichnis für die meisten Ausgaben
plot_output_directory = "plots/" # Unterverzeichnis für Plots, relativ zum output_directory

# HSI spezifische Dateinamen oder relative Pfade (Basis ist hsi_data_directory)
hsi_base_filename = "2025-05-15_08-28-48_cont" # Wird für .hdr, .img, .sync.txt verwendet
sensor_model_file = "Sensormodel_HAIP_BlackBirdV2_No6_20m.txt"

# WebODM spezifische Dateinamen oder relative Pfade (Basis ist webodm_data_directory)
shots_geojson_file = "shots.geojson"
haip_files_subdirectory = "haip_files/" # Unterverzeichnis für .haip Dateien
dsm_file = "data/WebODM/dsm.tif"

# Namen der Zwischen- und Ausgabedateien (werden im output_directory gespeichert, falls nicht anders angegeben)
consolidated_webodm_poses_csv = "webodm_poses_consolidated.csv"
hsi_poses_csv = "hsi_poses.csv"
georeferenced_pixels_csv = "georeferenced_pixels.csv"
# Ein neuer Name, um Verwechslungen mit alten Dateien zu vermeiden, wenn das Skript mit Config läuft
georeferenced_rgb_tif = "georeferenced_rgb_via_config.tif"

[sensor_model.interior_orientation]
# Interior Orientation Parameters (IOPs)
focal_length_mm = 35.0 # Focal length of the sensor in millimeters
pixel_size_um = 4.5 # Pixel size in micrometers
principal_point_x_mm = 0.01 # Principal point x-coordinate in millimeters (offset from sensor center)
principal_point_y_mm = -0.005 # Principal point y-coordinate in millimeters (offset from sensor center)
# Lens distortion coefficients (e.g., Brown's model)
k1 = 1.2e-5 # Radial distortion coefficient k1
k2 = 0.0 # Radial distortion coefficient k2 (example, often non-zero)
k3 = 0.0 # Radial distortion coefficient k3 (example, often non-zero)
p1 = 0.0 # Tangential distortion coefficient p1 (example, often non-zero)
p2 = 0.0 # Tangential distortion coefficient p2 (example, often non-zero)
[sensor_model.boresight_alignment_deg]
# Boresight alignment parameters (angular offsets of sensor frame relative to IMU body frame)
# Rotation order and direction can be specified if needed, e.g., "Rotation from IMU body frame to sensor frame: ZYX"
roll_offset_deg = 0.1     # Boresight roll offset in degrees
pitch_offset_deg = -1.35  # Boresight pitch offset in degrees
yaw_offset_deg = 0.0      # Boresight yaw offset in degrees

[sensor_model.lever_arms_meters]
# Lever arm components in meters, defined in a consistent body-fixed coordinate system (e.g., FRD - Front-Right-Down)
# GNSS antenna phase center to IMU reference point
gnss_to_imu_x_m = 0.0  # X component of GNSS to IMU lever arm
gnss_to_imu_y_m = 0.0  # Y component of GNSS to IMU lever arm
gnss_to_imu_z_m = 0.0  # Z component of GNSS to IMU lever arm
# IMU reference point to sensor perspective center
imu_to_sensor_x_m = 0.0 # X component of IMU to Sensor lever arm
imu_to_sensor_y_m = 0.0 # Y component of IMU to Sensor lever arm
imu_to_sensor_z_m = 0.0 # Z component of IMU to Sensor lever arm

[parameters.webodm_consolidation]
# Schlüssel für den Zeitstempel in den .haip Dateien
haip_timestamp_key = "rgb"

[dsm_parameters]
# Path to the Digital Surface Model (DSM) file
path = "data/WebODM/dsm.tif" # Corresponds to paths.dsm_file
# Type of DSM (e.g., "raster" for GeoTIFF, "tin" for Triangular Irregular Network)
type = "raster"
# Optional: Default vertical uncertainty of the DSM in meters
default_vertical_uncertainty_m = 0.5
# Optional: No-data value used in the DSM raster, if applicable
nodata_value = -9999.0 # Example, adjust if your DSM uses a different nodata value
# Parameters for Ray-DSM Intersection
ray_dsm_max_search_dist_m = 2000.0 # Maximum search distance along the ray in meters
ray_dsm_step_m = 5.0             # Initial step size for ray marching in meters
ray_dsm_bisection_tolerance_m = 0.01 # Tolerance for the bisection method in meters (accuracy of the intersection point)

[parameters.georeferencing]
# Sensor model parameters (optimized)
scale_vinkel_x = 1
offset_vinkel_x = 0

# Methode zur Berechnung der Grundebenenhöhe Z_ground
# Optionen könnten sein: "avg_pose_z_minus_offset", "fixed_value", "dsm_path" (für später)
z_ground_calculation_method = "dsm_intersection" # This should now primarily use dsm_parameters
# Offset in Metern, der vom Durchschnitt der Kamerahöhen abgezogen wird
z_ground_offset_meters = 20.0 # Potentially deprecated if DSM is always used
# Fester Wert für Z_ground, falls z_ground_calculation_method = "fixed_value"
z_ground_fixed_value_meters = 100.0 # Potentially deprecated if DSM is always used
# Pfad zu einem DSM, falls z_ground_calculation_method = "dsm_path" (optional, für spätere Erweiterung)
# dsm_file_path = "data/WebODM/dsm.tif" # This is now dsm_parameters.path

[parameters.rgb_geotiff_creation]
# Ziel-Wellenlängen in Nanometern für die RGB-Kanäle
target_wavelength_R_nm = 800.0
target_wavelength_G_nm = 700.0
target_wavelength_B_nm = 550.0

# Ziel-Auflösung für das GeoTIFF in Metern
target_resolution_meters = 0.1

# EPSG-Code für das Koordinatenbezugssystem des Ausgabe-GeoTIFFs
output_epsg_code = 32632 # UTM Zone 32N, WGS84

# Normalisierungsmethode für RGB-Bänder beim Erstellen des GeoTIFFs
# Mögliche Optionen: "min_max", "percentile_2_98" (streckt zwischen 2. und 98. Perzentil)
normalization_method = "min_max"

[processing_options]
# General processing options
log_level = "INFO" # Logging verbosity, e.g., "DEBUG", "INFO", "WARNING", "ERROR"
enable_atmospheric_correction = false # Flag to enable/disable atmospheric correction (advanced feature)
coordinate_reference_system_epsg_output = 32632 # Default output EPSG code for georeferenced products

[parameters.plotting]
# Hier könnten spezifische Plot-Parameter definiert werden, falls zukünftig benötigt.
# Beispiel:
# create_position_plot = false
# create_trajectory_plot = false