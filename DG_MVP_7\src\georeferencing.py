"""
Enhanced Georeferencing Processor for HSI Direct Georeferencing

This module implements the main georeferencing processing pipeline with modular design,
comprehensive input validation, error propagation, and detailed logging.

Implements requirements from prompts:
- LS1_GEO_MODULARITY_MAIN
- LS1_GEO_INPUT_VALIDATION
- LS1_GEO_LOGGING
- LS1_GEO_TIMESYNC
"""

import numpy as np
import pandas as pd
from pathlib import Path
from typing import Optional, Tuple, Dict, List, Union
from scipy.spatial.transform import Rotation
import logging

from .config_models import Config, load_and_validate_config
from .sensor_model import SensorModel
from .dsm_manager import DSMManager
from .error_propagation import ErrorPropagationModule
from .utils import setup_logger, validate_file_exists, validate_directory_exists

logger = logging.getLogger(__name__)


class GeoreferencingProcessor:
    """
    Main processor for HSI pixel georeferencing with enhanced modularity and error handling.
    """
    
    def __init__(self, config: Union[str, Path, Config]):
        """
        Initialize georeferencing processor.
        
        Args:
            config: Configuration file path or Config object
        """
        # Load and validate configuration
        if isinstance(config, (str, Path)):
            self.config = load_and_validate_config(config)
            if self.config is None:
                raise ValueError("Failed to load and validate configuration")
        else:
            self.config = config
        
        # Setup logging
        self.logger = setup_logger(
            f"{__name__}.{self.__class__.__name__}",
            self.config.processing_options.log_level
        )
        
        # Initialize components
        self.sensor_model: Optional[SensorModel] = None
        self.dsm_manager: Optional[DSMManager] = None
        self.error_propagation: Optional[ErrorPropagationModule] = None
        
        self.logger.info("GeoreferencingProcessor initialized")
    
    def validate_inputs(self) -> bool:
        """
        Comprehensive input validation.
        
        Returns:
            True if all inputs are valid, False otherwise
        """
        self.logger.info("Starting comprehensive input validation...")
        
        # Validate file paths
        base_hsi_dir = Path(self.config.paths.hsi_data_directory)
        hdr_file = base_hsi_dir / f"{self.config.paths.hsi_base_filename}.hdr"
        sensor_model_file = base_hsi_dir / self.config.paths.sensor_model_file
        
        output_dir = Path(self.config.paths.output_directory)
        poses_file = output_dir / self.config.paths.hsi_poses_csv
        
        # Check file existence
        if not validate_file_exists(hdr_file, "HSI header file"):
            return False
        if not validate_file_exists(sensor_model_file, "Sensor model file"):
            return False
        if not validate_file_exists(poses_file, "HSI poses file"):
            return False
        
        # Validate DSM if using DSM intersection
        if self.config.parameters.georeferencing.z_ground_calculation_method == "dsm_intersection":
            dsm_file = Path(self.config.dsm_parameters.path)
            if not validate_file_exists(dsm_file, "DSM file"):
                return False
        
        # Validate output directory
        if not validate_directory_exists(output_dir, "Output directory", create_if_missing=True):
            return False
        
        # Validate data consistency
        if not self._validate_data_consistency(hdr_file, sensor_model_file, poses_file):
            return False
        
        self.logger.info("Input validation completed successfully")
        return True
    
    def _validate_data_consistency(self, hdr_file: Path, sensor_model_file: Path, 
                                 poses_file: Path) -> bool:
        """
        Validate consistency between different data files.
        
        Args:
            hdr_file: HSI header file path
            sensor_model_file: Sensor model file path
            poses_file: Poses file path
            
        Returns:
            True if data is consistent, False otherwise
        """
        try:
            # Parse HSI header
            hdr_info = self._parse_hsi_header(hdr_file)
            if hdr_info is None:
                return False
            
            num_samples, num_lines, _ = hdr_info
            
            # Check sensor model consistency
            sensor_data = pd.read_csv(sensor_model_file, delim_whitespace=True, header=None)
            if len(sensor_data) != num_samples:
                self.logger.error(f"Sensor model entries ({len(sensor_data)}) don't match "
                                f"HSI samples ({num_samples})")
                return False
            
            # Check poses consistency
            poses_df = pd.read_csv(poses_file)
            if len(poses_df) != num_lines:
                self.logger.error(f"Number of poses ({len(poses_df)}) doesn't match "
                                f"HSI lines ({num_lines})")
                return False
            
            # Check temporal overlap (basic check)
            if 'timestamp' in poses_df.columns:
                pose_time_range = poses_df['timestamp'].max() - poses_df['timestamp'].min()
                self.logger.info(f"Pose temporal range: {pose_time_range:.2f} seconds")
                
                if pose_time_range <= 0:
                    self.logger.warning("Pose timestamps show no temporal progression")
            
            self.logger.info("Data consistency validation passed")
            return True
            
        except Exception as e:
            self.logger.error(f"Data consistency validation failed: {e}")
            return False
    
    def _parse_hsi_header(self, hdr_file: Path) -> Optional[Tuple[int, int, np.ndarray]]:
        """
        Parse HSI header file.
        
        Args:
            hdr_file: Path to HSI header file
            
        Returns:
            Tuple of (samples, lines, lever_arm) or None if parsing fails
        """
        try:
            header_data = {}
            with open(hdr_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        if key == 'samples':
                            header_data['samples'] = int(value)
                        elif key == 'lines':
                            header_data['lines'] = int(value)
                        elif key.startswith('OffsetBetweenMainAntennaAndTargetPoint'):
                            # Parse lever arm from header (for logging/comparison)
                            value = value.strip().replace('(', '').replace(')', '')
                            parts = value.split(',')
                            if len(parts) == 3:
                                header_data['lever_arm'] = np.array([float(p.strip()) / 1000.0 for p in parts])
            
            if 'samples' not in header_data or 'lines' not in header_data:
                self.logger.error("Required header fields 'samples' or 'lines' not found")
                return None
            
            lever_arm = header_data.get('lever_arm', np.array([0.0, 0.0, 0.0]))
            return header_data['samples'], header_data['lines'], lever_arm
            
        except Exception as e:
            self.logger.error(f"Failed to parse HSI header: {e}")
            return None
    
    def initialize_components(self) -> bool:
        """
        Initialize all processing components.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            self.logger.info("Initializing processing components...")
            
            # Initialize sensor model
            base_hsi_dir = Path(self.config.paths.hsi_data_directory)
            sensor_model_path = base_hsi_dir / self.config.paths.sensor_model_file
            
            self.sensor_model = SensorModel(self.config, sensor_model_path)
            self.logger.info("SensorModel initialized")
            
            # Initialize DSM manager if needed
            if self.config.parameters.georeferencing.z_ground_calculation_method == "dsm_intersection":
                self.dsm_manager = DSMManager(self.config)
                self.logger.info("DSMManager initialized")
            
            # Initialize error propagation module
            self.error_propagation = ErrorPropagationModule(self.config, self.sensor_model)
            self.logger.info("ErrorPropagationModule initialized")
            
            self.logger.info("All components initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Component initialization failed: {e}")
            return False
    
    def load_pose_data(self) -> Optional[pd.DataFrame]:
        """
        Load and validate pose data.
        
        Returns:
            Pose DataFrame or None if loading fails
        """
        try:
            output_dir = Path(self.config.paths.output_directory)
            poses_file = output_dir / self.config.paths.hsi_poses_csv
            
            self.logger.info(f"Loading pose data from: {poses_file}")
            poses_df = pd.read_csv(poses_file)
            
            # Validate required columns
            required_columns = ['pos_x', 'pos_y', 'pos_z', 'rot_x', 'rot_y', 'rot_z', 'rot_w']
            missing_columns = [col for col in required_columns if col not in poses_df.columns]
            
            if missing_columns:
                self.logger.error(f"Missing required columns in poses file: {missing_columns}")
                return None
            
            self.logger.info(f"Loaded {len(poses_df)} poses successfully")
            return poses_df
            
        except Exception as e:
            self.logger.error(f"Failed to load pose data: {e}")
            return None
    
    def process_scanline(self, line_index: int, pose_data: pd.Series, 
                        num_samples: int) -> List[Dict]:
        """
        Process a single HSI scanline.
        
        Args:
            line_index: HSI line index
            pose_data: Pose data for this line
            num_samples: Number of samples in the line
            
        Returns:
            List of georeferenced pixel results
        """
        results = []
        
        try:
            # Extract pose information
            position_world = np.array([pose_data['pos_x'], pose_data['pos_y'], pose_data['pos_z']])
            quaternion = np.array([pose_data['rot_x'], pose_data['rot_y'], 
                                 pose_data['rot_z'], pose_data['rot_w']])
            
            # Create rotation matrix from quaternion
            rotation = Rotation.from_quat(quaternion)
            R_world_to_body = rotation.as_matrix()
            R_body_to_world = R_world_to_body.T
            
            # Calculate flight height for uncertainty estimation
            flight_height_agl = position_world[2] - 100.0  # Simplified estimate
            
            # Process each pixel in the scanline
            for pixel_index in range(num_samples):
                try:
                    pixel_result = self._process_pixel(
                        line_index, pixel_index, position_world, R_body_to_world, flight_height_agl
                    )
                    results.append(pixel_result)
                    
                except Exception as e:
                    self.logger.warning(f"Failed to process pixel {pixel_index} in line {line_index}: {e}")
                    # Add NaN result for failed pixel
                    results.append({
                        'hsi_line_index': line_index,
                        'pixel_index': pixel_index,
                        'X_ground': np.nan,
                        'Y_ground': np.nan,
                        'Z_ground': np.nan,
                        'pos_uncertainty_m': np.nan
                    })
            
        except Exception as e:
            self.logger.error(f"Failed to process scanline {line_index}: {e}")
        
        return results
    
    def _process_pixel(self, line_index: int, pixel_index: int, position_world: np.ndarray,
                      R_body_to_world: np.ndarray, flight_height_agl: float) -> Dict:
        """
        Process a single pixel.
        
        Args:
            line_index: HSI line index
            pixel_index: Pixel index
            position_world: Camera position in world coordinates
            R_body_to_world: Rotation matrix from body to world frame
            flight_height_agl: Flight height above ground level
            
        Returns:
            Dictionary with georeferenced pixel results
        """
        # Calculate line-of-sight vector in sensor frame
        los_sensor = self.sensor_model.calculate_los_vector(pixel_index)
        
        # Transform to body frame
        los_body = self.sensor_model.transform_sensor_to_body(los_sensor)
        
        # Transform to world frame
        los_world = R_body_to_world @ los_body
        
        # Calculate sensor position in world coordinates
        sensor_position_world = self.sensor_model.calculate_sensor_position_world(
            position_world, R_body_to_world
        )
        
        # Calculate ground intersection
        if self.config.parameters.georeferencing.z_ground_calculation_method == "dsm_intersection":
            # Use DSM intersection
            x_ground, y_ground, z_ground = self.dsm_manager.calculate_ray_dsm_intersection(
                sensor_position_world, los_world
            )
        else:
            # Use flat plane intersection
            z_ground_plane = self._calculate_flat_plane_z()
            x_ground, y_ground, z_ground = self._intersect_ray_with_plane(
                sensor_position_world, los_world, z_ground_plane
            )
        
        # Calculate uncertainty
        uncertainty_dict = self.error_propagation.estimate_pixel_uncertainty(
            pixel_index, flight_height_agl
        )
        combined_uncertainty = self.error_propagation.combine_uncertainties(uncertainty_dict)
        
        return {
            'hsi_line_index': line_index,
            'pixel_index': pixel_index,
            'X_ground': x_ground,
            'Y_ground': y_ground,
            'Z_ground': z_ground,
            'pos_uncertainty_m': combined_uncertainty['total_uncertainty_m'],
            'x_uncertainty_m': combined_uncertainty['x_uncertainty_m'],
            'y_uncertainty_m': combined_uncertainty['y_uncertainty_m'],
            'z_uncertainty_m': combined_uncertainty['z_uncertainty_m']
        }
    
    def _calculate_flat_plane_z(self) -> float:
        """Calculate Z coordinate for flat plane intersection."""
        method = self.config.parameters.georeferencing.z_ground_calculation_method
        
        if method == "fixed_value":
            return self.config.parameters.georeferencing.z_ground_fixed_value_meters or 100.0
        elif method == "avg_pose_z_minus_offset":
            # This would require pose data - simplified for now
            offset = self.config.parameters.georeferencing.z_ground_offset_meters or 20.0
            return 100.0 - offset  # Placeholder
        else:
            return 100.0  # Default fallback
    
    def _intersect_ray_with_plane(self, ray_origin: np.ndarray, ray_direction: np.ndarray,
                                 plane_z: float) -> Tuple[float, float, float]:
        """
        Intersect ray with horizontal plane.
        
        Args:
            ray_origin: Ray origin point
            ray_direction: Ray direction vector
            plane_z: Z coordinate of the plane
            
        Returns:
            Intersection point (x, y, z)
        """
        # Calculate parameter t for intersection with plane z = plane_z
        if abs(ray_direction[2]) < 1e-10:  # Ray is parallel to plane
            return np.nan, np.nan, np.nan
        
        t = (plane_z - ray_origin[2]) / ray_direction[2]
        
        if t < 0:  # Intersection is behind the ray origin
            return np.nan, np.nan, np.nan
        
        intersection_point = ray_origin + t * ray_direction
        return intersection_point[0], intersection_point[1], intersection_point[2]
    
    def save_results(self, results: List[Dict]) -> bool:
        """
        Save georeferencing results to CSV file.
        
        Args:
            results: List of georeferenced pixel results
            
        Returns:
            True if saving successful, False otherwise
        """
        try:
            output_dir = Path(self.config.paths.output_directory)
            output_file = output_dir / self.config.paths.georeferenced_pixels_csv
            
            # Convert results to DataFrame
            results_df = pd.DataFrame(results)
            
            # Save to CSV
            results_df.to_csv(output_file, index=False)
            
            self.logger.info(f"Results saved to: {output_file}")
            self.logger.info(f"Total pixels processed: {len(results)}")
            
            # Log statistics
            valid_results = results_df.dropna(subset=['X_ground', 'Y_ground', 'Z_ground'])
            self.logger.info(f"Valid georeferenced pixels: {len(valid_results)}")
            
            if len(valid_results) > 0:
                mean_uncertainty = valid_results['pos_uncertainty_m'].mean()
                self.logger.info(f"Mean positional uncertainty: {mean_uncertainty:.3f}m")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save results: {e}")
            return False

    def run_georeferencing(self) -> bool:
        """
        Run the complete georeferencing pipeline.

        Returns:
            True if processing successful, False otherwise
        """
        try:
            self.logger.info("Starting HSI pixel georeferencing pipeline...")

            # Step 1: Validate inputs
            if not self.validate_inputs():
                self.logger.error("Input validation failed")
                return False

            # Step 2: Initialize components
            if not self.initialize_components():
                self.logger.error("Component initialization failed")
                return False

            # Step 3: Load pose data
            poses_df = self.load_pose_data()
            if poses_df is None:
                self.logger.error("Failed to load pose data")
                return False

            # Step 4: Get HSI dimensions
            base_hsi_dir = Path(self.config.paths.hsi_data_directory)
            hdr_file = base_hsi_dir / f"{self.config.paths.hsi_base_filename}.hdr"
            hdr_info = self._parse_hsi_header(hdr_file)
            if hdr_info is None:
                self.logger.error("Failed to parse HSI header")
                return False

            num_samples, num_lines, _ = hdr_info
            self.logger.info(f"Processing HSI data: {num_samples} samples × {num_lines} lines")

            # Step 5: Process all scanlines
            all_results = []
            for line_index in range(num_lines):
                if line_index % 100 == 0:
                    self.logger.info(f"Processing line {line_index}/{num_lines}")

                pose_data = poses_df.iloc[line_index]
                line_results = self.process_scanline(line_index, pose_data, num_samples)
                all_results.extend(line_results)

            # Step 6: Save results
            if not self.save_results(all_results):
                self.logger.error("Failed to save results")
                return False

            self.logger.info("Georeferencing pipeline completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Georeferencing pipeline failed: {e}")
            return False


def run_georeferencing(config_or_path: Union[str, Path, Config]) -> bool:
    """
    Main entry point for georeferencing processing.

    Args:
        config_or_path: Configuration file path or Config object

    Returns:
        True if processing successful, False otherwise
    """
    try:
        processor = GeoreferencingProcessor(config_or_path)
        return processor.run_georeferencing()
    except Exception as e:
        logger.error(f"Failed to create georeferencing processor: {e}")
        return False
