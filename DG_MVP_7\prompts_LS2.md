# Prompts for Layer Step 2 (LS2) - HSI Georeferencing Tool Refinement

## Prompt [LS2_DSM_FIX] - DSM Tile Indexing Bug Fix

### Context
The `DSMManager` in [`src/dsm_manager.py`](src/dsm_manager.py:1) has a critical bug in its R-tree spatial indexing for DSM tiles.
The integer ID returned by `self.spatial_index.intersection()` is not correctly mapped back to the string-based `tile_id` (e.g., "tile_row_col") used as keys in the `self.tiles` dictionary. This can lead to failures in retrieving correct DSM tiles.

### Objective
Implement the recommended fix for DSM tile indexing and retrieval logic in [`src/dsm_manager.py`](src/dsm_manager.py:1) to ensure correct tile ID mapping when using the R-tree spatial index.

### Focus Areas
- Correct mapping between R-tree integer IDs and string-based `tile_id`s.
- Proper initialization and usage of `self.indexed_tile_ids` list for mapping.
- Ensuring the fix is effective when DSM tiling is active for large DSMs.

### Code Reference
Problematic retrieval logic in `get_relevant_tiles` (from [`reflection_LS1.md`](reflection_LS1.md)):
```python
# src/dsm_manager.py:251-254
            query_bounds = (x - buffer, y - buffer, x + buffer, y + buffer)
            tile_indices = list(self.spatial_index.intersection(query_bounds))
            tile_ids = [f"tile_{i//1000}_{i%1000}" for i in tile_indices]  # Simplified mapping
            return [self.tiles[tid] for tid in tile_ids if tid in self.tiles]
```
Recommended fix for indexing in `_create_tiles` (from [`reflection_LS1.md`](reflection_LS1.md)):
```python
    # In DSMManager.__init__ or _create_tiles before loop
    self.indexed_tile_ids = []
    
    # In _create_tiles loop, after tile_id is created:
    self.indexed_tile_ids.append(tile_id)
    tile_rtree_id = len(self.indexed_tile_ids) - 1
    # ...
    if self.spatial_index is not None:
        self.spatial_index.insert(tile_rtree_id, (left, bottom, right, top))
```
Recommended fix for retrieval in `get_relevant_tiles` (from [`reflection_LS1.md`](reflection_LS1.md)):
```python
    # src/dsm_manager.py:252-254 (replacement)
            rtree_match_ids = list(self.spatial_index.intersection(query_bounds))
            actual_tile_ids = [self.indexed_tile_ids[idx] for idx in rtree_match_ids]
            return [self.tiles[tid] for tid in actual_tile_ids if tid in self.tiles]
```

### Requirements
1.  In `DSMManager`, initialize an empty list `self.indexed_tile_ids = []` before tile creation.
2.  In the `_create_tiles` method, when each tile is created:
    *   Append its string `tile_id` to `self.indexed_tile_ids`.
    *   Use the index of this `tile_id` in `self.indexed_tile_ids` (i.e., `len(self.indexed_tile_ids) - 1`) as the integer ID when inserting the tile's bounds into `self.spatial_index.insert()`.
3.  In the `get_relevant_tiles` method:
    *   Retrieve integer IDs from `self.spatial_index.intersection()`.
    *   Use these integer IDs as indices to look up the corresponding string `tile_id`s from `self.indexed_tile_ids`.
    *   Retrieve tiles from `self.tiles` using these correct string `tile_id`s.
4.  Ensure comprehensive docstrings are updated for the modified methods.

### Expected Improvements
- Correct and reliable retrieval of DSM tiles when tiling is active.
- Elimination of the "DSM Tile Indexing Bug" (Issue 1 from [`reflection_LS1.md`](reflection_LS1.md)).
- Increased stability of the georeferencing process for large DSMs.

---

## Prompt [LS2_DATA_PIPELINE] - Implement Data Consolidation and Synchronization

### Context
The HSI georeferencing pipeline in [`src/main_pipeline.py`](src/main_pipeline.py:1) currently has placeholders for critical data consolidation and HSI-EOP synchronization steps. These steps, adapted from [`MVP/create_consolidated_webodm_poses.py`](MVP/create_consolidated_webodm_poses.py) and [`MVP/synchronize_hsi_webodm.py`](MVP/synchronize_hsi_webodm.py), are essential pre-processing for accurate georeferencing and were part of LS1 prompts (LS1_CONSOLIDATE_*, LS1_SYNC_*).

### Objective
Implement robust data consolidation and HSI-EOP synchronization functionalities as new modules within the `src/` package. Integrate these modules into the `HSIProcessingPipeline` in [`src/main_pipeline.py`](src/main_pipeline.py:1), replacing the current placeholders.

### Focus Areas
- Porting and refactoring logic from MVP scripts ([`MVP/create_consolidated_webodm_poses.py`](MVP/create_consolidated_webodm_poses.py), [`MVP/synchronize_hsi_webodm.py`](MVP/synchronize_hsi_webodm.py)).
- Implementing robust Slerp (Spherical Linear Interpolation) for attitude data.
- Ensuring comprehensive input validation, error handling, and logging.
- Seamless integration into the existing `HSIProcessingPipeline`.

### Code Reference
- Placeholder sections in [`src/main_pipeline.py`](src/main_pipeline.py:92-117) (consolidation) and [`src/main_pipeline.py`](src/main_pipeline.py:119-150) (synchronization).
- MVP scripts: [`MVP/create_consolidated_webodm_poses.py`](MVP/create_consolidated_webodm_poses.py) and [`MVP/synchronize_hsi_webodm.py`](MVP/synchronize_hsi_webodm.py).
- [`src/utils.py`](src/utils.py:1) for `interpolate_timestamps` (currently basic linear, needs Slerp for attitude).

### Requirements
1.  Create two new Python modules in the `src/` directory:
    *   `data_consolidation.py`: For consolidating EOP data (e.g., from WebODM).
    *   `data_synchronization.py`: For synchronizing HSI timestamps with consolidated EOP data.
2.  In `data_consolidation.py`:
    *   Port and refactor logic from [`MVP/create_consolidated_webodm_poses.py`](MVP/create_consolidated_webodm_poses.py).
    *   Implement robust parsing, data validation, and clear logging.
3.  In `data_synchronization.py`:
    *   Port and refactor logic from [`MVP/synchronize_hsi_webodm.py`](MVP/synchronize_hsi_webodm.py).
    *   Implement Slerp for attitude interpolation (e.g., using `scipy.spatial.transform.Slerp`). Ensure correct handling of quaternions or rotation matrices.
    *   Implement robust parsing, data validation, and clear logging.
4.  Update `HSIProcessingPipeline` in [`src/main_pipeline.py`](src/main_pipeline.py:1) to:
    *   Call the new data consolidation module.
    *   Call the new data synchronization module, passing necessary data.
    *   Remove placeholder comments and logic.
5.  Ensure the new modules and updated pipeline are configurable via [`src/config_enhanced.toml`](src/config_enhanced.toml:1) if necessary (e.g., paths to input EOP files, synchronization parameters).

### Expected Improvements
- Fully functional data consolidation and synchronization within the `src/` package.
- Accurate attitude interpolation using Slerp.
- Improved robustness, logging, and traceability for pre-processing steps.
- Resolution of "Missing Implementation of Data Consolidation and Synchronization" (Issue 2 from [`reflection_LS1.md`](reflection_LS1.md)).

---

## Prompt [LS2_LENS_MODEL] - Refine Lens Distortion Model

### Context
The `apply_lens_distortion` method in [`src/sensor_model.py`](src/sensor_model.py:224-259) uses a simplified approach by applying Brown's model distortion coefficients directly to 3D Line-of-Sight (LOS) vector components. Standard photogrammetric practice involves applying 2D distortion corrections to image plane coordinates. This simplification may impact georeferencing accuracy.

### Objective
Re-evaluate and reimplement the `apply_lens_distortion` method in [`src/sensor_model.py`](src/sensor_model.py:1) to follow a more standard image-plane based correction method, as detailed in [`reflection_LS1.md`](reflection_LS1.md).

### Focus Areas
- Projection of 3D LOS to 2D image plane coordinates.
- Application of Brown's distortion model (k1, k2, k3, p1, p2) to 2D image coordinates.
- Transformation of corrected 2D image coordinates back to a 3D LOS vector.
- Correct handling of focal length and principal point offsets (ensure units are consistent, e.g., mm).

### Code Reference
Current simplified implementation in [`src/sensor_model.py`](src/sensor_model.py:240-256):
```python
# src/sensor_model.py:240-256
        # Simplified implementation: apply small corrections based on distortion coefficients
        x, y, z = vector
        r_squared = x*x + y*y
        radial_correction = 1 + self.k1 * r_squared + self.k2 * r_squared**2 + self.k3 * r_squared**3
        tangential_x = 2 * self.p1 * x * y + self.p2 * (r_squared + 2 * x*x)
        tangential_y = self.p1 * (r_squared + 2 * y*y) + 2 * self.p2 * x * y
        x_corrected = x * radial_correction + tangential_x
        y_corrected = y * radial_correction + tangential_y
        z_corrected = z # Assuming distortion primarily affects x, y in the sensor plane
        # ... (normalization follows)
```
Recommended correction steps (from [`reflection_LS1.md`](reflection_LS1.md), lines 80-93):
1.  Project undistorted 3D LOS vector to image plane `(x_u, y_u)` (e.g., at `z = -focal_length_mm`), accounting for principal point `(pp_x_mm, pp_y_mm)`.
2.  Calculate `x_offset = x_u - pp_x_mm`, `y_offset = y_u - pp_y_mm`.
3.  Calculate `r_sq = x_offset^2 + y_offset^2`.
4.  Calculate radial distortion: `dx_radial = x_offset * (k1*r_sq + k2*r_sq^2 + k3*r_sq^3)`, `dy_radial = y_offset * (k1*r_sq + k2*r_sq^2 + k3*r_sq^3)`.
5.  Calculate tangential distortion: `dx_tangential = p1 * (r_sq + 2*x_offset^2) + 2*p2*x_offset*y_offset`, `dy_tangential = p2 * (r_sq + 2*y_offset^2) + 2*p1*x_offset*y_offset`.
6.  Calculate corrected image coordinates: `x_corrected_img = x_u + dx_radial + dx_tangential`, `y_corrected_img = y_u + dy_radial + dy_tangential`.
7.  Form new 3D LOS vector: `[x_corrected_img - pp_x_mm, y_corrected_img - pp_y_mm, -focal_length_mm]` (or adjust based on coordinate system conventions, ensuring it's a direction vector).
8.  Normalize the resulting vector.

### Requirements
1.  Modify the `apply_lens_distortion` method in `SensorModel` ([`src/sensor_model.py`](src/sensor_model.py:1)).
2.  The method should take an undistorted 3D Line-of-Sight (LOS) vector (normalized) as input.
3.  Project this 3D LOS vector onto the sensor's image plane (e.g., at `z = -self.focal_length_mm`) to get undistorted 2D image coordinates `(x_u, y_u)`. Remember to account for the principal point offset (`self.pp_x_mm`, `self.pp_y_mm`).
4.  Apply Brown's distortion model (using `self.k1, self.k2, self.k3, self.p1, self.p2`) to the undistorted 2D image coordinates `(x_u - self.pp_x_mm, y_u - self.pp_y_mm)` to calculate the 2D distortion shifts `(dx, dy)`.
5.  Calculate the distorted 2D image coordinates: `x_d = x_u + dx`, `y_d = y_u + dy`.
6.  Form the new, distorted 3D LOS vector using these distorted image coordinates `(x_d, y_d)`, the principal point, and the focal length (e.g., `[x_d - self.pp_x_mm, y_d - self.pp_y_mm, -self.focal_length_mm]`).
7.  Normalize the resulting distorted 3D LOS vector before returning it.
8.  Ensure all calculations correctly handle units (e.g., mm for focal length, principal point, and image coordinates used in distortion model).
9.  Update docstrings to reflect the new implementation logic and its adherence to standard photogrammetric models.

### Expected Improvements
- Increased accuracy of the georeferencing results, especially for sensors with notable lens distortion.
- Alignment of the `SensorModel` with established photogrammetric practices for lens distortion.
- Resolution of "Simplified Lens Distortion Model" (Issue 3 from [`reflection_LS1.md`](reflection_LS1.md)).

---

## Prompt [LS2_TEST_EXPANSION] - Expand Unit Test Coverage

### Context
Current unit test coverage in [`src/tests/`](src/tests/) is limited, primarily covering [`src/config_models.py`](src/config_models.py:1) and parts of [`src/sensor_model.py`](src/sensor_model.py:1). Critical modules like [`src/dsm_manager.py`](src/dsm_manager.py:1), [`src/georeferencing.py`](src/georeferencing.py:1), [`src/error_propagation.py`](src/error_propagation.py:1), [`src/rgb_creation.py`](src/rgb_creation.py:1), and [`src/utils.py`](src/utils.py:1) lack sufficient tests. This poses a risk for undetected bugs and regressions.

### Objective
Significantly expand `pytest` unit test coverage across the HSI direct georeferencing tool's codebase. Focus on achieving good coverage for core functionalities, business logic, and utility functions in all key modules. Apply Test-Driven Development (TDD) principles for any new bug fixes or features developed in LS2.

### Focus Areas
- [`src/dsm_manager.py`](src/dsm_manager.py:1): DSM loading, tiling logic, spatial index queries, ray-DSM intersection.
- [`src/georeferencing.py`](src/georeferencing.py:1): Core processing logic in `GeoreferencingProcessor`, coordinate transformations, uncertainty calculations.
- [`src/error_propagation.py`](src/error_propagation.py:1): Uncertainty estimation and combination formulas.
- [`src/rgb_creation.py`](src/rgb_creation.py:1): Band selection, resampling, normalization, metadata generation.
- [`src/utils.py`](src/utils.py:1): All utility functions.
- New modules: `data_consolidation.py` and `data_synchronization.py` (to be created per Prompt LS2_DATA_PIPELINE).
- [`src/sensor_model.py`](src/sensor_model.py:1): Tests for the refined `apply_lens_distortion` method.

### Code Reference
- Existing tests: [`src/tests/test_config_loading.py`](src/tests/test_config_loading.py), [`src/tests/test_sensor_model.py`](src/tests/test_sensor_model.py).
- Modules needing tests: [`src/dsm_manager.py`](src/dsm_manager.py:1), [`src/georeferencing.py`](src/georeferencing.py:1), [`src/error_propagation.py`](src/error_propagation.py:1), [`src/rgb_creation.py`](src/rgb_creation.py:1), [`src/utils.py`](src/utils.py:1).
- Specific test suggestions from [`reflection_LS1.md`](reflection_LS1.md) (lines 101-105).

### Requirements
1.  Create new test files in [`src/tests/`](src/tests/) for modules currently lacking them (e.g., `test_dsm_manager.py`, `test_georeferencing_processor.py`, `test_error_propagation.py`, `test_rgb_creation.py`, `test_utils.py`, `test_data_consolidation.py`, `test_data_synchronization.py`).
2.  For `dsm_manager.py`:
    *   Test DSM loading (mock `rasterio` if needed).
    *   Test tile creation logic (e.g., number of tiles, bounds).
    *   Test spatial index queries with mock data or simple test DSMs.
    *   Test `calculate_ray_dsm_intersection` with known simple geometries (e.g., flat plane, sloped plane, no intersection).
    *   Test the DSM tiling bug fix (from Prompt LS2_DSM_FIX).
3.  For `georeferencing.py` (`GeoreferencingProcessor`):
    *   Test `_process_pixel` by mocking `SensorModel` and `DSMManager` dependencies.
    *   Verify correct coordinate transformations.
    *   Verify calls to uncertainty calculation methods.
4.  For `error_propagation.py`:
    *   Test `estimate_pixel_uncertainty` with known input values.
    *   Test `combine_uncertainties` with known input values.
5.  For `rgb_creation.py`:
    *   Test band selection logic.
    *   Test `resample_to_grid` with small, predictable datasets (mocking `KDTree` or using simple point clouds).
    *   Test normalization functions.
    *   Test metadata generation.
6.  For `utils.py`:
    *   Add tests for `create_rotation_matrix`.
    *   Add tests for `interpolate_timestamps` (especially if enhanced beyond linear for non-attitude data).
    *   Test any other utility functions.
7.  For `sensor_model.py`:
    *   Add specific tests for the refined `apply_lens_distortion` method, covering cases with and without distortion coefficients, and edge cases if identifiable.
8.  For the new `data_consolidation.py` and `data_synchronization.py` modules:
    *   Write comprehensive unit tests covering their core logic, including Slerp interpolation, data parsing, and validation.
9.  All tests should follow the Arrange-Act-Assert (AAA) pattern, be independent, and use descriptive names.
10. Aim for high code coverage for the targeted modules.

### Expected Improvements
- Significantly increased unit test coverage across the application.
- Enhanced code reliability and reduced risk of regressions.
- Better maintainability and confidence in future refactoring.
- Resolution of "Limited Unit Test Coverage" (Issue 4 from [`reflection_LS1.md`](reflection_LS1.md)).
- A solid foundation for Test-Driven Development practices.