# Primary Findings: Improving Direct Georeferencing for HSI Linescan Cameras

This document will store the primary findings from the initial research phase, organized by research area.

## 1. Advanced Direct Georeferencing Techniques for Airborne Linescan Imagery

Direct georeferencing of airborne hyperspectral pushbroom (linescan) systems relies on advanced mathematical frameworks integrating inertial navigation, GNSS positioning, and rigorous sensor modeling.

### Integrated Sensor Orientation Systems
Contemporary systems use **tightly coupled INS/GNSS solutions** with Kalman filtering to achieve <3 cm positional and <0.01° angular accuracy at 200 Hz update rates[S1-1][S1-5]. Key innovations include:
- **Multi-constellation GNSS** (GPS+Galileo+GLONASS) signal processing with integer ambiguity resolution
- **Coning/sculling algorithms** compensating for high-frequency aircraft vibrations[S1-5]
- **Wavelet-based denoising** of inertial measurements preserving 0.1-25 Hz motion spectrum[S1-1]

### Rigorous Sensor Models
Physical sensor modeling addresses:

**Interior Orientation:**
- Polynomial distortion models with 10+ coefficients for lens deformation (Δx = k₁r³ + k₂r⁵ + p₁(r² + 2x²))[S1-2]
- Focal plane alignment cubes mapping 2,000+ detector positions (±0.3 pixel accuracy)[S1-2]

**Exterior Orientation:**
- Modified collinearity equations incorporating INS/GNSS states:
\[
\begin{bmatrix}
X \\ Y \\ Z
\end{bmatrix}
=
\begin{bmatrix}
X_{GNSS} \\ Y_{GNSS} \\ Z_{GNSS}
\end{bmatrix}
+ R_{INS} \cdot \left( R_{mount} \cdot \begin{bmatrix} x \\ y \\ -f \end{bmatrix} + \Delta_{lever} \right)
\]
Where \(R_{mount}\) contains boresight alignment angles and \(\Delta_{lever}\) is GNSS-INS lever arm[S1-2][S1-5]

### Atmospheric Compensation
Critical for VNIR/SWIR bands:
- **Saastamoinen model** for refraction index (n = 1 + 77.6 × 10⁻⁶ P/T)[S1-2]
- **Ray tracing algorithms** adjusting line-of-sight vectors by 0.1-1.2 pixels[S1-2][S1-4]
- **Aberration correction** for aircraft velocity ( δθ = v/c sinθ )[S1-2]

### Dynamic Flight Condition Mitigation
- **Spline-based trajectory modeling** with 5th-order B-splines (0.01 s knot spacing)[S1-3]
- **Time synchronization** using PPS signals and FPGA timestamping (<10 μs sync)[S1-2]
- **Automatic motion compensation** through overlapping pushbroom scans (85% overlap)[S1-4]

### Emerging Techniques
- **Multi-sensor tight coupling** fusing LiDAR, hyperspectral, and SAR observations[S1-4]
- **Deep learning GNSS outage prediction** using LSTM networks (3 s outage tolerance)[S1-4]
- **Quantum-enhanced INS** with cold-atom interferometers (experimental)[S1-4]

The EnMAP mission demonstrates operational implementation with 12.5 m geolocation accuracy using boresight-alignable FPA and real-time PPP-RTK GNSS[S1-2]. Commercial systems like HySpex pushbroom scanners achieve <1 pixel RMSE through tight INS/GNSS integration with 200 Hz Kalman updates[S1-5]. Current research focuses on autonomous onboard processing using tensor-based SLAM approaches for real-time orthorectification[S1-4].


## 2. Robust Ray-DSM Intersection Algorithms

Ray-tracing algorithms for digital surface models (DSMs) balance geometric accuracy and computational efficiency through specialized techniques for raster/grid and TIN representations, with optimizations tailored to data size and sensor geometry.

### Raster/Grid DSM Algorithms
- **Digital Differential Analyzer (DDA):** Efficient for uniform grids using incremental steps to traverse voxels. While fast, it may miss sub-voxel details in steep terrain.
- **Ray Marching with Implicit Functions:** Solves ray-grid intersections mathematically (e.g., sphere equation in [S2-5]). Accuracy depends on grid resolution but benefits from GPU parallelization.

### TIN (Triangulated Irregular Network) Algorithms
- **Möller-Trumbore Ray-Triangle Test:** Directly computes ray-triangle intersections using barycentric coordinates (Figure 8 in [S2-2]). Formula: \( \vec{I} = \vec{S} + \vec{q} \cdot \vec{w} / (\vec{q} \cdot \vec{P}) \cdot \vec{P} \) [S2-2]. Tests parametric coordinates \( s, t \) to validate intersections [S2-2][S2-5].
- **Hierarchical Acceleration:** Bounding Volume Hierarchies (BVH) or spatial grids reduce triangle tests by culling non-overlapping regions [S2-5].

### Handling Complex Terrain
- **Steep Slopes & Discontinuities:** Surface normals and partial derivatives (e.g., \( \frac{\partial \vec{P}}{\partial u}, \frac{\partial \vec{N}}{\partial v} \)) ensure accurate shading and occlusion detection [S2-4].
- **Occlusion Management:** Retains closest intersection per ray via priority queues, critical for shadow rays and multi-bounce effects [S2-5].

### Optimization Strategies

| **Method**          | **Advantage**                                  | **Use Case**              |
|----------------------|-----------------------------------------------|---------------------------|
| Spatial Indexing     | Reduces intersection tests via grid/octree    | Large raster datasets [S2-5] |
| GPU Parallelization  | Accelerates ray traversal & shading           | Real-time rendering [S2-4]   |
| Adaptive Sampling    | Focuses computation on high-curvature regions | Steep terrain [S2-2]         |

### Linescan vs. Frame Geometry
- **Linescan Sensors:** Incremental ray updates align with sensor motion, using parametric ray origins (e.g., planetocentric coordinates in [S2-2]).
- **Frame Sensors:** Embarrassingly parallel per-pixel rays, optimized via GPU tile-based rendering [S2-4].

### Incorporating DSM Uncertainty
- **Probabilistic Intersection:** Weight ray contributions by DSM error metrics (e.g., Gaussian variance). (Inference, not directly in sources)
- **Monte Carlo Sampling:** Perturbs rays stochastically within uncertainty bounds to compute confidence intervals. (Inference, not directly in sources)

### Examples
- **NASA’s DEM Interception:** Combines ray-triangle tests with convergence checks for planetary surfaces [S2-2].
- **PBRT’s Sphere Intersection:** Solves quadratic equations for implicit grid-aligned shapes [S2-5].

Modern implementations like NVIDIA OptiX leverage hybrid approaches, combining BVH for TINs and DDA for grids, while stochastic methods address uncertainty in LiDAR-derived DSMs. (General knowledge, OptiX not explicitly in sources for this query).

## 3. Error Propagation and Accuracy Assessment in Direct Georeferencing

Direct georeferencing of HSI linescan data involves complex error propagation from multiple components.

### Error Sources
- **GPS/IMU Errors:**
    - Positioning errors (0.01–0.1 m for PPK/RTK GPS; 0.005–0.05° IMU angular drift) directly impact object-space coordinates. A 0.1° IMU roll error can cause ~0.17 m lateral error per 100 m altitude [S3-4].
    - Temporal synchronization errors between GPS/IMU and sensor data lead to along-track smearing [S3-5].
- **Sensor Model Errors:**
    - Interior Orientation Parameters (IOP): Lens distortion miscalibration (e.g., radial distortion) introduces non-linear spatial errors. A 1-pixel principal point error can propagate to 0.3–0.5 m ground error at nadir [S3-1].
    - Exterior Orientation Parameters (EOP): Boresight misalignment (e.g., 0.01° error) creates rotational offsets magnified by flying height [S3-3].
- **DSM Errors:** Terrain height errors in DSMs cause relief displacement. A 1 m DSM error at a 30° off-nadir view angle results in ~0.58 m horizontal error [S3-2].
- **Calibration Parameter Errors:** Residual errors in lever arms and boresight angles dominate. A 5 mm Z-axis lever-arm error can introduce a 0.1 m ground error at 1:2000 scale [S3-4].

### Analytical Error Propagation Models
- **Covariance Propagation:** Uses Taylor series expansion to propagate variances: \( \Sigma_{XYZ} = J \cdot \text{diag}(\Sigma_{GPS}, \Sigma_{IMU}, \Sigma_{IOP}) \cdot J^T \), where J is the Jacobian matrix [S3-2, S3-4].
- **Monte Carlo Simulation:** Stochastic modeling of input parameter distributions to compute output error statistics [S3-1].

### Empirical Accuracy Assessment
- **Independent Check Points (ICPs):**
    - **Number:** 20–50 points for statistical significance [S3-4].
    - **Distribution:** 1 point per 5–10 hectares, covering planimetric (perimeter + grid) and vertical (high/low relief) variations.
    - **Accuracy:** ICPs should be 3–5 times more accurate than the sensor's expected accuracy.
- **Validation Metrics:**
    - Global Accuracy: RMSE (e.g., ≤0.5 m for 1:1000 mapping).
    - Local Accuracy: 95% confidence interval per land cover class; spatial error autocorrelation analysis.

### Geometric Validation Methods
- **Comparison Datasets:** LiDAR point clouds (vertical ±0.1 m) [S3-5], survey-grade RTK (planimetric), and overlap analysis (relative accuracy).
- **Spatially Varying Accuracy Quantification:** Zonal RMSE statistics (e.g., 100x100 m cells), error vector fields, semivariogram analysis of errors [S3-1].

### Best Practices
1.  **Pre-flight calibration:** Thermal-stabilized boresight alignment; lever-arm measurement to ±1 mm.
2.  **Redundant observations:** 60% forward overlap; cross-flight lines.
3.  **Error budgeting:** Allocate error contributions (e.g., 40% GPS/IMU, 30% sensor model, 20% DSM, 10% calibration [S3-4]).

Example: TLS cultural heritage project saw 0.12 m planimetric errors due to calibration, reduced to 0.03 m post-boresight calibration [S3-1]. GPS-supported aerial triangulation with 37 GCPs matched direct georeferencing accuracy, highlighting GCP importance in validation [S3-4].

## 4. Best Practices for Sensor Model Calibration, Boresight Alignment, and Lever Arm Correction

Hyperspectral imaging (HSI) line-scan sensor calibration requires meticulous attention to interior orientation, boresight alignment, and lever arm correction to ensure accurate georeferencing.

### Interior Orientation Parameters (IOP) Calibration
- **Laboratory methods:** Focus on spectral, radiometric, and geometric calibration.
    - Spectral calibration: Ensures precise wavelength alignment using monochromatic light sources or calibration panels [S4-1, S4-2].
    - Radiometric correction: Characterizes sensor response to uniform illumination, often using integrating spheres [S4-1, S4-5].
    - Geometric calibration: Employs line-scan-specific patterns (e.g., parallel/crossed lines or grids) to compute intrinsic parameters like focal length, principal point, and lens distortion [S4-4, S4-5].
        - Example: A calibration grid with intersecting lines quantifies lens distortion and focal plane alignment, reducing residual errors to <0.3 pixels [S4-4].
        - Tools: Defocused homogeneous white surfaces help identify sensor-related inhomogeneities (e.g., fixed-pattern noise) [S4-5].
- **In-flight methods:** Use natural targets (e.g., water bodies or uniform terrains) for dynamic radiometric adjustments and cross-validation of lab-derived IOPs [S4-1].

### Boresight Alignment (Sensor-IMU Angular Relationship)
- **Laboratory methods:** Use precision rotational stages to align the sensor’s optical axis with the IMU reference frame. Cross-line calibration patterns help quantify angular offsets [S4-4, S4-5].
- **In-flight methods:** Analyze overlapping flight lines or ground control points (GCPs) to compute residual pitch, roll, and yaw errors via bundle adjustment [S4-1].
- **Sensitivity:** A 0.01° boresight error can cause ~1.7 m positional drift at 10 km altitude. Regular in-flight validation is critical.

### Lever Arm Correction (GPS-IMU-Sensor Offsets)
- **Static surveys:** Use 3D laser scanners or theodolites to measure distances between GPS antenna phase center, IMU origin, and sensor focal point.
- **Dynamic compensation:** For flexible platforms (e.g., drones), real-time strain gauges or photogrammetric targets monitor structural flexure during flight [S4-3].
- **Impact:** A 1 cm lever arm error in altitude translates to ~1 m vertical georeferencing error.

### Recalibration Frequency
- **IOPs:** Recalibrate after mechanical shocks, temperature extremes, or annually for stable environments [S4-5].
- **Boresight:** Validate before each campaign; full recalibrations every 6–12 months.
- **Lever arms:** Re-measure after hardware modifications or significant vibrations.

### Integrated Calibration Approaches
Modern systems combine lab and in-flight data:
1.  **Pre-flight:** Lab-derived IOPs and boresight angles.
2.  **In-flight:** GCPs or LiDAR/DSM references refine parameters via least-squares optimization [S4-1, S4-4].
3.  **Post-processing:** Bundle adjustment software (e.g., PARGE or ENVI) jointly optimizes IOPs, boresight, and lever arms using tie points [S4-2, S4-5].
    - Example: A 2024 study achieved <2 m RMSE in aerial HSI by integrating lab-calibrated IOPs with in-flight GCP-derived boresight corrections [S4-1, S4-4].

### Georeferencing Sensitivity to Calibration Parameters

| Parameter          | Error Magnitude | Georeferencing Impact (at 10 km altitude, example) |
|--------------------|-----------------|----------------------------------------------------|
| Focal length (IOP) | 0.1%            | ~10 m planimetric error                            |
| Boresight roll     | 0.02°           | ~3.5 m cross-track error                           |
| Lever arm Z-offset | 1 cm            | ~1 m vertical error                               |

Rigorous lab protocols, frequent in-flight validation, and holistic adjustment workflows are key to achieving sub-pixel georeferencing accuracy [S4-1, S4-2, S4-5].

## 5. Potential Python Libraries or Algorithms to Enhance Current Implementation

For enhancing ray-DSM intersection logic in Python-based HSI georeferencing tools, several libraries and approaches offer optimal performance and scalability.

### Core Geometry and Ray Tracing Libraries
-   **`point_cloud_utils` (with Intel Embree):**
    -   **Feature:** Python bindings to Intel's Embree, a highly optimized CPU-based ray-tracing kernel [S5-4, S5-5].
    -   **Performance:** Capable of millions of ray/mesh intersections per second using Bounding Volume Hierarchy (BVH) acceleration.
    -   **Usage:**
        ```python
        import point_cloud_utils as pcu
        # Assuming DSM is converted to vertices (v) and faces (f)
        # ray_origins and ray_directions are NumPy arrays (Nx3)
        face_indices, barycentric_coords, distances = pcu.ray_mesh_intersection(v, f, ray_origins, ray_directions)
        ```
-   **GPU-accelerated Ray Tracing (Custom CUDA or via libraries):**
    -   **Feature:** Leverage GPU parallelism for significantly faster intersection tests, especially for massive numbers of rays [S5-5].
    -   **Performance:** Can achieve >10 million rays/second on modern GPUs.
    -   **Consideration:** Requires CUDA toolkit and GPU hardware. Libraries like PyCUDA or Numba can help interface Python with CUDA kernels. OptiX (Nvidia) is a powerful option if C++/CUDA development is feasible.

### Geospatial Data Handling (DSM Input)
-   **`Rasterio` and `GDAL`:**
    -   **Feature:** Robust libraries for reading, writing, and manipulating various raster geospatial data formats, including large DSMs.
    -   **Usage:** Efficiently read DSM tiles or windows for out-of-core processing.
        ```python
        import rasterio
        with rasterio.open('large_dsm.tif') as src:
            # Read a specific window/tile
            dsm_tile = src.read(1, window=rasterio.windows.Window(col_off, row_off, width, height))
        ```
-   **`PyVista` or `Trimesh` (for DSM to Mesh Conversion):**
    -   **Feature:** Convert gridded DSM data into a 3D mesh (triangles) suitable for ray-tracing libraries like Embree.
    -   **Usage (`PyVista`):**
        ```python
        import pyvista as pv
        import numpy as np
        # x, y are 1D coord arrays, dsm_array is 2D elevation
        grid = pv.StructuredGrid(x_coords_mesh, y_coords_mesh, z_coords_mesh) # Create from np.meshgrid
        mesh = grid.delaunay_2d() # Triangulate
        vertices = mesh.points
        faces = mesh.faces.reshape((-1, 4))[:, 1:4] # Get triangle connectivity
        ```

### Spatial Indexing and Large Dataset Management
-   **`rtree`:**
    -   **Feature:** Python wrapper for `libspatialindex`, providing R-tree spatial indexing.
    -   **Usage:** If DSM is processed in tiles, an R-tree can quickly find which DSM tiles a given ray might intersect, reducing the number of tiles to load and process.
-   **`Dask`:**
    -   **Feature:** Parallel computing library that can manage out-of-core computations on large NumPy-like arrays (Dask arrays).
    -   **Usage:** Process large DSMs in chunks, applying mesh conversion and ray tracing in parallel across chunks.
        ```python
        import dask.array as da
        dsm_dask_array = da.from_array(large_dsm_numpy_array, chunks=(1024, 1024))
        # Apply functions that operate on NumPy arrays to dask array chunks
        ```

### Algorithm and Workflow Considerations
1.  **DSM Representation:**
    *   For raster DSMs, convert relevant tiles to triangular meshes for intersection with libraries like Embree.
    *   Direct ray-grid intersection (e.g., DDA-like methods) can be simpler but may be less accurate on complex terrain and harder to optimize with advanced ray-tracing engines.
2.  **Performance Trade-offs:**
    *   **CPU (Embree via `point_cloud_utils`):** Excellent performance, easier integration into Python. Good for many-core CPUs.
    *   **GPU (Custom CUDA / OptiX):** Highest potential performance but more complex development and hardware dependency.
3.  **Memory Management for Large DSMs:**
    *   Implement tiling: Read and process DSM in manageable chunks.
    *   Use spatial indexing (R-tree) to select only relevant DSM tiles for each ray or group of rays.
    *   Leverage Dask for out-of-core array operations if the entire DSM (or its mesh representation) doesn't fit in memory.

### Alternative Libraries (Potentially Lower Performance for Pure Ray Tracing)
-   **`ncollpyde`:** Python bindings for the Parry Rust collision detection library [S5-3]. Could be an option for general 3D spatial queries.
-   **`SciPy`:** Contains `scipy.spatial.KDTree` or `cKDTree` for spatial lookups, which could assist in finding nearby DSM points, but not direct ray-mesh intersection.

**Recommendation:** For the `georeference_hsi_pixels.py` script, a robust approach would be:
1.  Use `Rasterio` to read DSM tiles.
2.  For each tile, convert to a mesh using `PyVista` or a custom NumPy-based triangulation.
3.  Use `point_cloud_utils` (Embree) for ray-mesh intersection against these mesh tiles.
4.  If dealing with very large DSMs, manage tiling and tile selection using an R-tree index and potentially Dask for parallel processing of tiles.

## 6. Methods to Improve Stability and Usability of Georeferencing Tools

Improving the stability and usability of direct georeferencing software involves addressing common pitfalls with robust software engineering practices and enhancing user interaction.

### Common Pitfalls and Mitigation Strategies
-   **Pitfall: Sensor Misalignment and Calibration Drift:** Errors in IMU/GPS to camera alignment, or unmodeled gravity fields interacting with accelerometer errors, can degrade accuracy significantly [S6-1, S6-5].
    -   **Mitigation (Software):** Implement routines for regular calibration checks. Allow users to input or adjust boresight and lever arm parameters. Incorporate sensor fusion algorithms that can estimate and compensate for minor dynamic misalignments. Use robust statistical methods to detect outliers in sensor readings.
    -   **Mitigation (Process):** Emphasize pre-flight and post-flight calibration procedures.
-   **Pitfall: GPS Solution Reliability:** Over-reliance on single reference station GPS can be less reliable than network-based solutions (e.g., VRS) [S6-2].
    -   **Mitigation (Software):** Support import and processing of data from multiple GPS processing modes. Provide quality indicators for the GPS solution used.
-   **Pitfall: Projection Distortions:** Using non-Cartesian map projections without proper handling during adjustments can introduce errors [S6-1, S6-4].
    -   **Mitigation (Software):** Ensure all spatial computations are performed in a consistent 3D Cartesian coordinate system internally. Handle transformations to and from geographic/projected coordinate systems explicitly at input/output stages, using libraries like `PyProj` or `GDAL`.
-   **Pitfall: Data Synchronization Issues:** Mismatched timestamps between sensor data streams (HSI, GPS, IMU) are critical.
    -   **Mitigation (Software):** Implement rigorous timestamp validation and interpolation/resampling schemes. Log any synchronization discrepancies.

### Software Engineering Best Practices for Robustness and Maintainability
-   **Modular Design:**
    -   Separate modules for data ingestion, sensor modeling, core georeferencing algorithms, DSM interaction, and output generation.
    -   Example: A `SensorModel` abstract base class with specific implementations for different HSI cameras.
        ```python
        from abc import ABC, abstractmethod

        class SensorModel(ABC):
            def __init__(self, calibration_params):
                self.params = calibration_params
            
            @abstractmethod
            def get_los_vector(self, pixel_index, platform_orientation):
                pass
        ```
-   **Comprehensive Error Handling:**
    -   Use specific custom exceptions (e.g., `CalibrationError`, `DSMAccessError`, `TimestampMismatchError`).
    -   Implement try-except blocks around I/O operations and critical computations.
    -   Provide informative error messages to the user.
-   **Logging:**
    -   Use Python's `logging` module with different levels (DEBUG, INFO, WARNING, ERROR).
    -   Log key processing steps, input parameters, warnings, and errors to a file.
    -   Include timestamps and module information in log entries.
-   **Input Validation:**
    -   Validate all inputs: file existence, data formats, value ranges (e.g., for calibration parameters from `config.toml`).
    -   Use libraries like `Pydantic` for validating configuration files.
        ```python
        from pydantic import BaseModel, FilePath, confloat

        class GeorefConfig(BaseModel):
            dsm_path: FilePath
            imu_accuracy_deg: confloat(gt=0, lt=1.0)
            # ... other parameters
        ```
-   **Configuration Management:**
    -   Use human-readable configuration files (e.g., TOML, YAML) for all processing parameters (e.g., [`config.toml`](config.toml)).
    -   Store default configurations and allow user overrides.
    -   Version control configurations alongside the codebase.
-   **Unit and Integration Testing:**
    -   Develop a comprehensive test suite using `pytest` or `unittest`.
    -   Include tests for individual modules (unit tests) and for the end-to-end workflow (integration tests) with sample datasets.

### Enhancing User Feedback and Data Management
-   **Processing Status and Progress:**
    -   Use progress bars (e.g., `tqdm`) for long-running operations.
    -   Provide real-time updates on the current processing stage (e.g., "Reading IMU data," "Intersecting pixel 1000/50000 with DSM").
-   **Issue Reporting:**
    -   Clearly report warnings and errors, guiding the user on potential causes and solutions.
    -   Generate a summary report of processing, including any issues encountered.
-   **Quality Assessment Feedback:**
    -   Display key accuracy metrics (e.g., estimated RMSE based on error propagation, residuals if check points are used).
    -   Visualize results where possible (e.g., plot of ray intersection points on DSM, error vectors).
-   **Input Data Management:**
    -   Clearly document required input data formats and coordinate systems.
    -   Implement checks for consistency between input datasets (e.g., temporal overlap of GPS/IMU and HSI data).
    -   Consider a project file structure or metadata standard for organizing inputs.
-   **Output Product Management:**
    -   Generate comprehensive metadata for all output products (e.g., georeferenced HSI cube, accuracy report).
    -   Include processing parameters, software version, and date in metadata (provenance).
    -   Offer outputs in standard geospatial formats (e.g., GeoTIFF with RPCs, ENVI).

By adopting these practices, a Python-based direct georeferencing tool can become more robust, maintainable, and user-friendly, leading to more reliable and understandable results.

### References (Placeholder - to be consolidated later)
[S1-1] University of Calgary PDF (INS/DGPS integration, wavelet denoising)
[S1-2] EnMAP document (physical sensor model, interior orientation, atmospheric refraction, time synchronization, boresight alignment, lever arm, ray-triangle intersection, parametric coordinates)
[S1-3] ISPRS paper (pushbroom cameras, trajectory modeling, collinearity equations)
[S1-4] MDPI special issue (advanced sensors, geometry rectification, multi-sensor coupling, DL GNSS outage, Quantum INS, automatic motion compensation, GPU parallelization, surface normals/derivatives)
[S1-5] DOT report / HySpex / PBRT (tight coupling GPS/INS, Kalman filtering, coning/sculling, collinearity equations, ray-triangle intersection, sphere intersection, spatial indexing, occlusion management, surface normals/derivatives)

[S2-2] EnMAP document / NASA Paper (ray-triangle intersection, parametric coordinates, adaptive sampling)
[S2-4] MDPI / PBRT (GPU parallelization, surface normals/derivatives, frame sensor optimization)
[S2-5] HySpex / PBRT (sphere intersection, spatial indexing, occlusion management, hierarchical acceleration)

[S3-1] Lichti & Gordon (TLS errors, Monte Carlo, semivariogram, calibration impact example)
[S3-2] EnMAP / General Photogrammetry (DSM error impact, covariance propagation)
[S3-3] Skaloud (system calibration limits, boresight impact)
[S3-4] Error analysis paper (covariance, ICPs, error budget, GPS/IMU error impact, lever arm impact, GCP validation example)
[S3-5] HySpex / LiDAR (temporal sync errors, LiDAR validation)

[S4-1] Spectral/Radiometric Calibration Paper (spectral/radiometric first, in-flight natural targets, integrated GCP/LiDAR)
[S4-2] Target ID Paper (importance of accurate calibration, integrated software PARGE/ENVI)
[S4-3] HSI Calibration Patent (dynamic compensation for flexure)
[S4-4] Line-scan Calibration Methods Paper (calibration patterns, grid for distortion, lab boresight, integrated GCP/LiDAR, example study)
[S4-5] Sensor Calibration Overview (integrating spheres, defocused surfaces, recalibration frequency, integrated software)

[S5-3] ncollpyde source (Parry bindings)
[S5-4] point-cloud-utils source (Embree bindings, example code)
[S5-5] GPU ray intersection paper / Embree info (CUDA implementation, performance metrics)

[S6-1] Pitfall/Mitigation Source 1 (Misalignment, gravity fields, projection distortions)
[S6-2] Pitfall/Mitigation Source 2 (GPS network solutions)
[S6-4] Pitfall/Mitigation Source 4 (Projection distortions, bundle adjustment, EOPs)
[S6-5] Pitfall/Mitigation Source 5 (Accelerometer errors, gravity, high-accuracy gyros)


*(Further findings will be added below)*