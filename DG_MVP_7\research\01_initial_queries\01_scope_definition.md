# Research Scope Definition: Improving Direct Georeferencing for HSI Linescan Cameras

## 1. Project Goal

The primary goal of this research is to identify and evaluate advanced techniques, algorithms, and best practices for improving the accuracy, robustness, and performance of a direct georeferencing tool for airborne Hyperspectral Imaging (HSI) linescan camera data. The existing Python-based tool utilizes GPS/IMU data, a sensor model, and a Digital Surface Model (DSM) for georeferencing.

## 2. Key Research Areas

The research will focus on the following key areas:

1.  **Advanced Direct Georeferencing Techniques:** Explore state-of-the-art methods for direct georeferencing of airborne linescan imagery, beyond basic collinearity equations, considering dynamic sensor orientation and platform movements.
2.  **Robust Ray-DSM Intersection Algorithms:** Investigate and compare algorithms for accurately and efficiently calculating the intersection of each pixel's line-of-sight (LOS) with a DSM. This includes handling complex terrain, occlusions, and performance optimization strategies.
3.  **Error Propagation and Accuracy Assessment:** Understand how errors from various sources (GPS/IMU, sensor model, DSM, calibration parameters) propagate through the georeferencing chain and how to rigorously assess the final product accuracy.
4.  **Sensor Model Calibration and System Parameter Correction:** Research best practices for:
    *   Sensor model calibration (interior orientation parameters).
    *   Boresight alignment (angular relationship between sensor and IMU).
    *   Lever arm correction (spatial offset between GPS antenna, IMU, and sensor).
5.  **Python Libraries and Algorithms:** Identify relevant Python libraries, algorithms, or computational geometry techniques that can enhance the current implementation, particularly the ray-DSM intersection logic (e.g., in `georeference_hsi_pixels.py`).
6.  **Tool Stability and Usability:** Investigate methods to improve the overall stability, reliability, and user-friendliness of the georeferencing tool.

## 3. Out of Scope

*   Fundamental changes to the existing core data processing steps (data consolidation, synchronization) are out of scope. The focus is on the georeferencing algorithm itself.
*   Development of a completely new software tool from scratch. The aim is to improve the existing MVP.
*   Indirect georeferencing techniques using Ground Control Points (GCPs) as the primary method, although their use for validation or calibration refinement might be considered.

## 4. Deliverables

The primary deliverable will be a structured markdown document summarizing:
*   Findings from the research.
*   Best practices identified.
*   Actionable recommendations for improving the existing MVP tool.
*   Relevant citations and references.

This research will follow a recursive self-learning approach, iteratively refining queries and deepening understanding based on initial findings.