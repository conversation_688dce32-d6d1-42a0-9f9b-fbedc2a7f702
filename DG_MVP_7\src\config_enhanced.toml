# Enhanced Configuration for HSI Direct Georeferencing Tool (LS1)
# This configuration implements all requirements from prompts LS1_CONFIG_* series

# Global project information
project_name = "HSI Direct Georeferencing Enhanced (LS1)"

[paths]
# Base directories (relative to project root or absolute paths)
hsi_data_directory = "data/HSI/"
webodm_data_directory = "data/WebODM/"
output_directory = "output/"
plot_output_directory = "plots/"

# HSI specific file names or relative paths (relative to hsi_data_directory)
hsi_base_filename = "2025-05-15_08-28-48_cont"
sensor_model_file = "Sensormodel_HAIP_BlackBirdV2_No6_20m.txt"

# WebODM specific file names or relative paths (relative to webodm_data_directory)
shots_geojson_file = "shots.geojson"
haip_files_subdirectory = "haip_files/"
dsm_file = "data/WebODM/dsm.tif"

# Output file names (saved in output_directory)
consolidated_webodm_poses_csv = "webodm_poses_consolidated.csv"
hsi_poses_csv = "hsi_poses.csv"
georeferenced_pixels_csv = "georeferenced_pixels_enhanced.csv"
georeferenced_rgb_tif = "georeferenced_rgb_enhanced.tif"

[sensor_model.interior_orientation]
# Interior Orientation Parameters (IOPs) for accurate ray generation
focal_length_mm = 35.0                    # Focal length of the sensor in millimeters
pixel_size_um = 4.5                       # Pixel size in micrometers
principal_point_x_mm = 0.01               # Principal point x-coordinate offset from sensor center (mm)
principal_point_y_mm = -0.005             # Principal point y-coordinate offset from sensor center (mm)

# Lens distortion coefficients (Brown's distortion model)
k1 = 1.2e-5                               # Radial distortion coefficient k1
k2 = -3.4e-8                              # Radial distortion coefficient k2
k3 = 0.0                                  # Radial distortion coefficient k3
p1 = 7.6e-7                               # Tangential distortion coefficient p1
p2 = -2.1e-7                              # Tangential distortion coefficient p2

[sensor_model.boresight_alignment_deg]
# Boresight alignment parameters (angular offsets from IMU body frame to sensor frame)
# Rotation order: ZYX (Yaw-Pitch-Roll)
# These angles define the rotation FROM the IMU body frame TO the sensor frame
roll_offset_deg = 0.1                     # Roll offset in degrees
pitch_offset_deg = -1.35                  # Pitch offset in degrees
yaw_offset_deg = 0.0                      # Yaw offset in degrees

[sensor_model.lever_arms_meters]
# Lever arm components in meters, defined in body-fixed coordinate system (FRD - Front-Right-Down)

# GNSS antenna phase center to IMU reference point
gnss_to_imu_x_m = 0.0                     # X component (forward) of GNSS to IMU lever arm
gnss_to_imu_y_m = 0.0                     # Y component (right) of GNSS to IMU lever arm
gnss_to_imu_z_m = 0.0                     # Z component (down) of GNSS to IMU lever arm

# IMU reference point to sensor perspective center
imu_to_sensor_x_m = 0.0                   # X component (forward) of IMU to Sensor lever arm
imu_to_sensor_y_m = 0.0                   # Y component (right) of IMU to Sensor lever arm
imu_to_sensor_z_m = 0.0                   # Z component (down) of IMU to Sensor lever arm

[sensor_model.uncertainties]
# Uncertainty parameters for error propagation (LS1_GEO_ERROR_PROPAGATION)
iop_focal_length_uncertainty_mm = 0.1     # Focal length uncertainty in millimeters
iop_principal_point_uncertainty_mm = 0.01 # Principal point uncertainty in millimeters
boresight_uncertainty_deg = 0.01          # Boresight alignment uncertainty in degrees
lever_arm_uncertainty_m = 0.005           # Lever arm uncertainty in meters

[dsm_parameters]
# Digital Surface Model configuration (LS1_CONFIG_DSM)
path = "data/WebODM/dsm.tif"              # Path to the DSM file
type = "raster"                           # DSM type: "raster" for GeoTIFF, "tin" for Triangular Irregular Network
default_vertical_uncertainty_m = 0.5      # Default vertical uncertainty of the DSM in meters
nodata_value = -9999.0                    # No-data value used in the DSM raster

# Ray-DSM intersection parameters (moved from parameters.georeferencing for better organization)
ray_dsm_max_search_dist_m = 2000.0        # Maximum search distance along the ray in meters
ray_dsm_step_m = 5.0                      # Initial step size for ray marching in meters
ray_dsm_bisection_tolerance_m = 0.01      # Tolerance for the bisection method in meters

[processing_options]
# General processing options (LS1_CONFIG_PROCESSING)
log_level = "INFO"                        # Logging verbosity: "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"
enable_atmospheric_correction = false     # Enable atmospheric refraction correction (advanced feature)
coordinate_reference_system_epsg_output = 32632  # Default output EPSG code (UTM Zone 32N, WGS84)

[parameters.webodm_consolidation]
# WebODM consolidation parameters
haip_timestamp_key = "rgb"                # Key for timestamp in .haip files

[parameters.georeferencing]
# Georeferencing-specific parameters
scale_vinkel_x = 1.0                      # Scale factor for vinkel_x sensor model correction
offset_vinkel_x = 0.0                     # Offset for vinkel_x sensor model correction

# Z ground calculation method
z_ground_calculation_method = "dsm_intersection"  # Options: "avg_pose_z_minus_offset", "fixed_value", "dsm_intersection"
z_ground_offset_meters = 20.0             # Offset in meters (used with "avg_pose_z_minus_offset")
z_ground_fixed_value_meters = 100.0       # Fixed Z value in meters (used with "fixed_value")

[parameters.rgb_geotiff_creation]
# RGB GeoTIFF creation parameters (LS1_RGB_QUALITY_LAYERS)
target_wavelength_R_nm = 800.0            # Target wavelength for red band in nanometers
target_wavelength_G_nm = 700.0            # Target wavelength for green band in nanometers
target_wavelength_B_nm = 550.0            # Target wavelength for blue band in nanometers

target_resolution_meters = 0.1            # Target resolution for the GeoTIFF in meters
output_epsg_code = 32632                  # EPSG code for the output coordinate reference system

# Normalization method for RGB bands
normalization_method = "min_max"          # Options: "min_max", "percentile_2_98"

# Quality layer generation (LS1_RGB_QUALITY_LAYERS)
generate_uncertainty_layer = true         # Generate uncertainty layer as separate GeoTIFF

[parameters.plotting]
# Plotting parameters (placeholder for future enhancements)
# Future options could include:
# create_position_plot = true
# create_trajectory_plot = true
# create_uncertainty_plot = true
