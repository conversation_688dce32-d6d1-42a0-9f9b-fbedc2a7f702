"""
DSM Manager for Efficient Ray-DSM Intersection

This module implements a DSMManager class that handles Digital Surface Model loading,
tiling, spatial indexing, and provides efficient ray-DSM intersection capabilities.

Implements requirements from prompts:
- LS1_GEO_RAYDSM_INTEGRATION
- LS1_GEO_RAYDSM_TILING
"""

import numpy as np
import rasterio
from pathlib import Path
from typing import Optional, Tuple, Union, List, Dict
from scipy.interpolate import RegularGridInterpolator
from scipy.optimize import brentq
import logging

try:
    from rtree import index
    RTREE_AVAILABLE = True
except ImportError:
    RTREE_AVAILABLE = False
    logging.warning("rtree library not available. Spatial indexing will be disabled.")

from .config_models import Config

logger = logging.getLogger(__name__)


class DSMTile:
    """Represents a single DSM tile with its data and bounds."""
    
    def __init__(self, tile_id: str, bounds: rasterio.coords.BoundingBox, 
                 data: np.ndarray, transform: rasterio.Affine, nodata_value: Optional[float] = None):
        """
        Initialize DSM tile.
        
        Args:
            tile_id: Unique identifier for the tile
            bounds: Bounding box of the tile
            data: DSM elevation data
            transform: Affine transformation
            nodata_value: No-data value
        """
        self.tile_id = tile_id
        self.bounds = bounds
        self.data = data
        self.transform = transform
        self.nodata_value = nodata_value
        
        # Create interpolator for this tile
        self._create_interpolator()
    
    def _create_interpolator(self):
        """Create interpolator for this tile."""
        # Create coordinate arrays
        height, width = self.data.shape
        x_coords = np.array([self.transform.c + self.transform.a * (i + 0.5) for i in range(width)])
        y_coords_raw = np.array([self.transform.f + self.transform.e * (i + 0.5) for i in range(height)])
        
        # Handle different Y coordinate orientations
        if self.transform.e < 0:
            y_coords = y_coords_raw[::-1]
            data_for_interp = self.data[::-1, :]
        else:
            y_coords = y_coords_raw
            data_for_interp = self.data.copy()
        
        # Handle no-data values
        if self.nodata_value is not None and not np.isnan(self.nodata_value):
            nodata_mask = np.isclose(data_for_interp, self.nodata_value)
            data_for_interp[nodata_mask] = np.nan
        
        self.interpolator = RegularGridInterpolator(
            (y_coords, x_coords), data_for_interp,
            method='linear', bounds_error=False, fill_value=np.nan
        )
    
    def get_elevation(self, x: float, y: float) -> float:
        """
        Get elevation at given coordinates.
        
        Args:
            x: X coordinate
            y: Y coordinate
            
        Returns:
            Elevation value or NaN if outside bounds or no-data
        """
        if not (self.bounds.left <= x <= self.bounds.right and 
                self.bounds.bottom <= y <= self.bounds.top):
            return np.nan
        
        return float(self.interpolator((y, x)))


class DSMManager:
    """
    Manages Digital Surface Model data with tiling and spatial indexing support.
    
    This class provides efficient access to DSM data for ray intersection calculations,
    supporting both small DSMs (loaded entirely) and large DSMs (tiled approach).
    """
    
    def __init__(self, config: Config):
        """
        Initialize DSM Manager.
        
        Args:
            config: Configuration object containing DSM parameters
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        self.dsm_path = Path(config.dsm_parameters.path)
        self.dsm_type = config.dsm_parameters.type
        self.nodata_value = config.dsm_parameters.nodata_value
        self.default_uncertainty = config.dsm_parameters.default_vertical_uncertainty_m
        
        # Ray intersection parameters
        self.max_search_dist = config.dsm_parameters.ray_dsm_max_search_dist_m
        self.initial_step = config.dsm_parameters.ray_dsm_step_m
        self.tolerance = config.dsm_parameters.ray_dsm_bisection_tolerance_m
        
        # DSM data storage
        self.tiles: Dict[str, DSMTile] = {}
        self.bounds: Optional[rasterio.coords.BoundingBox] = None
        self.spatial_index = None
        
        # Load DSM
        self._load_dsm()
    
    def _load_dsm(self):
        """Load DSM data and create tiles if necessary."""
        if self.dsm_type == "raster":
            self._load_raster_dsm()
        elif self.dsm_type == "tin":
            self._load_tin_dsm()
        else:
            raise ValueError(f"Unsupported DSM type: {self.dsm_type}")
    
    def _load_raster_dsm(self):
        """Load raster DSM and create tiles."""
        try:
            with rasterio.open(self.dsm_path) as src:
                self.bounds = src.bounds
                dsm_array = src.read(1).astype(np.float32)
                transform = src.transform
                
                # Get nodata value from file if not specified in config
                if self.nodata_value is None and src.nodatavals[0] is not None:
                    self.nodata_value = float(src.nodatavals[0])
                
                self.logger.info(f"Loaded DSM: {dsm_array.shape}, bounds: {self.bounds}")
                self.logger.info(f"NoData value: {self.nodata_value}")
                
                # Determine if we should tile the DSM
                dsm_size_mb = dsm_array.nbytes / (1024 * 1024)
                if dsm_size_mb > 500:  # Tile if larger than 500MB
                    self.logger.info(f"DSM size ({dsm_size_mb:.1f}MB) is large. Creating tiles...")
                    self._create_tiles(dsm_array, transform)
                else:
                    self.logger.info(f"DSM size ({dsm_size_mb:.1f}MB) is manageable. Loading as single tile...")
                    # Create single tile for entire DSM
                    tile = DSMTile("main", self.bounds, dsm_array, transform, self.nodata_value)
                    self.tiles["main"] = tile
                
        except Exception as e:
            self.logger.error(f"Failed to load raster DSM from {self.dsm_path}: {e}")
            raise
    
    def _create_tiles(self, dsm_array: np.ndarray, transform: rasterio.Affine, tile_size: int = 1024):
        """
        Create tiles from large DSM array.
        
        Args:
            dsm_array: Full DSM array
            transform: Affine transformation
            tile_size: Size of each tile in pixels
        """
        height, width = dsm_array.shape
        
        # Create spatial index if available
        if RTREE_AVAILABLE:
            self.spatial_index = index.Index()
        
        tile_count = 0
        for row_start in range(0, height, tile_size):
            for col_start in range(0, width, tile_size):
                row_end = min(row_start + tile_size, height)
                col_end = min(col_start + tile_size, width)
                
                # Extract tile data
                tile_data = dsm_array[row_start:row_end, col_start:col_end]
                
                # Calculate tile bounds
                left = transform.c + transform.a * col_start
                right = transform.c + transform.a * col_end
                top = transform.f + transform.e * row_start
                bottom = transform.f + transform.e * row_end
                
                # Ensure correct order for bounds
                if transform.e < 0:  # Y decreases with row
                    top, bottom = bottom, top
                
                tile_bounds = rasterio.coords.BoundingBox(left, bottom, right, top)
                
                # Create tile transform
                tile_transform = rasterio.Affine(
                    transform.a, transform.b, left,
                    transform.d, transform.e, top
                )
                
                # Create tile
                tile_id = f"tile_{row_start}_{col_start}"
                tile = DSMTile(tile_id, tile_bounds, tile_data, tile_transform, self.nodata_value)
                self.tiles[tile_id] = tile
                
                # Add to spatial index
                if self.spatial_index is not None:
                    self.spatial_index.insert(tile_count, (left, bottom, right, top))
                
                tile_count += 1
        
        self.logger.info(f"Created {tile_count} tiles from DSM")
    
    def _load_tin_dsm(self):
        """Load TIN DSM (placeholder for future implementation)."""
        raise NotImplementedError("TIN DSM support not yet implemented")
    
    def get_relevant_tiles(self, x: float, y: float, buffer: float = 100.0) -> List[DSMTile]:
        """
        Get DSM tiles that are relevant for a given point.
        
        Args:
            x: X coordinate
            y: Y coordinate
            buffer: Buffer distance around point
            
        Returns:
            List of relevant DSM tiles
        """
        if len(self.tiles) == 1:
            # Single tile case
            return list(self.tiles.values())
        
        if self.spatial_index is not None:
            # Use spatial index
            query_bounds = (x - buffer, y - buffer, x + buffer, y + buffer)
            tile_indices = list(self.spatial_index.intersection(query_bounds))
            tile_ids = [f"tile_{i//1000}_{i%1000}" for i in tile_indices]  # Simplified mapping
            return [self.tiles[tid] for tid in tile_ids if tid in self.tiles]
        else:
            # Brute force search
            relevant_tiles = []
            for tile in self.tiles.values():
                if (tile.bounds.left - buffer <= x <= tile.bounds.right + buffer and
                    tile.bounds.bottom - buffer <= y <= tile.bounds.top + buffer):
                    relevant_tiles.append(tile)
            return relevant_tiles
    
    def get_elevation(self, x: float, y: float) -> float:
        """
        Get elevation at given coordinates.
        
        Args:
            x: X coordinate
            y: Y coordinate
            
        Returns:
            Elevation value or NaN if not available
        """
        relevant_tiles = self.get_relevant_tiles(x, y)
        
        for tile in relevant_tiles:
            elevation = tile.get_elevation(x, y)
            if not np.isnan(elevation):
                return elevation
        
        return np.nan
    
    def calculate_ray_dsm_intersection(self, ray_origin: np.ndarray, 
                                     ray_direction: np.ndarray) -> Tuple[float, float, float]:
        """
        Calculate intersection of ray with DSM using enhanced algorithm.
        
        Args:
            ray_origin: Ray origin point [x, y, z]
            ray_direction: Normalized ray direction vector [dx, dy, dz]
            
        Returns:
            Intersection point [x, y, z] or (NaN, NaN, NaN) if no intersection
        """
        def elevation_difference(t: float) -> float:
            """Calculate difference between ray height and DSM elevation at parameter t."""
            point = ray_origin + t * ray_direction
            x, y, z_ray = point[0], point[1], point[2]
            z_dsm = self.get_elevation(x, y)
            
            if np.isnan(z_dsm):
                return z_ray - (ray_origin[2] - 10000)  # Large negative value
            return z_ray - z_dsm
        
        # Initial search to find DSM coverage
        t_current = 0.0
        step = self.initial_step
        
        # Find entry point into DSM coverage
        while t_current < self.max_search_dist:
            point = ray_origin + t_current * ray_direction
            z_dsm = self.get_elevation(point[0], point[1])
            
            if not np.isnan(z_dsm):
                break
            
            t_current += step
        
        if t_current >= self.max_search_dist:
            return np.nan, np.nan, np.nan
        
        # Ray marching to find intersection
        diff_prev = elevation_difference(t_current)
        t_prev = t_current
        
        while t_current < self.max_search_dist:
            t_current += step
            diff_curr = elevation_difference(t_current)
            
            # Check for sign change (intersection)
            if diff_prev * diff_curr <= 0:
                try:
                    # Use bisection to refine intersection
                    t_intersect = brentq(elevation_difference, t_prev, t_current, 
                                       xtol=self.tolerance, rtol=self.tolerance)
                    intersection_point = ray_origin + t_intersect * ray_direction
                    return intersection_point[0], intersection_point[1], intersection_point[2]
                except (ValueError, RuntimeError):
                    # Bisection failed, continue searching
                    pass
            
            diff_prev = diff_curr
            t_prev = t_current
        
        return np.nan, np.nan, np.nan
    
    def get_uncertainty_at_point(self, x: float, y: float) -> float:
        """
        Get vertical uncertainty at given point.
        
        Args:
            x: X coordinate
            y: Y coordinate
            
        Returns:
            Vertical uncertainty in meters
        """
        # For now, return default uncertainty
        # In future, this could be based on DSM metadata or local terrain characteristics
        return self.default_uncertainty or 0.5
