# Requirements for HSI Direct Georeferencing Tool - Enhanced Version (LS1)

# Core scientific computing
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# Geospatial libraries
rasterio>=1.2.0
pyproj>=3.2.0

# Configuration and validation
pydantic>=1.8.0
toml>=0.10.0

# Machine learning (for nearest neighbor search)
scikit-learn>=1.0.0

# Spatial transformations
Pillow>=8.0.0

# Testing framework
pytest>=6.0.0
pytest-cov>=2.12.0

# Optional dependencies for enhanced features
# Uncomment if needed:

# For spatial indexing (DSM tiling)
# rtree>=0.9.0

# For advanced ray-DSM intersection (future enhancement)
# point-cloud-utils>=0.18.0

# For mesh processing (future enhancement)
# trimesh>=3.9.0
# PyVista>=0.32.0

# For parallel processing (future enhancement)
# joblib>=1.0.0
# dask>=2021.0.0

# For advanced plotting (future enhancement)
# matplotlib>=3.4.0
# plotly>=5.0.0
# bokeh>=2.4.0

# Development dependencies
# black>=21.0.0
# flake8>=3.9.0
# mypy>=0.910
