"""
Unit tests for configuration loading and validation.

Tests the Pydantic models and configuration loading functionality.
"""

import pytest
import tempfile
import toml
from pathlib import Path
from unittest.mock import patch

from ..config_models import (
    Config, 
    load_and_validate_config,
    InteriorOrientationConfig,
    BoresightAlignmentDegConfig,
    LeverArmsMetersConfig,
    DSMParametersConfig,
    ProcessingOptions
)


class TestConfigModels:
    """Test Pydantic configuration models."""
    
    def test_interior_orientation_config_valid(self):
        """Test valid interior orientation configuration."""
        config_data = {
            'focal_length_mm': 35.0,
            'pixel_size_um': 4.5,
            'principal_point_x_mm': 0.01,
            'principal_point_y_mm': -0.005,
            'k1': 1.2e-5,
            'k2': -3.4e-8,
            'k3': 0.0,
            'p1': 7.6e-7,
            'p2': -2.1e-7
        }
        
        iop_config = InteriorOrientationConfig(**config_data)
        assert iop_config.focal_length_mm == 35.0
        assert iop_config.pixel_size_um == 4.5
        assert iop_config.k1 == 1.2e-5
    
    def test_boresight_alignment_config_valid(self):
        """Test valid boresight alignment configuration."""
        config_data = {
            'roll_offset_deg': 0.1,
            'pitch_offset_deg': -1.35,
            'yaw_offset_deg': 0.0
        }
        
        boresight_config = BoresightAlignmentDegConfig(**config_data)
        assert boresight_config.roll_offset_deg == 0.1
        assert boresight_config.pitch_offset_deg == -1.35
        assert boresight_config.yaw_offset_deg == 0.0
    
    def test_lever_arms_config_valid(self):
        """Test valid lever arms configuration."""
        config_data = {
            'gnss_to_imu_x_m': 0.1,
            'gnss_to_imu_y_m': -0.05,
            'gnss_to_imu_z_m': -0.2,
            'imu_to_sensor_x_m': 0.02,
            'imu_to_sensor_y_m': 0.01,
            'imu_to_sensor_z_m': 0.15
        }
        
        lever_config = LeverArmsMetersConfig(**config_data)
        assert lever_config.gnss_to_imu_x_m == 0.1
        assert lever_config.imu_to_sensor_z_m == 0.15
    
    def test_dsm_parameters_config_valid(self):
        """Test valid DSM parameters configuration."""
        with tempfile.NamedTemporaryFile(suffix='.tif', delete=False) as tmp_file:
            tmp_path = tmp_file.name
        
        try:
            config_data = {
                'path': tmp_path,
                'type': 'raster',
                'default_vertical_uncertainty_m': 0.5,
                'nodata_value': -9999.0,
                'ray_dsm_max_search_dist_m': 2000.0,
                'ray_dsm_step_m': 5.0,
                'ray_dsm_bisection_tolerance_m': 0.01
            }
            
            dsm_config = DSMParametersConfig(**config_data)
            assert dsm_config.type == 'raster'
            assert dsm_config.default_vertical_uncertainty_m == 0.5
        finally:
            Path(tmp_path).unlink(missing_ok=True)
    
    def test_dsm_parameters_invalid_type(self):
        """Test DSM parameters with invalid type."""
        with tempfile.NamedTemporaryFile(suffix='.tif', delete=False) as tmp_file:
            tmp_path = tmp_file.name
        
        try:
            config_data = {
                'path': tmp_path,
                'type': 'invalid_type',  # Invalid type
                'ray_dsm_max_search_dist_m': 2000.0,
                'ray_dsm_step_m': 5.0,
                'ray_dsm_bisection_tolerance_m': 0.01
            }
            
            with pytest.raises(ValueError, match='DSM type must be'):
                DSMParametersConfig(**config_data)
        finally:
            Path(tmp_path).unlink(missing_ok=True)
    
    def test_processing_options_valid(self):
        """Test valid processing options."""
        config_data = {
            'log_level': 'INFO',
            'enable_atmospheric_correction': False,
            'coordinate_reference_system_epsg_output': 32632
        }
        
        proc_config = ProcessingOptions(**config_data)
        assert proc_config.log_level == 'INFO'
        assert proc_config.enable_atmospheric_correction is False
        assert proc_config.coordinate_reference_system_epsg_output == 32632
    
    def test_processing_options_invalid_log_level(self):
        """Test processing options with invalid log level."""
        config_data = {
            'log_level': 'INVALID',  # Invalid log level
            'enable_atmospheric_correction': False,
            'coordinate_reference_system_epsg_output': 32632
        }
        
        with pytest.raises(ValueError, match='log_level must be one of'):
            ProcessingOptions(**config_data)


class TestConfigLoading:
    """Test configuration file loading and validation."""
    
    def test_load_valid_config(self):
        """Test loading a valid configuration file."""
        # Create a minimal valid configuration
        config_data = {
            'project_name': 'Test Project',
            'paths': {
                'hsi_data_directory': 'data/HSI/',
                'webodm_data_directory': 'data/WebODM/',
                'output_directory': 'output/',
                'plot_output_directory': 'plots/',
                'hsi_base_filename': 'test_hsi',
                'sensor_model_file': 'sensor_model.txt',
                'shots_geojson_file': 'shots.geojson',
                'haip_files_subdirectory': 'haip_files/',
                'dsm_file': 'dsm.tif',
                'consolidated_webodm_poses_csv': 'poses.csv',
                'hsi_poses_csv': 'hsi_poses.csv',
                'georeferenced_pixels_csv': 'georef.csv',
                'georeferenced_rgb_tif': 'rgb.tif'
            },
            'sensor_model': {
                'interior_orientation': {
                    'focal_length_mm': 35.0,
                    'pixel_size_um': 4.5,
                    'principal_point_x_mm': 0.0,
                    'principal_point_y_mm': 0.0,
                    'k1': 0.0, 'k2': 0.0, 'k3': 0.0, 'p1': 0.0, 'p2': 0.0
                },
                'boresight_alignment_deg': {
                    'roll_offset_deg': 0.0,
                    'pitch_offset_deg': 0.0,
                    'yaw_offset_deg': 0.0
                },
                'lever_arms_meters': {
                    'gnss_to_imu_x_m': 0.0, 'gnss_to_imu_y_m': 0.0, 'gnss_to_imu_z_m': 0.0,
                    'imu_to_sensor_x_m': 0.0, 'imu_to_sensor_y_m': 0.0, 'imu_to_sensor_z_m': 0.0
                }
            },
            'dsm_parameters': {
                'path': 'dsm.tif',
                'type': 'raster',
                'ray_dsm_max_search_dist_m': 2000.0,
                'ray_dsm_step_m': 5.0,
                'ray_dsm_bisection_tolerance_m': 0.01
            },
            'parameters': {
                'webodm_consolidation': {'haip_timestamp_key': 'rgb'},
                'georeferencing': {
                    'scale_vinkel_x': 1.0,
                    'offset_vinkel_x': 0.0,
                    'z_ground_calculation_method': 'fixed_value'
                },
                'rgb_geotiff_creation': {
                    'target_wavelength_R_nm': 800.0,
                    'target_wavelength_G_nm': 700.0,
                    'target_wavelength_B_nm': 550.0,
                    'target_resolution_meters': 0.1,
                    'output_epsg_code': 32632,
                    'normalization_method': 'min_max',
                    'generate_uncertainty_layer': False
                },
                'plotting': {}
            },
            'processing_options': {
                'log_level': 'INFO',
                'enable_atmospheric_correction': False,
                'coordinate_reference_system_epsg_output': 32632
            }
        }
        
        # Write to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as tmp_file:
            toml.dump(config_data, tmp_file)
            tmp_path = tmp_file.name
        
        try:
            # Test loading
            config = load_and_validate_config(tmp_path)
            assert config is not None
            assert config.project_name == 'Test Project'
            assert config.sensor_model.interior_orientation.focal_length_mm == 35.0
            assert config.processing_options.log_level == 'INFO'
        finally:
            Path(tmp_path).unlink(missing_ok=True)
    
    def test_load_nonexistent_config(self):
        """Test loading a non-existent configuration file."""
        config = load_and_validate_config('nonexistent_file.toml')
        assert config is None
    
    def test_load_invalid_config(self):
        """Test loading an invalid configuration file."""
        # Create invalid configuration (missing required fields)
        config_data = {
            'project_name': 'Test Project'
            # Missing all required sections
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as tmp_file:
            toml.dump(config_data, tmp_file)
            tmp_path = tmp_file.name
        
        try:
            config = load_and_validate_config(tmp_path)
            assert config is None
        finally:
            Path(tmp_path).unlink(missing_ok=True)


def test_placeholder():
    """Placeholder test to ensure pytest runs."""
    assert True
