"""
Enhanced RGB GeoTIFF Creation Module

This module implements enhanced RGB GeoTIFF creation with quality layers,
comprehensive metadata, and improved orthorectification capabilities.

Implements requirements from prompts:
- LS1_RGB_ACCURACY
- LS1_RGB_ORTHORECTIFICATION
- LS1_RGB_QUALITY_LAYERS
- LS1_RGB_METADATA
- LS1_RGB_LOGGING
"""

import numpy as np
import pandas as pd
import rasterio
from rasterio.transform import from_bounds
from rasterio.crs import CRS
from pathlib import Path
from typing import Optional, Tuple, Dict, Union
from sklearn.neighbors import KDTree
import logging
from datetime import datetime

from .config_models import Config, load_and_validate_config
from .utils import setup_logger, validate_file_exists

logger = logging.getLogger(__name__)


class RGBGeoTIFFCreator:
    """
    Enhanced RGB GeoTIFF creator with quality layers and comprehensive metadata.
    """
    
    def __init__(self, config: Union[str, Path, Config]):
        """
        Initialize RGB GeoTIFF creator.
        
        Args:
            config: Configuration file path or Config object
        """
        # Load and validate configuration
        if isinstance(config, (str, Path)):
            self.config = load_and_validate_config(config)
            if self.config is None:
                raise ValueError("Failed to load and validate configuration")
        else:
            self.config = config
        
        # Setup logging
        self.logger = setup_logger(
            f"{__name__}.{self.__class__.__name__}",
            self.config.processing_options.log_level
        )
        
        self.logger.info("RGBGeoTIFFCreator initialized")
    
    def validate_inputs(self) -> bool:
        """
        Validate input files and parameters.
        
        Returns:
            True if all inputs are valid, False otherwise
        """
        self.logger.info("Validating inputs for RGB GeoTIFF creation...")
        
        # Check georeferenced pixels file
        output_dir = Path(self.config.paths.output_directory)
        georef_file = output_dir / self.config.paths.georeferenced_pixels_csv
        
        if not validate_file_exists(georef_file, "Georeferenced pixels CSV"):
            return False
        
        # Check HSI data file
        base_hsi_dir = Path(self.config.paths.hsi_data_directory)
        hsi_file = base_hsi_dir / f"{self.config.paths.hsi_base_filename}.img"
        
        if not validate_file_exists(hsi_file, "HSI data file"):
            return False
        
        # Check HSI header file
        hdr_file = base_hsi_dir / f"{self.config.paths.hsi_base_filename}.hdr"
        
        if not validate_file_exists(hdr_file, "HSI header file"):
            return False
        
        self.logger.info("Input validation completed successfully")
        return True
    
    def load_georeferenced_data(self) -> Optional[pd.DataFrame]:
        """
        Load georeferenced pixel data.
        
        Returns:
            DataFrame with georeferenced pixel data or None if loading fails
        """
        try:
            output_dir = Path(self.config.paths.output_directory)
            georef_file = output_dir / self.config.paths.georeferenced_pixels_csv
            
            self.logger.info(f"Loading georeferenced data from: {georef_file}")
            georef_df = pd.read_csv(georef_file)
            
            # Validate required columns
            required_columns = ['hsi_line_index', 'pixel_index', 'X_ground', 'Y_ground']
            missing_columns = [col for col in required_columns if col not in georef_df.columns]
            
            if missing_columns:
                self.logger.error(f"Missing required columns: {missing_columns}")
                return None
            
            # Remove invalid coordinates
            valid_mask = (
                ~georef_df['X_ground'].isna() & 
                ~georef_df['Y_ground'].isna() &
                np.isfinite(georef_df['X_ground']) &
                np.isfinite(georef_df['Y_ground'])
            )
            
            valid_df = georef_df[valid_mask].copy()
            
            self.logger.info(f"Loaded {len(georef_df)} total pixels, {len(valid_df)} valid")
            
            if len(valid_df) == 0:
                self.logger.error("No valid georeferenced pixels found")
                return None
            
            return valid_df
            
        except Exception as e:
            self.logger.error(f"Failed to load georeferenced data: {e}")
            return None
    
    def load_hsi_data(self) -> Optional[Tuple[np.ndarray, Dict]]:
        """
        Load HSI data and header information.
        
        Returns:
            Tuple of (HSI data array, header info) or None if loading fails
        """
        try:
            base_hsi_dir = Path(self.config.paths.hsi_data_directory)
            hsi_file = base_hsi_dir / f"{self.config.paths.hsi_base_filename}.img"
            hdr_file = base_hsi_dir / f"{self.config.paths.hsi_base_filename}.hdr"
            
            # Parse header file
            header_info = self._parse_hsi_header(hdr_file)
            if header_info is None:
                return None
            
            # Load HSI data
            self.logger.info(f"Loading HSI data from: {hsi_file}")
            
            with open(hsi_file, 'rb') as f:
                # Read binary data based on header info
                data_type = header_info.get('data_type', 'uint16')
                samples = header_info['samples']
                lines = header_info['lines']
                bands = header_info['bands']
                
                # Determine numpy dtype
                if data_type == 'uint16':
                    dtype = np.uint16
                elif data_type == 'float32':
                    dtype = np.float32
                else:
                    dtype = np.uint16  # Default
                
                # Read data
                hsi_data = np.frombuffer(f.read(), dtype=dtype)
                hsi_data = hsi_data.reshape((lines, samples, bands))
            
            self.logger.info(f"Loaded HSI data: {hsi_data.shape}")
            return hsi_data, header_info
            
        except Exception as e:
            self.logger.error(f"Failed to load HSI data: {e}")
            return None
    
    def _parse_hsi_header(self, hdr_file: Path) -> Optional[Dict]:
        """
        Parse HSI header file.
        
        Args:
            hdr_file: Path to header file
            
        Returns:
            Dictionary with header information or None if parsing fails
        """
        try:
            header_info = {}
            with open(hdr_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        if key in ['samples', 'lines', 'bands']:
                            header_info[key] = int(value)
                        elif key == 'data type':
                            # Map ENVI data types to numpy types
                            type_map = {
                                '2': 'int16',
                                '4': 'float32',
                                '12': 'uint16'
                            }
                            header_info['data_type'] = type_map.get(value, 'uint16')
                        elif key == 'wavelength':
                            # Parse wavelength array
                            wavelengths_str = value.strip('{}')
                            wavelengths = [float(w.strip()) for w in wavelengths_str.split(',')]
                            header_info['wavelengths'] = wavelengths
            
            # Validate required fields
            required_fields = ['samples', 'lines', 'bands']
            for field in required_fields:
                if field not in header_info:
                    self.logger.error(f"Required header field '{field}' not found")
                    return None
            
            return header_info
            
        except Exception as e:
            self.logger.error(f"Failed to parse HSI header: {e}")
            return None
    
    def select_rgb_bands(self, wavelengths: list) -> Tuple[int, int, int]:
        """
        Select RGB bands based on target wavelengths.
        
        Args:
            wavelengths: List of wavelengths for each band
            
        Returns:
            Tuple of (red_band_index, green_band_index, blue_band_index)
        """
        rgb_config = self.config.parameters.rgb_geotiff_creation
        target_r = rgb_config.target_wavelength_R_nm
        target_g = rgb_config.target_wavelength_G_nm
        target_b = rgb_config.target_wavelength_B_nm
        
        # Find closest bands
        wavelengths = np.array(wavelengths)
        
        r_band = np.argmin(np.abs(wavelengths - target_r))
        g_band = np.argmin(np.abs(wavelengths - target_g))
        b_band = np.argmin(np.abs(wavelengths - target_b))
        
        self.logger.info(f"Selected RGB bands: R={r_band}({wavelengths[r_band]:.1f}nm), "
                        f"G={g_band}({wavelengths[g_band]:.1f}nm), "
                        f"B={b_band}({wavelengths[b_band]:.1f}nm)")
        
        return r_band, g_band, b_band
    
    def determine_target_grid(self, georef_df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, rasterio.Affine]:
        """
        Determine target grid for resampling.
        
        Args:
            georef_df: Georeferenced pixel DataFrame
            
        Returns:
            Tuple of (x_coords, y_coords, transform)
        """
        # Calculate bounds
        x_min, x_max = georef_df['X_ground'].min(), georef_df['X_ground'].max()
        y_min, y_max = georef_df['Y_ground'].min(), georef_df['Y_ground'].max()
        
        # Add small buffer
        buffer = 10.0  # meters
        x_min -= buffer
        x_max += buffer
        y_min -= buffer
        y_max += buffer
        
        # Calculate grid dimensions
        resolution = self.config.parameters.rgb_geotiff_creation.target_resolution_meters
        width = int(np.ceil((x_max - x_min) / resolution))
        height = int(np.ceil((y_max - y_min) / resolution))
        
        # Create coordinate arrays
        x_coords = np.linspace(x_min, x_max, width)
        y_coords = np.linspace(y_max, y_min, height)  # Y decreases with row
        
        # Create affine transform
        transform = from_bounds(x_min, y_min, x_max, y_max, width, height)
        
        self.logger.info(f"Target grid: {width}×{height} pixels, resolution: {resolution}m")
        self.logger.info(f"Bounds: ({x_min:.1f}, {y_min:.1f}) to ({x_max:.1f}, {y_max:.1f})")
        
        return x_coords, y_coords, transform
    
    def resample_to_grid(self, georef_df: pd.DataFrame, hsi_data: np.ndarray,
                        rgb_bands: Tuple[int, int, int], x_coords: np.ndarray,
                        y_coords: np.ndarray) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """
        Resample HSI data to regular grid.
        
        Args:
            georef_df: Georeferenced pixel DataFrame
            hsi_data: HSI data array
            rgb_bands: RGB band indices
            x_coords: Target X coordinates
            y_coords: Target Y coordinates
            
        Returns:
            Tuple of (RGB array, uncertainty array if available)
        """
        self.logger.info("Resampling HSI data to target grid...")
        
        # Extract RGB data for valid pixels
        valid_pixels = []
        rgb_values = []
        uncertainty_values = []
        
        for _, row in georef_df.iterrows():
            line_idx = int(row['hsi_line_index'])
            pixel_idx = int(row['pixel_index'])
            
            if (0 <= line_idx < hsi_data.shape[0] and 
                0 <= pixel_idx < hsi_data.shape[1]):
                
                valid_pixels.append([row['X_ground'], row['Y_ground']])
                
                # Extract RGB values
                r_val = hsi_data[line_idx, pixel_idx, rgb_bands[0]]
                g_val = hsi_data[line_idx, pixel_idx, rgb_bands[1]]
                b_val = hsi_data[line_idx, pixel_idx, rgb_bands[2]]
                rgb_values.append([r_val, g_val, b_val])
                
                # Extract uncertainty if available
                if 'pos_uncertainty_m' in row:
                    uncertainty_values.append(row['pos_uncertainty_m'])
        
        if len(valid_pixels) == 0:
            self.logger.error("No valid pixels found for resampling")
            return None, None
        
        valid_pixels = np.array(valid_pixels)
        rgb_values = np.array(rgb_values)
        
        # Create KDTree for nearest neighbor search
        tree = KDTree(valid_pixels)
        
        # Create target grid
        xx, yy = np.meshgrid(x_coords, y_coords)
        target_points = np.column_stack([xx.ravel(), yy.ravel()])
        
        # Find nearest neighbors
        distances, indices = tree.query(target_points, k=1)
        
        # Resample RGB values
        resampled_rgb = rgb_values[indices.ravel()].reshape(len(y_coords), len(x_coords), 3)
        
        # Resample uncertainty values if available
        uncertainty_array = None
        if uncertainty_values:
            uncertainty_values = np.array(uncertainty_values)
            resampled_uncertainty = uncertainty_values[indices.ravel()].reshape(len(y_coords), len(x_coords))
            uncertainty_array = resampled_uncertainty
        
        self.logger.info(f"Resampling completed: {resampled_rgb.shape}")
        
        return resampled_rgb, uncertainty_array
    
    def normalize_rgb_data(self, rgb_array: np.ndarray) -> np.ndarray:
        """
        Normalize RGB data for display.
        
        Args:
            rgb_array: RGB data array
            
        Returns:
            Normalized RGB array (0-255, uint8)
        """
        method = self.config.parameters.rgb_geotiff_creation.normalization_method
        
        self.logger.info(f"Normalizing RGB data using method: {method}")
        
        normalized = np.zeros_like(rgb_array, dtype=np.uint8)
        
        for band in range(3):
            band_data = rgb_array[:, :, band].astype(float)
            
            if method == "min_max":
                min_val = np.nanmin(band_data)
                max_val = np.nanmax(band_data)
            elif method == "percentile_2_98":
                min_val = np.nanpercentile(band_data, 2)
                max_val = np.nanpercentile(band_data, 98)
            else:
                min_val = np.nanmin(band_data)
                max_val = np.nanmax(band_data)
            
            # Normalize to 0-255
            if max_val > min_val:
                normalized_band = ((band_data - min_val) / (max_val - min_val) * 255)
                normalized_band = np.clip(normalized_band, 0, 255)
                normalized[:, :, band] = normalized_band.astype(np.uint8)
            
            self.logger.debug(f"Band {band}: {min_val:.1f} - {max_val:.1f}")
        
        return normalized

    def create_metadata(self, header_info: Dict, rgb_bands: Tuple[int, int, int]) -> Dict[str, str]:
        """
        Create comprehensive metadata for GeoTIFF.

        Args:
            header_info: HSI header information
            rgb_bands: RGB band indices

        Returns:
            Dictionary with metadata tags
        """
        wavelengths = header_info.get('wavelengths', [])

        metadata = {
            'DESCRIPTION': 'Georeferenced RGB composite from HSI data',
            'PROCESSING_SOFTWARE': 'HSI Direct Georeferencing Tool v1.0.0',
            'PROCESSING_DATE': datetime.now().isoformat(),
            'SOURCE_HSI_FILE': self.config.paths.hsi_base_filename,
            'GEOREFERENCING_METHOD': 'Direct georeferencing with Ray-DSM intersection',
            'TARGET_RESOLUTION_M': str(self.config.parameters.rgb_geotiff_creation.target_resolution_meters),
            'NORMALIZATION_METHOD': self.config.parameters.rgb_geotiff_creation.normalization_method
        }

        # Add RGB band wavelengths if available
        if wavelengths and len(wavelengths) > max(rgb_bands):
            r_wl = wavelengths[rgb_bands[0]]
            g_wl = wavelengths[rgb_bands[1]]
            b_wl = wavelengths[rgb_bands[2]]
            metadata['RGB_BAND_WAVELENGTHS_NM'] = f"R:{r_wl:.1f}nm, G:{g_wl:.1f}nm, B:{b_wl:.1f}nm"

        # Add configuration summary
        config_summary = (f"Boresight: {self.config.sensor_model.boresight_alignment_deg.roll_offset_deg:.3f}°/"
                         f"{self.config.sensor_model.boresight_alignment_deg.pitch_offset_deg:.3f}°/"
                         f"{self.config.sensor_model.boresight_alignment_deg.yaw_offset_deg:.3f}°")
        metadata['CONFIG_BORESIGHT_RPY_DEG'] = config_summary

        return metadata

    def save_geotiff(self, rgb_array: np.ndarray, transform: rasterio.Affine,
                    metadata: Dict[str, str], output_path: Path) -> bool:
        """
        Save RGB array as GeoTIFF with metadata.

        Args:
            rgb_array: RGB data array
            transform: Affine transformation
            metadata: Metadata dictionary
            output_path: Output file path

        Returns:
            True if saving successful, False otherwise
        """
        try:
            self.logger.info(f"Saving RGB GeoTIFF to: {output_path}")

            # Create output directory if needed
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # Get CRS
            crs = CRS.from_epsg(self.config.parameters.rgb_geotiff_creation.output_epsg_code)

            # Save GeoTIFF
            with rasterio.open(
                output_path,
                'w',
                driver='GTiff',
                height=rgb_array.shape[0],
                width=rgb_array.shape[1],
                count=3,
                dtype=rgb_array.dtype,
                crs=crs,
                transform=transform,
                compress='lzw',
                tiled=True,
                blockxsize=256,
                blockysize=256
            ) as dst:
                # Write bands
                for band in range(3):
                    dst.write(rgb_array[:, :, band], band + 1)

                # Write metadata
                dst.update_tags(**metadata)

                # Set band descriptions
                dst.set_band_description(1, 'Red')
                dst.set_band_description(2, 'Green')
                dst.set_band_description(3, 'Blue')

            self.logger.info("RGB GeoTIFF saved successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to save RGB GeoTIFF: {e}")
            return False

    def save_uncertainty_layer(self, uncertainty_array: np.ndarray, transform: rasterio.Affine,
                              output_path: Path) -> bool:
        """
        Save uncertainty layer as single-band GeoTIFF.

        Args:
            uncertainty_array: Uncertainty data array
            transform: Affine transformation
            output_path: Output file path

        Returns:
            True if saving successful, False otherwise
        """
        try:
            self.logger.info(f"Saving uncertainty layer to: {output_path}")

            # Create output directory if needed
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # Get CRS
            crs = CRS.from_epsg(self.config.parameters.rgb_geotiff_creation.output_epsg_code)

            # Metadata for uncertainty layer
            uncertainty_metadata = {
                'DESCRIPTION': 'Georeferencing positional uncertainty (meters)',
                'PROCESSING_SOFTWARE': 'HSI Direct Georeferencing Tool v1.0.0',
                'PROCESSING_DATE': datetime.now().isoformat(),
                'UNITS': 'meters'
            }

            # Save GeoTIFF
            with rasterio.open(
                output_path,
                'w',
                driver='GTiff',
                height=uncertainty_array.shape[0],
                width=uncertainty_array.shape[1],
                count=1,
                dtype=uncertainty_array.dtype,
                crs=crs,
                transform=transform,
                compress='lzw',
                tiled=True,
                blockxsize=256,
                blockysize=256
            ) as dst:
                # Write uncertainty data
                dst.write(uncertainty_array, 1)

                # Write metadata
                dst.update_tags(**uncertainty_metadata)

                # Set band description
                dst.set_band_description(1, 'Positional Uncertainty (m)')

            self.logger.info("Uncertainty layer saved successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to save uncertainty layer: {e}")
            return False

    def create_rgb_geotiff(self) -> bool:
        """
        Main method to create RGB GeoTIFF.

        Returns:
            True if creation successful, False otherwise
        """
        try:
            self.logger.info("Starting RGB GeoTIFF creation...")

            # Step 1: Validate inputs
            if not self.validate_inputs():
                return False

            # Step 2: Load georeferenced data
            georef_df = self.load_georeferenced_data()
            if georef_df is None:
                return False

            # Step 3: Load HSI data
            hsi_result = self.load_hsi_data()
            if hsi_result is None:
                return False

            hsi_data, header_info = hsi_result

            # Step 4: Select RGB bands
            wavelengths = header_info.get('wavelengths', [])
            if not wavelengths:
                self.logger.error("No wavelength information found in HSI header")
                return False

            rgb_bands = self.select_rgb_bands(wavelengths)

            # Step 5: Determine target grid
            x_coords, y_coords, transform = self.determine_target_grid(georef_df)

            # Step 6: Resample to grid
            rgb_array, uncertainty_array = self.resample_to_grid(
                georef_df, hsi_data, rgb_bands, x_coords, y_coords
            )

            if rgb_array is None:
                return False

            # Step 7: Normalize RGB data
            normalized_rgb = self.normalize_rgb_data(rgb_array)

            # Step 8: Create metadata
            metadata = self.create_metadata(header_info, rgb_bands)

            # Step 9: Save RGB GeoTIFF
            output_dir = Path(self.config.paths.output_directory)
            rgb_output_path = output_dir / self.config.paths.georeferenced_rgb_tif

            if not self.save_geotiff(normalized_rgb, transform, metadata, rgb_output_path):
                return False

            # Step 10: Save uncertainty layer if available and requested
            if (uncertainty_array is not None and
                self.config.parameters.rgb_geotiff_creation.generate_uncertainty_layer):

                uncertainty_output_path = output_dir / "georeferenced_uncertainty.tif"
                self.save_uncertainty_layer(uncertainty_array, transform, uncertainty_output_path)

            self.logger.info("RGB GeoTIFF creation completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"RGB GeoTIFF creation failed: {e}")
            return False


def run_create_rgb_geotiff(config_or_path: Union[str, Path, Config]) -> bool:
    """
    Main entry point for RGB GeoTIFF creation.

    Args:
        config_or_path: Configuration file path or Config object

    Returns:
        True if creation successful, False otherwise
    """
    try:
        creator = RGBGeoTIFFCreator(config_or_path)
        return creator.create_rgb_geotiff()
    except Exception as e:
        logger.error(f"Failed to create RGB GeoTIFF creator: {e}")
        return False
