"""
Error Propagation Module for HSI Direct Georeferencing

This module implements error propagation functionality to estimate per-pixel
georeferencing uncertainty based on input data quality and calibration errors.

Implements requirements from prompt:
- LS1_GEO_ERROR_PROPAGATION
"""

import numpy as np
from typing import Dict, Tuple, Optional
import logging

from .config_models import Config
from .sensor_model import SensorModel

logger = logging.getLogger(__name__)


class ErrorPropagationModule:
    """
    Estimates per-pixel georeferencing uncertainty using simplified error propagation.
    
    This module combines uncertainties from various sources:
    - INS/GNSS navigation data
    - Sensor calibration parameters (IOPs, boresight, lever arms)
    - DSM vertical uncertainty
    - Atmospheric effects (if enabled)
    """
    
    def __init__(self, config: Config, sensor_model: SensorModel):
        """
        Initialize error propagation module.
        
        Args:
            config: Configuration object
            sensor_model: SensorModel instance
        """
        self.config = config
        self.sensor_model = sensor_model
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Load uncertainty parameters
        self._load_uncertainty_parameters()
        
        self.logger.info("Error propagation module initialized")
    
    def _load_uncertainty_parameters(self):
        """Load uncertainty parameters from configuration and sensor model."""
        # Sensor uncertainties
        sensor_uncertainties = self.sensor_model.get_uncertainty_parameters()
        self.focal_length_uncertainty_mm = sensor_uncertainties['focal_length_uncertainty_mm']
        self.principal_point_uncertainty_mm = sensor_uncertainties['principal_point_uncertainty_mm']
        self.boresight_uncertainty_deg = sensor_uncertainties['boresight_uncertainty_deg']
        self.lever_arm_uncertainty_m = sensor_uncertainties['lever_arm_uncertainty_m']
        
        # DSM uncertainty
        self.dsm_uncertainty_m = self.config.dsm_parameters.default_vertical_uncertainty_m or 0.5
        
        # Default navigation uncertainties (could be made configurable)
        self.position_uncertainty_m = 0.05  # Typical RTK GNSS uncertainty
        self.attitude_uncertainty_deg = 0.01  # Typical IMU attitude uncertainty
        
        self.logger.info(f"Loaded uncertainty parameters:")
        self.logger.info(f"  Position: {self.position_uncertainty_m}m")
        self.logger.info(f"  Attitude: {self.attitude_uncertainty_deg}°")
        self.logger.info(f"  Boresight: {self.boresight_uncertainty_deg}°")
        self.logger.info(f"  Lever arm: {self.lever_arm_uncertainty_m}m")
        self.logger.info(f"  DSM: {self.dsm_uncertainty_m}m")
    
    def estimate_pixel_uncertainty(self, pixel_index: int, flight_height_agl: float,
                                 slant_range: Optional[float] = None) -> Dict[str, float]:
        """
        Estimate georeferencing uncertainty for a single pixel.
        
        Args:
            pixel_index: Pixel index
            flight_height_agl: Flight height above ground level in meters
            slant_range: Slant range to ground point in meters (optional)
            
        Returns:
            Dictionary with uncertainty estimates
        """
        if slant_range is None:
            # Estimate slant range from flight height and viewing angle
            try:
                los_vector = self.sensor_model.calculate_los_vector(pixel_index)
                # Approximate slant range assuming nadir-looking geometry
                slant_range = flight_height_agl / abs(los_vector[2])
            except Exception:
                slant_range = flight_height_agl  # Fallback to vertical distance
        
        # Calculate individual uncertainty contributions
        uncertainties = {}
        
        # 1. Position uncertainty contribution
        uncertainties['position_horizontal_m'] = self.position_uncertainty_m
        uncertainties['position_vertical_m'] = self.position_uncertainty_m
        
        # 2. Attitude uncertainty contribution (angular error projected to ground)
        attitude_uncertainty_rad = np.deg2rad(self.attitude_uncertainty_deg)
        uncertainties['attitude_horizontal_m'] = slant_range * attitude_uncertainty_rad
        
        # 3. Boresight uncertainty contribution
        boresight_uncertainty_rad = np.deg2rad(self.boresight_uncertainty_deg)
        uncertainties['boresight_horizontal_m'] = slant_range * boresight_uncertainty_rad
        
        # 4. Lever arm uncertainty contribution
        uncertainties['lever_arm_horizontal_m'] = self.lever_arm_uncertainty_m
        uncertainties['lever_arm_vertical_m'] = self.lever_arm_uncertainty_m
        
        # 5. DSM uncertainty contribution
        uncertainties['dsm_vertical_m'] = self.dsm_uncertainty_m
        
        # 6. Sensor calibration uncertainties (IOPs)
        # Convert focal length uncertainty to angular uncertainty
        focal_length_angular_uncertainty_rad = (self.focal_length_uncertainty_mm / 
                                               self.sensor_model.focal_length_mm)
        uncertainties['iop_horizontal_m'] = slant_range * focal_length_angular_uncertainty_rad
        
        # Principal point uncertainty
        pixel_size_mm = self.sensor_model.pixel_size_um / 1000.0
        principal_point_angular_uncertainty_rad = (self.principal_point_uncertainty_mm / 
                                                  self.sensor_model.focal_length_mm)
        uncertainties['principal_point_horizontal_m'] = slant_range * principal_point_angular_uncertainty_rad
        
        return uncertainties
    
    def combine_uncertainties(self, uncertainties: Dict[str, float]) -> Dict[str, float]:
        """
        Combine individual uncertainty components using Root Sum Square (RSS).
        
        Args:
            uncertainties: Dictionary of individual uncertainty components
            
        Returns:
            Dictionary with combined uncertainties
        """
        # Separate horizontal and vertical components
        horizontal_components = [
            uncertainties.get('position_horizontal_m', 0),
            uncertainties.get('attitude_horizontal_m', 0),
            uncertainties.get('boresight_horizontal_m', 0),
            uncertainties.get('lever_arm_horizontal_m', 0),
            uncertainties.get('iop_horizontal_m', 0),
            uncertainties.get('principal_point_horizontal_m', 0)
        ]
        
        vertical_components = [
            uncertainties.get('position_vertical_m', 0),
            uncertainties.get('lever_arm_vertical_m', 0),
            uncertainties.get('dsm_vertical_m', 0)
        ]
        
        # Calculate RSS for each component
        horizontal_uncertainty = np.sqrt(sum(comp**2 for comp in horizontal_components))
        vertical_uncertainty = np.sqrt(sum(comp**2 for comp in vertical_components))
        
        # Total positional uncertainty (3D)
        total_uncertainty = np.sqrt(horizontal_uncertainty**2 + vertical_uncertainty**2)
        
        return {
            'horizontal_uncertainty_m': horizontal_uncertainty,
            'vertical_uncertainty_m': vertical_uncertainty,
            'total_uncertainty_m': total_uncertainty,
            'x_uncertainty_m': horizontal_uncertainty / np.sqrt(2),  # Assume equal X,Y uncertainty
            'y_uncertainty_m': horizontal_uncertainty / np.sqrt(2),
            'z_uncertainty_m': vertical_uncertainty
        }
    
    def estimate_scanline_uncertainties(self, num_pixels: int, flight_height_agl: float,
                                      slant_ranges: Optional[np.ndarray] = None) -> np.ndarray:
        """
        Estimate uncertainties for all pixels in a scanline.
        
        Args:
            num_pixels: Number of pixels in scanline
            flight_height_agl: Flight height above ground level
            slant_ranges: Array of slant ranges for each pixel (optional)
            
        Returns:
            Array of uncertainty dictionaries for each pixel
        """
        uncertainties = []
        
        for pixel_idx in range(num_pixels):
            slant_range = slant_ranges[pixel_idx] if slant_ranges is not None else None
            
            # Estimate individual uncertainties
            pixel_uncertainties = self.estimate_pixel_uncertainty(
                pixel_idx, flight_height_agl, slant_range
            )
            
            # Combine uncertainties
            combined_uncertainties = self.combine_uncertainties(pixel_uncertainties)
            
            uncertainties.append(combined_uncertainties)
        
        return np.array(uncertainties)
    
    def create_uncertainty_summary(self, uncertainties_array: np.ndarray) -> Dict[str, float]:
        """
        Create summary statistics for uncertainty estimates.
        
        Args:
            uncertainties_array: Array of uncertainty dictionaries
            
        Returns:
            Dictionary with summary statistics
        """
        # Extract uncertainty values
        horizontal_uncertainties = [u['horizontal_uncertainty_m'] for u in uncertainties_array]
        vertical_uncertainties = [u['vertical_uncertainty_m'] for u in uncertainties_array]
        total_uncertainties = [u['total_uncertainty_m'] for u in uncertainties_array]
        
        summary = {
            'horizontal_mean_m': np.mean(horizontal_uncertainties),
            'horizontal_std_m': np.std(horizontal_uncertainties),
            'horizontal_min_m': np.min(horizontal_uncertainties),
            'horizontal_max_m': np.max(horizontal_uncertainties),
            'vertical_mean_m': np.mean(vertical_uncertainties),
            'vertical_std_m': np.std(vertical_uncertainties),
            'vertical_min_m': np.min(vertical_uncertainties),
            'vertical_max_m': np.max(vertical_uncertainties),
            'total_mean_m': np.mean(total_uncertainties),
            'total_std_m': np.std(total_uncertainties),
            'total_min_m': np.min(total_uncertainties),
            'total_max_m': np.max(total_uncertainties)
        }
        
        return summary
    
    def log_uncertainty_summary(self, summary: Dict[str, float]):
        """
        Log uncertainty summary statistics.
        
        Args:
            summary: Summary statistics dictionary
        """
        self.logger.info("Uncertainty Summary:")
        self.logger.info(f"  Horizontal: {summary['horizontal_mean_m']:.3f} ± {summary['horizontal_std_m']:.3f}m "
                        f"(range: {summary['horizontal_min_m']:.3f} - {summary['horizontal_max_m']:.3f}m)")
        self.logger.info(f"  Vertical: {summary['vertical_mean_m']:.3f} ± {summary['vertical_std_m']:.3f}m "
                        f"(range: {summary['vertical_min_m']:.3f} - {summary['vertical_max_m']:.3f}m)")
        self.logger.info(f"  Total: {summary['total_mean_m']:.3f} ± {summary['total_std_m']:.3f}m "
                        f"(range: {summary['total_min_m']:.3f} - {summary['total_max_m']:.3f}m)")
    
    def add_atmospheric_uncertainty(self, uncertainties: Dict[str, float], 
                                  slant_range: float, atmospheric_conditions: str = "standard") -> Dict[str, float]:
        """
        Add atmospheric refraction uncertainty (optional advanced feature).
        
        Args:
            uncertainties: Existing uncertainty dictionary
            slant_range: Slant range to ground point
            atmospheric_conditions: Atmospheric conditions ("standard", "poor", "excellent")
            
        Returns:
            Updated uncertainty dictionary
        """
        if not self.config.processing_options.enable_atmospheric_correction:
            return uncertainties
        
        # Simplified atmospheric uncertainty model
        # In reality, this would depend on atmospheric models and conditions
        atmospheric_uncertainty_factors = {
            "excellent": 0.0001,  # Very stable conditions
            "standard": 0.0005,   # Normal conditions
            "poor": 0.002         # Unstable conditions
        }
        
        factor = atmospheric_uncertainty_factors.get(atmospheric_conditions, 0.0005)
        atmospheric_uncertainty_m = slant_range * factor
        
        # Add to horizontal uncertainty
        uncertainties['atmospheric_horizontal_m'] = atmospheric_uncertainty_m
        
        # Recalculate combined uncertainties
        updated_uncertainties = self.combine_uncertainties(uncertainties)
        
        return updated_uncertainties
