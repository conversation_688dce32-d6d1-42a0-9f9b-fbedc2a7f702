"""
Enhanced Main Pipeline for HSI Direct Georeferencing Tool

This module orchestrates the complete processing pipeline with enhanced error handling,
logging, and modular design.

Implements requirements from prompts:
- LS1_PIPELINE_ORCHESTRATION
- LS1_PIPELINE_ERROR_HANDLING
"""

import logging
from pathlib import Path
from typing import Union, Optional
import sys

from .config_models import Config, load_and_validate_config
from .utils import setup_logger
from .georeferencing import run_georeferencing
from .rgb_creation import run_create_rgb_geotiff

# Import data synchronization modules (placeholders for now)
# from .data_synchronization import run_consolidation, run_synchronization
# from .plotting import run_plotting

logger = logging.getLogger(__name__)


class HSIProcessingPipeline:
    """
    Main processing pipeline for HSI direct georeferencing.
    
    This class orchestrates the complete workflow from data consolidation
    to final product generation with comprehensive error handling.
    """
    
    def __init__(self, config_path: Union[str, Path]):
        """
        Initialize the processing pipeline.
        
        Args:
            config_path: Path to configuration file
        """
        self.config_path = Path(config_path)
        self.config: Optional[Config] = None
        self.logger: Optional[logging.Logger] = None
        
        # Pipeline status tracking
        self.pipeline_successful = True
        self.step_results = {}
    
    def initialize(self) -> bool:
        """
        Initialize the pipeline by loading configuration and setting up logging.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            # Load and validate configuration
            self.config = load_and_validate_config(self.config_path)
            if self.config is None:
                print(f"ERROR: Failed to load configuration from {self.config_path}")
                return False
            
            # Setup logging
            self.logger = setup_logger(
                f"{__name__}.{self.__class__.__name__}",
                self.config.processing_options.log_level
            )
            
            self.logger.info("=" * 60)
            self.logger.info("HSI Direct Georeferencing Pipeline - Enhanced Version")
            self.logger.info("=" * 60)
            self.logger.info(f"Configuration loaded from: {self.config_path.resolve()}")
            self.logger.info(f"Project: {self.config.project_name or 'Unnamed'}")
            self.logger.info(f"Log level: {self.config.processing_options.log_level}")
            
            return True
            
        except Exception as e:
            print(f"ERROR: Pipeline initialization failed: {e}")
            return False
    
    def run_data_consolidation(self) -> bool:
        """
        Run WebODM data consolidation step.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            self.logger.info("Step 1: WebODM Data Consolidation")
            self.logger.info("-" * 40)
            
            # Placeholder for actual consolidation
            # In the enhanced version, this would call the refactored consolidation module
            self.logger.info("WebODM data consolidation - PLACEHOLDER")
            self.logger.warning("Data consolidation step not yet implemented in enhanced version")
            
            # For now, assume success if poses file exists
            output_dir = Path(self.config.paths.output_directory)
            poses_file = output_dir / self.config.paths.consolidated_webodm_poses_csv
            
            if poses_file.exists():
                self.logger.info("Found existing consolidated poses file")
                self.step_results['consolidation'] = True
                return True
            else:
                self.logger.warning("Consolidated poses file not found - step may need to be run manually")
                self.step_results['consolidation'] = False
                return False
                
        except Exception as e:
            self.logger.error(f"Data consolidation failed: {e}")
            self.step_results['consolidation'] = False
            return False
    
    def run_data_synchronization(self) -> bool:
        """
        Run HSI-WebODM data synchronization step.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            self.logger.info("Step 2: HSI-WebODM Data Synchronization")
            self.logger.info("-" * 40)
            
            # Placeholder for actual synchronization
            self.logger.info("HSI-WebODM data synchronization - PLACEHOLDER")
            self.logger.warning("Data synchronization step not yet implemented in enhanced version")
            
            # For now, assume success if HSI poses file exists
            output_dir = Path(self.config.paths.output_directory)
            hsi_poses_file = output_dir / self.config.paths.hsi_poses_csv
            
            if hsi_poses_file.exists():
                self.logger.info("Found existing HSI poses file")
                self.step_results['synchronization'] = True
                return True
            else:
                self.logger.warning("HSI poses file not found - step may need to be run manually")
                self.step_results['synchronization'] = False
                return False
                
        except Exception as e:
            self.logger.error(f"Data synchronization failed: {e}")
            self.step_results['synchronization'] = False
            return False
    
    def run_pixel_georeferencing(self) -> bool:
        """
        Run HSI pixel georeferencing step.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            self.logger.info("Step 3: HSI Pixel Georeferencing")
            self.logger.info("-" * 40)
            
            # Run the enhanced georeferencing
            success = run_georeferencing(self.config)
            
            if success:
                self.logger.info("HSI pixel georeferencing completed successfully")
                self.step_results['georeferencing'] = True
            else:
                self.logger.error("HSI pixel georeferencing failed")
                self.step_results['georeferencing'] = False
            
            return success
            
        except Exception as e:
            self.logger.error(f"HSI pixel georeferencing failed with exception: {e}")
            self.step_results['georeferencing'] = False
            return False
    
    def run_rgb_geotiff_creation(self) -> bool:
        """
        Run RGB GeoTIFF creation step.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            self.logger.info("Step 4: RGB GeoTIFF Creation")
            self.logger.info("-" * 40)
            
            # Run the enhanced RGB creation
            success = run_create_rgb_geotiff(self.config)
            
            if success:
                self.logger.info("RGB GeoTIFF creation completed successfully")
                self.step_results['rgb_creation'] = True
            else:
                self.logger.error("RGB GeoTIFF creation failed")
                self.step_results['rgb_creation'] = False
                self.pipeline_successful = False  # Non-critical but affects overall success
            
            return success
            
        except Exception as e:
            self.logger.error(f"RGB GeoTIFF creation failed with exception: {e}")
            self.step_results['rgb_creation'] = False
            self.pipeline_successful = False
            return False
    
    def run_plotting(self) -> bool:
        """
        Run plotting and visualization step.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            self.logger.info("Step 5: Plotting and Visualization")
            self.logger.info("-" * 40)
            
            # Placeholder for enhanced plotting
            self.logger.info("Plotting and visualization - PLACEHOLDER")
            self.logger.warning("Enhanced plotting step not yet implemented")
            
            self.step_results['plotting'] = True
            return True
            
        except Exception as e:
            self.logger.error(f"Plotting failed with exception: {e}")
            self.step_results['plotting'] = False
            self.pipeline_successful = False  # Non-critical
            return False
    
    def run_complete_pipeline(self) -> bool:
        """
        Run the complete processing pipeline.
        
        Returns:
            True if pipeline completed successfully, False otherwise
        """
        if not self.initialize():
            return False
        
        try:
            self.logger.info("Starting complete HSI processing pipeline...")
            
            # Step 1: Data consolidation (non-critical for enhanced version)
            consolidation_success = self.run_data_consolidation()
            if not consolidation_success:
                self.logger.warning("Data consolidation step failed or skipped")
                # Continue anyway as this might be handled externally
            
            # Step 2: Data synchronization (non-critical for enhanced version)
            synchronization_success = self.run_data_synchronization()
            if not synchronization_success:
                self.logger.warning("Data synchronization step failed or skipped")
                # Continue anyway as this might be handled externally
            
            # Step 3: Pixel georeferencing (CRITICAL)
            georef_success = self.run_pixel_georeferencing()
            if not georef_success:
                self.logger.error("CRITICAL: Pixel georeferencing failed - aborting pipeline")
                self.pipeline_successful = False
                return False
            
            # Step 4: RGB GeoTIFF creation (important but not critical)
            rgb_success = self.run_rgb_geotiff_creation()
            if not rgb_success:
                self.logger.warning("RGB GeoTIFF creation failed - continuing with remaining steps")
            
            # Step 5: Plotting (optional)
            plotting_success = self.run_plotting()
            if not plotting_success:
                self.logger.warning("Plotting step failed - this is non-critical")
            
            # Final status
            self._log_pipeline_summary()
            
            return self.pipeline_successful
            
        except Exception as e:
            self.logger.error(f"Pipeline execution failed with unexpected error: {e}")
            return False
    
    def _log_pipeline_summary(self):
        """Log a summary of pipeline execution."""
        self.logger.info("=" * 60)
        self.logger.info("PIPELINE EXECUTION SUMMARY")
        self.logger.info("=" * 60)
        
        for step, success in self.step_results.items():
            status = "SUCCESS" if success else "FAILED"
            self.logger.info(f"  {step.capitalize()}: {status}")
        
        overall_status = "SUCCESS" if self.pipeline_successful else "FAILED"
        self.logger.info(f"\nOverall Pipeline Status: {overall_status}")
        
        if self.pipeline_successful:
            self.logger.info("All critical steps completed successfully!")
            output_dir = Path(self.config.paths.output_directory)
            self.logger.info(f"Results available in: {output_dir.resolve()}")
        else:
            self.logger.error("Pipeline completed with errors. Check logs for details.")


def run_complete_pipeline(config_path: Union[str, Path]) -> bool:
    """
    Main entry point for running the complete processing pipeline.
    
    Args:
        config_path: Path to configuration file
        
    Returns:
        True if pipeline completed successfully, False otherwise
    """
    try:
        pipeline = HSIProcessingPipeline(config_path)
        return pipeline.run_complete_pipeline()
    except Exception as e:
        print(f"ERROR: Failed to create or run pipeline: {e}")
        return False


def main():
    """Command-line entry point."""
    if len(sys.argv) != 2:
        print("Usage: python -m src.main_pipeline <config_file>")
        sys.exit(1)
    
    config_path = sys.argv[1]
    success = run_complete_pipeline(config_path)
    
    if success:
        print("Pipeline completed successfully!")
        sys.exit(0)
    else:
        print("Pipeline failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
