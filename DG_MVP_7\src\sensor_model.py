"""
Rigorous SensorModel Class for HSI Direct Georeferencing

This module implements a comprehensive SensorModel class that encapsulates all sensor
geometric properties and provides methods for precise line-of-sight calculations,
lens distortion corrections, and coordinate frame transformations.

Implements requirements from prompts:
- LS1_GEO_SENSORMODEL_CLASS
- LS1_GEO_SENSORMODEL_LOAD
- LS1_GEO_SENSORMODEL_LOS
- LS1_GEO_SENSORMODEL_TRANSFORM
"""

import numpy as np
import pandas as pd
from pathlib import Path
from typing import Optional, Tuple, Union
from scipy.spatial.transform import Rotation
import logging

from .config_models import Config
from .utils import normalize_vector, create_rotation_matrix

logger = logging.getLogger(__name__)


class SensorModel:
    """
    Rigorous sensor model class that encapsulates sensor geometric properties.
    
    This class handles:
    - Interior Orientation Parameters (IOPs)
    - Boresight alignment parameters
    - Lever arm definitions
    - Line-of-sight vector calculations
    - Lens distortion corrections
    - Coordinate frame transformations
    """
    
    def __init__(self, config: Config, sensor_model_path: Optional[Path] = None):
        """
        Initialize SensorModel from configuration.
        
        Args:
            config: Validated configuration object
            sensor_model_path: Optional path to sensor model file with per-pixel angles
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Load Interior Orientation Parameters
        self._load_iops()
        
        # Load boresight alignment parameters
        self._load_boresight_parameters()
        
        # Load lever arm parameters
        self._load_lever_arms()
        
        # Load per-pixel viewing angles if provided
        self.vinkelx_rad_all_pixels = None
        self.vinkely_rad_all_pixels = None
        if sensor_model_path:
            self._load_sensor_model_file(sensor_model_path)
        
        self.logger.info("SensorModel initialized successfully")
    
    def _load_iops(self):
        """Load Interior Orientation Parameters from config."""
        iop_config = self.config.sensor_model.interior_orientation
        
        self.focal_length_mm = iop_config.focal_length_mm
        self.pixel_size_um = iop_config.pixel_size_um
        self.principal_point_x_mm = iop_config.principal_point_x_mm
        self.principal_point_y_mm = iop_config.principal_point_y_mm
        
        # Lens distortion coefficients (Brown's model)
        self.k1 = iop_config.k1
        self.k2 = iop_config.k2
        self.k3 = iop_config.k3
        self.p1 = iop_config.p1
        self.p2 = iop_config.p2
        
        self.logger.info(f"Loaded IOPs: focal_length={self.focal_length_mm}mm, "
                        f"pixel_size={self.pixel_size_um}μm")
        self.logger.debug(f"Principal point: ({self.principal_point_x_mm}, {self.principal_point_y_mm})mm")
        self.logger.debug(f"Distortion coefficients: k1={self.k1}, k2={self.k2}, k3={self.k3}, "
                         f"p1={self.p1}, p2={self.p2}")
    
    def _load_boresight_parameters(self):
        """Load boresight alignment parameters from config."""
        boresight_config = self.config.sensor_model.boresight_alignment_deg
        
        self.boresight_roll_deg = boresight_config.roll_offset_deg
        self.boresight_pitch_deg = boresight_config.pitch_offset_deg
        self.boresight_yaw_deg = boresight_config.yaw_offset_deg
        
        # Calculate rotation matrix from sensor to body frame
        # R_sensor_to_body = R_body_to_sensor.T
        self.R_body_to_sensor = create_rotation_matrix(
            self.boresight_roll_deg, self.boresight_pitch_deg, self.boresight_yaw_deg, 'ZYX'
        )
        self.R_sensor_to_body = self.R_body_to_sensor.T
        
        self.logger.info(f"Loaded boresight angles: roll={self.boresight_roll_deg}°, "
                        f"pitch={self.boresight_pitch_deg}°, yaw={self.boresight_yaw_deg}°")
        self.logger.debug(f"R_sensor_to_body matrix:\n{self.R_sensor_to_body}")
    
    def _load_lever_arms(self):
        """Load lever arm parameters from config."""
        lever_config = self.config.sensor_model.lever_arms_meters
        
        # GNSS to IMU lever arm
        self.gnss_to_imu_body = np.array([
            lever_config.gnss_to_imu_x_m,
            lever_config.gnss_to_imu_y_m,
            lever_config.gnss_to_imu_z_m
        ])
        
        # IMU to sensor lever arm
        self.imu_to_sensor_body = np.array([
            lever_config.imu_to_sensor_x_m,
            lever_config.imu_to_sensor_y_m,
            lever_config.imu_to_sensor_z_m
        ])
        
        self.logger.info(f"Loaded lever arms: GNSS->IMU={self.gnss_to_imu_body}m, "
                        f"IMU->Sensor={self.imu_to_sensor_body}m")
    
    def _load_sensor_model_file(self, sensor_model_path: Path):
        """
        Load per-pixel viewing angles from sensor model file.
        
        Args:
            sensor_model_path: Path to sensor model file
        """
        try:
            # Try different parsing strategies
            sensor_data = None
            
            # Strategy 1: With header row
            try:
                sensor_data = pd.read_csv(
                    sensor_model_path, delim_whitespace=True, header=None, skiprows=1,
                    names=['pixel_index', 'vinkelx_rad', 'vinkely_rad']
                )
            except Exception:
                # Strategy 2: Without header row
                try:
                    sensor_data = pd.read_csv(
                        sensor_model_path, delim_whitespace=True, header=None,
                        names=['pixel_index', 'vinkelx_rad', 'vinkely_rad']
                    )
                except Exception:
                    # Strategy 3: Only angle columns
                    sensor_data_raw = pd.read_csv(
                        sensor_model_path, delim_whitespace=True, header=None,
                        names=['vinkelx_rad', 'vinkely_rad']
                    )
                    sensor_data = pd.DataFrame({
                        'pixel_index': np.arange(len(sensor_data_raw)),
                        'vinkelx_rad': sensor_data_raw['vinkelx_rad'],
                        'vinkely_rad': sensor_data_raw['vinkely_rad']
                    })
            
            if sensor_data is None:
                raise ValueError("Could not parse sensor model file")
            
            # Apply scale and offset corrections
            scale_vinkel_x = self.config.parameters.georeferencing.scale_vinkel_x
            offset_vinkel_x = self.config.parameters.georeferencing.offset_vinkel_x
            
            self.vinkelx_rad_all_pixels = (sensor_data['vinkelx_rad'].values * scale_vinkel_x + 
                                          offset_vinkel_x).astype(float)
            self.vinkely_rad_all_pixels = sensor_data['vinkely_rad'].values.astype(float)
            
            self.logger.info(f"Loaded sensor model with {len(self.vinkelx_rad_all_pixels)} pixels")
            self.logger.info(f"Applied corrections: scale={scale_vinkel_x}, offset={offset_vinkel_x}")
            self.logger.debug(f"Vinkelx range: {np.min(self.vinkelx_rad_all_pixels):.3f} to "
                             f"{np.max(self.vinkelx_rad_all_pixels):.3f} rad")
            
        except Exception as e:
            self.logger.error(f"Failed to load sensor model file {sensor_model_path}: {e}")
            raise
    
    def calculate_los_vector(self, pixel_index: int) -> np.ndarray:
        """
        Calculate line-of-sight vector for a given pixel in sensor coordinate system.
        
        Args:
            pixel_index: Pixel index (column number)
            
        Returns:
            3D LOS vector in sensor coordinate system (before distortion correction)
        """
        if self.vinkelx_rad_all_pixels is None or self.vinkely_rad_all_pixels is None:
            raise ValueError("Sensor model file not loaded. Cannot calculate LOS vectors.")
        
        if pixel_index < 0 or pixel_index >= len(self.vinkelx_rad_all_pixels):
            raise ValueError(f"Pixel index {pixel_index} out of range [0, {len(self.vinkelx_rad_all_pixels)-1}]")
        
        # Get viewing angles for this pixel
        vinkelx_rad = self.vinkelx_rad_all_pixels[pixel_index]
        vinkely_rad = self.vinkely_rad_all_pixels[pixel_index]
        
        # Convert angles to 3D vector in sensor coordinate system
        # For line scanners: vinkelx is across-track angle, vinkely is along-track (often ~0)
        # LOS vector convention: [tan(vinkelx), tan(vinkely), -1] then normalize
        los_vector_raw = np.array([
            np.tan(vinkelx_rad),
            np.tan(vinkely_rad),
            -1.0  # Negative Z points down (towards ground)
        ])
        
        # Normalize the vector
        los_vector_normalized = normalize_vector(los_vector_raw)
        
        # Apply lens distortion correction
        los_vector_corrected = self.apply_lens_distortion(los_vector_normalized)
        
        return los_vector_corrected
    
    def apply_lens_distortion(self, vector: np.ndarray) -> np.ndarray:
        """
        Apply lens distortion correction to a LOS vector using Brown's model.
        
        Args:
            vector: 3D LOS vector before distortion correction
            
        Returns:
            Distortion-corrected 3D LOS vector
        """
        # For now, implement a simplified distortion model
        # In a full implementation, this would involve:
        # 1. Project vector to image plane
        # 2. Apply radial and tangential distortion corrections
        # 3. Back-project to 3D vector
        
        # Simplified implementation: apply small corrections based on distortion coefficients
        x, y, z = vector
        
        # Calculate radial distance from principal point (simplified)
        r_squared = x*x + y*y
        
        # Radial distortion correction
        radial_correction = 1 + self.k1 * r_squared + self.k2 * r_squared**2 + self.k3 * r_squared**3
        
        # Tangential distortion correction (simplified)
        tangential_x = 2 * self.p1 * x * y + self.p2 * (r_squared + 2 * x*x)
        tangential_y = self.p1 * (r_squared + 2 * y*y) + 2 * self.p2 * x * y
        
        # Apply corrections
        x_corrected = x * radial_correction + tangential_x
        y_corrected = y * radial_correction + tangential_y
        z_corrected = z  # Z component typically unchanged
        
        corrected_vector = np.array([x_corrected, y_corrected, z_corrected])
        return normalize_vector(corrected_vector)
    
    def transform_sensor_to_body(self, vector: np.ndarray) -> np.ndarray:
        """
        Transform a vector from sensor frame to IMU body frame.
        
        Args:
            vector: Vector in sensor coordinate system
            
        Returns:
            Vector in IMU body coordinate system
        """
        return self.R_sensor_to_body @ vector
    
    def transform_body_to_sensor(self, vector: np.ndarray) -> np.ndarray:
        """
        Transform a vector from IMU body frame to sensor frame.
        
        Args:
            vector: Vector in IMU body coordinate system
            
        Returns:
            Vector in sensor coordinate system
        """
        return self.R_body_to_sensor @ vector
    
    def calculate_sensor_position_world(self, gnss_position_world: np.ndarray, 
                                       R_body_to_world: np.ndarray) -> np.ndarray:
        """
        Calculate sensor position in world coordinates.
        
        Args:
            gnss_position_world: GNSS antenna position in world coordinates
            R_body_to_world: Rotation matrix from body to world frame
            
        Returns:
            Sensor position in world coordinates
        """
        # P_sensor_world = P_GNSS_world + R_body_to_world @ (L_IMU_to_sensor - L_GNSS_to_IMU)
        # Note: L_GNSS_to_IMU is typically the negative of the stored gnss_to_imu vector
        total_lever_arm_body = self.imu_to_sensor_body - self.gnss_to_imu_body
        lever_arm_world = R_body_to_world @ total_lever_arm_body
        
        return gnss_position_world + lever_arm_world
    
    def get_uncertainty_parameters(self) -> dict:
        """
        Get uncertainty parameters for error propagation.
        
        Returns:
            Dictionary of uncertainty parameters
        """
        if self.config.sensor_model.uncertainties:
            uncertainties = self.config.sensor_model.uncertainties
            return {
                'focal_length_uncertainty_mm': uncertainties.iop_focal_length_uncertainty_mm,
                'principal_point_uncertainty_mm': uncertainties.iop_principal_point_uncertainty_mm,
                'boresight_uncertainty_deg': uncertainties.boresight_uncertainty_deg,
                'lever_arm_uncertainty_m': uncertainties.lever_arm_uncertainty_m
            }
        else:
            # Default uncertainty values
            return {
                'focal_length_uncertainty_mm': 0.1,
                'principal_point_uncertainty_mm': 0.01,
                'boresight_uncertainty_deg': 0.01,
                'lever_arm_uncertainty_m': 0.005
            }
