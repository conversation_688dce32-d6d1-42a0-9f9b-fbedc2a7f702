"""
Enhanced Configuration Models for HSI Direct Georeferencing Tool

This module implements comprehensive Pydantic models for validating the enhanced config.toml
structure as specified in prompts LS1_CONFIG_* series.

Key enhancements:
- Detailed Interior Orientation Parameters (IOPs)
- Standardized boresight alignment parameters
- Distinct lever arm definitions (GNSS-IMU-Sensor chain)
- Consolidated DSM parameters
- Processing options and uncertainty parameters
"""

from typing import List, Optional, Union
from pathlib import Path
from pydantic import BaseModel, validator, Field
import toml
import logging

logger = logging.getLogger(__name__)


class InteriorOrientationConfig(BaseModel):
    """Interior Orientation Parameters (IOPs) for the sensor."""
    focal_length_mm: float = Field(..., description="Focal length of the sensor in millimeters")
    pixel_size_um: float = Field(..., description="Pixel size in micrometers")
    principal_point_x_mm: float = Field(..., description="Principal point x-coordinate in millimeters")
    principal_point_y_mm: float = Field(..., description="Principal point y-coordinate in millimeters")
    
    # Lens distortion coefficients (<PERSON>'s model)
    k1: float = Field(default=0.0, description="Radial distortion coefficient k1")
    k2: float = Field(default=0.0, description="Radial distortion coefficient k2")
    k3: float = Field(default=0.0, description="Radial distortion coefficient k3")
    p1: float = Field(default=0.0, description="Tangential distortion coefficient p1")
    p2: float = Field(default=0.0, description="Tangential distortion coefficient p2")


class BoresightAlignmentDegConfig(BaseModel):
    """Boresight alignment parameters (angular offsets from IMU body frame to sensor frame)."""
    roll_offset_deg: float = Field(..., description="Roll offset in degrees")
    pitch_offset_deg: float = Field(..., description="Pitch offset in degrees")
    yaw_offset_deg: float = Field(..., description="Yaw offset in degrees")


class LeverArmsMetersConfig(BaseModel):
    """Lever arm components in meters, defined in body-fixed coordinate system (FRD)."""
    # GNSS antenna phase center to IMU reference point
    gnss_to_imu_x_m: float = Field(..., description="X component of GNSS to IMU lever arm")
    gnss_to_imu_y_m: float = Field(..., description="Y component of GNSS to IMU lever arm")
    gnss_to_imu_z_m: float = Field(..., description="Z component of GNSS to IMU lever arm")
    
    # IMU reference point to sensor perspective center
    imu_to_sensor_x_m: float = Field(..., description="X component of IMU to Sensor lever arm")
    imu_to_sensor_y_m: float = Field(..., description="Y component of IMU to Sensor lever arm")
    imu_to_sensor_z_m: float = Field(..., description="Z component of IMU to Sensor lever arm")


class SensorUncertaintiesConfig(BaseModel):
    """Uncertainty parameters for error propagation."""
    iop_focal_length_uncertainty_mm: float = Field(default=0.1, description="Focal length uncertainty in mm")
    iop_principal_point_uncertainty_mm: float = Field(default=0.01, description="Principal point uncertainty in mm")
    boresight_uncertainty_deg: float = Field(default=0.01, description="Boresight alignment uncertainty in degrees")
    lever_arm_uncertainty_m: float = Field(default=0.005, description="Lever arm uncertainty in meters")


class SensorModelConfig(BaseModel):
    """Complete sensor model configuration."""
    interior_orientation: InteriorOrientationConfig
    boresight_alignment_deg: BoresightAlignmentDegConfig
    lever_arms_meters: LeverArmsMetersConfig
    uncertainties: Optional[SensorUncertaintiesConfig] = Field(default_factory=SensorUncertaintiesConfig)


class DSMParametersConfig(BaseModel):
    """Digital Surface Model parameters."""
    path: str = Field(..., description="Path to the DSM file")
    type: str = Field(..., description="DSM type: 'raster' or 'tin'")
    default_vertical_uncertainty_m: Optional[float] = Field(default=0.5, description="Default vertical uncertainty in meters")
    nodata_value: Optional[Union[float, int]] = Field(default=None, description="No-data value in the DSM")
    
    # Ray-DSM intersection parameters
    ray_dsm_max_search_dist_m: float = Field(default=2000.0, description="Maximum search distance along ray in meters")
    ray_dsm_step_m: float = Field(default=5.0, description="Initial step size for ray marching in meters")
    ray_dsm_bisection_tolerance_m: float = Field(default=0.01, description="Tolerance for bisection method in meters")

    @validator('type')
    def validate_dsm_type(cls, v):
        if v not in ["raster", "tin"]:
            raise ValueError('DSM type must be "raster" or "tin"')
        return v


class ProcessingOptions(BaseModel):
    """General processing options."""
    log_level: str = Field(default="INFO", description="Logging level")
    enable_atmospheric_correction: bool = Field(default=False, description="Enable atmospheric correction")
    coordinate_reference_system_epsg_output: int = Field(default=32632, description="Default output EPSG code")

    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f'log_level must be one of {valid_levels}')
        return v.upper()


class PathsConfig(BaseModel):
    """File and directory paths configuration."""
    hsi_data_directory: str = Field(..., description="HSI data directory")
    webodm_data_directory: str = Field(..., description="WebODM data directory")
    output_directory: str = Field(..., description="Output directory")
    plot_output_directory: str = Field(default="plots/", description="Plot output directory")
    
    hsi_base_filename: str = Field(..., description="Base filename for HSI files")
    sensor_model_file: str = Field(..., description="Sensor model file")
    shots_geojson_file: str = Field(..., description="Shots GeoJSON file")
    haip_files_subdirectory: str = Field(..., description="HAIP files subdirectory")
    dsm_file: str = Field(..., description="DSM file path")
    
    consolidated_webodm_poses_csv: str = Field(..., description="Consolidated WebODM poses CSV")
    hsi_poses_csv: str = Field(..., description="HSI poses CSV")
    georeferenced_pixels_csv: str = Field(..., description="Georeferenced pixels CSV")
    georeferenced_rgb_tif: str = Field(..., description="Georeferenced RGB TIFF")


class GeoreferencingParams(BaseModel):
    """Georeferencing-specific parameters."""
    scale_vinkel_x: float = Field(default=1.0, description="Scale factor for vinkel_x")
    offset_vinkel_x: float = Field(default=0.0, description="Offset for vinkel_x")
    z_ground_calculation_method: str = Field(..., description="Z ground calculation method")
    z_ground_offset_meters: Optional[float] = Field(default=None, description="Z ground offset in meters")
    z_ground_fixed_value_meters: Optional[float] = Field(default=None, description="Fixed Z ground value in meters")

    @validator('z_ground_calculation_method')
    def validate_z_ground_method(cls, v):
        valid_methods = ["avg_pose_z_minus_offset", "fixed_value", "dsm_intersection"]
        if v not in valid_methods:
            raise ValueError(f"z_ground_calculation_method must be one of {valid_methods}")
        return v


class RGBGeotiffCreationParams(BaseModel):
    """RGB GeoTIFF creation parameters."""
    target_wavelength_R_nm: float = Field(..., description="Target wavelength for red band in nm")
    target_wavelength_G_nm: float = Field(..., description="Target wavelength for green band in nm")
    target_wavelength_B_nm: float = Field(..., description="Target wavelength for blue band in nm")
    target_resolution_meters: float = Field(..., description="Target resolution in meters")
    output_epsg_code: int = Field(..., description="Output EPSG code")
    normalization_method: str = Field(..., description="Normalization method")
    generate_uncertainty_layer: bool = Field(default=False, description="Generate uncertainty layer")

    @validator('normalization_method')
    def validate_normalization_method(cls, v):
        if v not in ["min_max", "percentile_2_98"]:
            raise ValueError('normalization_method must be "min_max" or "percentile_2_98"')
        return v


class WebODMConsolidationParams(BaseModel):
    """WebODM consolidation parameters."""
    haip_timestamp_key: str = Field(..., description="Timestamp key in HAIP files")


class PlottingParams(BaseModel):
    """Plotting parameters."""
    pass  # Placeholder for future plotting options


class ParametersConfig(BaseModel):
    """All processing parameters."""
    webodm_consolidation: WebODMConsolidationParams
    georeferencing: GeoreferencingParams
    rgb_geotiff_creation: RGBGeotiffCreationParams
    plotting: PlottingParams


class Config(BaseModel):
    """Main configuration model."""
    project_name: Optional[str] = Field(default=None, description="Project name")
    paths: PathsConfig
    sensor_model: SensorModelConfig
    dsm_parameters: DSMParametersConfig
    parameters: ParametersConfig
    processing_options: ProcessingOptions


def load_and_validate_config(config_path: Union[str, Path]) -> Optional[Config]:
    """
    Load and validate configuration from TOML file.
    
    Args:
        config_path: Path to the configuration file
        
    Returns:
        Validated Config object or None if validation fails
    """
    config_path = Path(config_path)
    
    try:
        logger.info(f"Loading configuration from: {config_path.resolve()}")
        config_data = toml.load(config_path)
        validated_config = Config(**config_data)
        logger.info("Configuration loaded and validated successfully")
        return validated_config
        
    except FileNotFoundError:
        logger.error(f"Configuration file not found: {config_path.resolve()}")
        return None
    except Exception as e:
        logger.error(f"Configuration validation error: {e}")
        return None
