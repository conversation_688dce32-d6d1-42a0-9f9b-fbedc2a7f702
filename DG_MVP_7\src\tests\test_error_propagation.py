"""
Unit tests for ErrorPropagationModule class.

Tests uncertainty estimation and combination formulas.
"""

import pytest
import numpy as np
from unittest.mock import Mock

from ..error_propagation import ErrorPropagationModule
from ..config_models import Config
from ..sensor_model import SensorModel


class TestErrorPropagationModule:
    """Test ErrorPropagationModule class functionality."""
    
    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration for testing."""
        config = Mock(spec=Config)
        
        # Mock DSM parameters
        config.dsm_parameters.default_vertical_uncertainty_m = 0.5
        
        # Mock processing options
        config.processing_options.enable_atmospheric_correction = False
        
        return config
    
    @pytest.fixture
    def mock_sensor_model(self):
        """Create a mock sensor model for testing."""
        sensor_model = Mock(spec=SensorModel)
        
        # Mock uncertainty parameters
        sensor_model.get_uncertainty_parameters.return_value = {
            'focal_length_uncertainty_mm': 0.1,
            'principal_point_uncertainty_mm': 0.01,
            'boresight_uncertainty_deg': 0.01,
            'lever_arm_uncertainty_m': 0.005
        }
        
        # Mock sensor properties
        sensor_model.focal_length_mm = 35.0
        sensor_model.pixel_size_um = 4.5
        
        return sensor_model
    
    def test_error_propagation_initialization(self, mock_config, mock_sensor_model):
        """Test ErrorPropagationModule initialization."""
        error_prop = ErrorPropagationModule(mock_config, mock_sensor_model)
        
        assert error_prop.config == mock_config
        assert error_prop.sensor_model == mock_sensor_model
        assert error_prop.focal_length_uncertainty_mm == 0.1
        assert error_prop.boresight_uncertainty_deg == 0.01
        assert error_prop.dsm_uncertainty_m == 0.5
    
    def test_estimate_pixel_uncertainty_basic(self, mock_config, mock_sensor_model):
        """Test basic pixel uncertainty estimation."""
        # Mock LOS vector calculation
        mock_sensor_model.calculate_los_vector.return_value = np.array([0.1, 0.0, -1.0])
        
        error_prop = ErrorPropagationModule(mock_config, mock_sensor_model)
        
        # Test uncertainty estimation
        pixel_index = 50
        flight_height_agl = 100.0
        
        uncertainties = error_prop.estimate_pixel_uncertainty(pixel_index, flight_height_agl)
        
        # Verify that all expected uncertainty components are present
        expected_keys = [
            'position_horizontal_m',
            'position_vertical_m',
            'attitude_horizontal_m',
            'boresight_horizontal_m',
            'lever_arm_horizontal_m',
            'lever_arm_vertical_m',
            'dsm_vertical_m',
            'iop_horizontal_m',
            'principal_point_horizontal_m'
        ]
        
        for key in expected_keys:
            assert key in uncertainties
            assert uncertainties[key] >= 0  # All uncertainties should be non-negative
    
    def test_estimate_pixel_uncertainty_with_slant_range(self, mock_config, mock_sensor_model):
        """Test pixel uncertainty estimation with provided slant range."""
        error_prop = ErrorPropagationModule(mock_config, mock_sensor_model)
        
        pixel_index = 50
        flight_height_agl = 100.0
        slant_range = 120.0  # Provided slant range
        
        uncertainties = error_prop.estimate_pixel_uncertainty(
            pixel_index, flight_height_agl, slant_range
        )
        
        # Verify that attitude uncertainty scales with slant range
        attitude_uncertainty = uncertainties['attitude_horizontal_m']
        expected_attitude_uncertainty = slant_range * np.deg2rad(error_prop.attitude_uncertainty_deg)
        
        assert abs(attitude_uncertainty - expected_attitude_uncertainty) < 1e-6
    
    def test_combine_uncertainties_basic(self, mock_config, mock_sensor_model):
        """Test basic uncertainty combination using RSS."""
        error_prop = ErrorPropagationModule(mock_config, mock_sensor_model)
        
        # Create test uncertainties
        uncertainties = {
            'position_horizontal_m': 0.05,
            'attitude_horizontal_m': 0.1,
            'boresight_horizontal_m': 0.02,
            'lever_arm_horizontal_m': 0.005,
            'iop_horizontal_m': 0.01,
            'principal_point_horizontal_m': 0.001,
            'position_vertical_m': 0.05,
            'lever_arm_vertical_m': 0.005,
            'dsm_vertical_m': 0.5
        }
        
        combined = error_prop.combine_uncertainties(uncertainties)
        
        # Verify that combined uncertainties are present
        assert 'horizontal_uncertainty_m' in combined
        assert 'vertical_uncertainty_m' in combined
        assert 'total_uncertainty_m' in combined
        assert 'x_uncertainty_m' in combined
        assert 'y_uncertainty_m' in combined
        assert 'z_uncertainty_m' in combined
        
        # Verify RSS calculation for horizontal components
        horizontal_components = [
            uncertainties['position_horizontal_m'],
            uncertainties['attitude_horizontal_m'],
            uncertainties['boresight_horizontal_m'],
            uncertainties['lever_arm_horizontal_m'],
            uncertainties['iop_horizontal_m'],
            uncertainties['principal_point_horizontal_m']
        ]
        expected_horizontal = np.sqrt(sum(comp**2 for comp in horizontal_components))
        
        assert abs(combined['horizontal_uncertainty_m'] - expected_horizontal) < 1e-6
        
        # Verify RSS calculation for vertical components
        vertical_components = [
            uncertainties['position_vertical_m'],
            uncertainties['lever_arm_vertical_m'],
            uncertainties['dsm_vertical_m']
        ]
        expected_vertical = np.sqrt(sum(comp**2 for comp in vertical_components))
        
        assert abs(combined['vertical_uncertainty_m'] - expected_vertical) < 1e-6
        
        # Verify total uncertainty
        expected_total = np.sqrt(expected_horizontal**2 + expected_vertical**2)
        assert abs(combined['total_uncertainty_m'] - expected_total) < 1e-6
    
    def test_estimate_scanline_uncertainties(self, mock_config, mock_sensor_model):
        """Test uncertainty estimation for a complete scanline."""
        # Mock LOS vector calculation for multiple pixels
        mock_sensor_model.calculate_los_vector.side_effect = lambda idx: np.array([
            (idx - 50) * 0.001, 0.0, -1.0
        ])
        
        error_prop = ErrorPropagationModule(mock_config, mock_sensor_model)
        
        num_pixels = 10
        flight_height_agl = 100.0
        
        uncertainties_array = error_prop.estimate_scanline_uncertainties(num_pixels, flight_height_agl)
        
        # Verify that we get uncertainties for all pixels
        assert len(uncertainties_array) == num_pixels
        
        # Verify that each uncertainty dictionary has the expected structure
        for uncertainty_dict in uncertainties_array:
            assert 'horizontal_uncertainty_m' in uncertainty_dict
            assert 'vertical_uncertainty_m' in uncertainty_dict
            assert 'total_uncertainty_m' in uncertainty_dict
    
    def test_create_uncertainty_summary(self, mock_config, mock_sensor_model):
        """Test uncertainty summary statistics creation."""
        error_prop = ErrorPropagationModule(mock_config, mock_sensor_model)
        
        # Create sample uncertainty array
        uncertainties_array = []
        for i in range(5):
            uncertainties_array.append({
                'horizontal_uncertainty_m': 0.1 + i * 0.01,
                'vertical_uncertainty_m': 0.5 + i * 0.05,
                'total_uncertainty_m': 0.52 + i * 0.05
            })
        
        summary = error_prop.create_uncertainty_summary(uncertainties_array)
        
        # Verify summary statistics
        expected_keys = [
            'horizontal_mean_m', 'horizontal_std_m', 'horizontal_min_m', 'horizontal_max_m',
            'vertical_mean_m', 'vertical_std_m', 'vertical_min_m', 'vertical_max_m',
            'total_mean_m', 'total_std_m', 'total_min_m', 'total_max_m'
        ]
        
        for key in expected_keys:
            assert key in summary
        
        # Verify some specific values
        assert summary['horizontal_min_m'] == 0.1
        assert summary['horizontal_max_m'] == 0.14
        assert summary['vertical_min_m'] == 0.5
        assert summary['vertical_max_m'] == 0.7
    
    def test_add_atmospheric_uncertainty_disabled(self, mock_config, mock_sensor_model):
        """Test atmospheric uncertainty when atmospheric correction is disabled."""
        mock_config.processing_options.enable_atmospheric_correction = False
        
        error_prop = ErrorPropagationModule(mock_config, mock_sensor_model)
        
        uncertainties = {'position_horizontal_m': 0.05}
        slant_range = 100.0
        
        updated_uncertainties = error_prop.add_atmospheric_uncertainty(
            uncertainties, slant_range
        )
        
        # Should return unchanged uncertainties
        assert updated_uncertainties == uncertainties
        assert 'atmospheric_horizontal_m' not in updated_uncertainties
    
    def test_add_atmospheric_uncertainty_enabled(self, mock_config, mock_sensor_model):
        """Test atmospheric uncertainty when atmospheric correction is enabled."""
        mock_config.processing_options.enable_atmospheric_correction = True
        
        error_prop = ErrorPropagationModule(mock_config, mock_sensor_model)
        
        uncertainties = {
            'position_horizontal_m': 0.05,
            'attitude_horizontal_m': 0.1
        }
        slant_range = 100.0
        
        updated_uncertainties = error_prop.add_atmospheric_uncertainty(
            uncertainties, slant_range, "standard"
        )
        
        # Should add atmospheric uncertainty
        assert 'atmospheric_horizontal_m' in updated_uncertainties
        assert updated_uncertainties['atmospheric_horizontal_m'] > 0
        
        # Should recalculate combined uncertainties
        assert 'horizontal_uncertainty_m' in updated_uncertainties
    
    def test_uncertainty_scaling_with_flight_height(self, mock_config, mock_sensor_model):
        """Test that uncertainties scale appropriately with flight height."""
        mock_sensor_model.calculate_los_vector.return_value = np.array([0.1, 0.0, -1.0])
        
        error_prop = ErrorPropagationModule(mock_config, mock_sensor_model)
        
        pixel_index = 50
        
        # Test with different flight heights
        uncertainties_100m = error_prop.estimate_pixel_uncertainty(pixel_index, 100.0)
        uncertainties_200m = error_prop.estimate_pixel_uncertainty(pixel_index, 200.0)
        
        # Angular uncertainties should scale with flight height
        assert uncertainties_200m['attitude_horizontal_m'] > uncertainties_100m['attitude_horizontal_m']
        assert uncertainties_200m['boresight_horizontal_m'] > uncertainties_100m['boresight_horizontal_m']
    
    def test_log_uncertainty_summary(self, mock_config, mock_sensor_model, caplog):
        """Test uncertainty summary logging."""
        error_prop = ErrorPropagationModule(mock_config, mock_sensor_model)
        
        summary = {
            'horizontal_mean_m': 0.12,
            'horizontal_std_m': 0.02,
            'horizontal_min_m': 0.10,
            'horizontal_max_m': 0.15,
            'vertical_mean_m': 0.52,
            'vertical_std_m': 0.05,
            'vertical_min_m': 0.50,
            'vertical_max_m': 0.60,
            'total_mean_m': 0.54,
            'total_std_m': 0.05,
            'total_min_m': 0.52,
            'total_max_m': 0.62
        }
        
        error_prop.log_uncertainty_summary(summary)
        
        # Verify that logging occurred
        assert "Uncertainty Summary:" in caplog.text
        assert "Horizontal:" in caplog.text
        assert "Vertical:" in caplog.text
        assert "Total:" in caplog.text


def test_placeholder():
    """Placeholder test to ensure pytest runs."""
    assert True
