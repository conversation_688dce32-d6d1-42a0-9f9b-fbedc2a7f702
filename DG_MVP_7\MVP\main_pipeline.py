from create_consolidated_webodm_poses import run_consolidation
from synchronize_hsi_webodm import run_synchronization
from georeference_hsi_pixels import run_georeferencing
from create_georeferenced_rgb import run_create_rgb_geotiff
from plot_hsi_data import run_plotting
import toml
from pathlib import Path
import logging # Import logging
from pydantic import ValidationError

# Assuming config_models.py is in the same directory (MVP)
from config_models import Config

# Configure basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_and_validate_config(config_path_str: str) -> Optional[Config]:
    """Loads and validates the TOML configuration file using Pydantic models."""
    config_path = Path(config_path_str)
    if not config_path.is_absolute():
        # If MVP directory is the base for execution of main_pipeline.py
        # and config.toml is also in MVP
        base_path = Path(__file__).parent
        config_path = base_path / config_path_str
        logger.info(f"Relative config path '{config_path_str}' resolved to '{config_path.resolve()}'")


    try:
        logger.info(f"Attempting to load configuration from: {config_path.resolve()}")
        config_data = toml.load(config_path)
        validated_config = Config(**config_data)
        logger.info("Configuration loaded and validated successfully.")
        
        # Set log level based on config after validation
        log_level_str = validated_config.processing_options.log_level
        numeric_level = getattr(logging, log_level_str.upper(), logging.INFO)
        logging.getLogger().setLevel(numeric_level) # Set root logger level
        logger.info(f"Logging level set to: {log_level_str}")

        return validated_config
    except FileNotFoundError:
        logger.error(f"Configuration file not found at {config_path.resolve()}")
        return None
    except ValidationError as e:
        logger.error(f"Configuration validation error: {e}")
        return None
    except Exception as e:
        logger.error(f"An unexpected error occurred while loading or validating the configuration: {e}")
        return None

def run_complete_pipeline(config_path_str: str = 'config.toml') -> bool:
    """
    Führt die komplette HSI-Direktgeoreferenzierungs-Pipeline aus.

    Args:
        config_path_str (str): Pfad zur TOML-Konfigurationsdatei.
                               Standard ist 'config.toml' im MVP-Verzeichnis.

    Returns:
        bool: True, wenn die Pipeline vollständig erfolgreich war, sonst False.
    """
    logger.info(f"Starting complete HSI Georeferencing Pipeline with configuration: {config_path_str}")
    
    config = load_and_validate_config(config_path_str)
    if config is None:
        logger.error("Pipeline aborted due to configuration loading/validation errors.")
        return False
        
    pipeline_successful = True

    # Pass the validated config object to each step
    # Note: Individual run_... functions will need to be updated to accept the Config object
    # instead of a config_path string. This change is outside the scope of this diff for main_pipeline.py
    # but is implied by LS1_CONFIG_VALIDATION. For now, we'll keep passing config_path_str
    # and assume those functions will be updated later or handle loading themselves.
    # Ideally, they would take the `config: Config` object.

    logger.info("\n--- Step 1: Consolidating WebODM Poses ---")
    if not run_consolidation(config_path_str): # TODO: Update to pass `config` object
        logger.error("ERROR in Step 1: Consolidating WebODM Poses. Pipeline aborted.")
        return False
    logger.info("Step 1 completed successfully.")

    logger.info("\n--- Step 2: HSI Pose Synchronization ---")
    if not run_synchronization(config_path_str): # TODO: Update to pass `config` object
        logger.error("ERROR in Step 2: HSI Pose Synchronization. Pipeline aborted.")
        return False
    logger.info("Step 2 completed successfully.")

    logger.info("\n--- Step 3: Direct Georeferencing of HSI Pixels ---")
    if not run_georeferencing(config_path_str): # TODO: Update to pass `config` object
        logger.error("ERROR in Step 3: Direct Georeferencing of HSI Pixels. Pipeline aborted.")
        return False
    logger.info("Step 3 completed successfully.")

    logger.info("\n--- Step 4: Creating Georeferenced RGB GeoTIFF ---")
    if not run_create_rgb_geotiff(config_path_str): # TODO: Update to pass `config` object
        logger.warning("WARNING in Step 4: Error creating georeferenced RGB GeoTIFF.")
        pipeline_successful = False
    else:
        logger.info("Step 4 completed successfully.")

    logger.info("\n--- Step 5: Creating Plots ---")
    if not run_plotting(config_path_str): # TODO: Update to pass `config` object
        logger.warning("WARNING in Step 5: Error creating plots.")
        pipeline_successful = False
    else:
        logger.info("Step 5 completed successfully.")

    if pipeline_successful:
        logger.info("\nComplete HSI Georeferencing Pipeline finished successfully.")
    else:
        logger.warning("\nHSI Georeferencing Pipeline finished with warnings or errors in optional steps.")
    return pipeline_successful

if __name__ == "__main__":
    # Default config path relative to this script's location (MVP folder)
    default_config = 'config.toml'
    
    # Example for argparse if needed later:
    # import argparse
    # parser = argparse.ArgumentParser(description="Runs the HSI Georeferencing Pipeline.")
    # parser.add_argument('--config', type=str, default=default_config,
    #                     help=f'Path to the configuration file (default: {default_config} in MVP directory)')
    # args = parser.parse_args()
    # success = run_complete_pipeline(config_path_str=args.config)
    
    success = run_complete_pipeline(config_path_str=default_config)
    if success:
        logger.info("Pipeline execution finished: Success")
    else:
        logger.warning("Pipeline execution finished: Completed with errors/warnings")