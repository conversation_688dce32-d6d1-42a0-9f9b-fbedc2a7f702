"""
Example usage of the Enhanced HSI Direct Georeferencing Tool

This script demonstrates how to use the enhanced georeferencing tool
with the new modular architecture and configuration system.
"""

import logging
from pathlib import Path
import sys

# Add the parent directory to the path so we can import from src
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.config_models import load_and_validate_config
from src.main_pipeline import run_complete_pipeline
from src.georeferencing import run_georeferencing
from src.rgb_creation import run_create_rgb_geotiff
from src.sensor_model import SensorModel
from src.utils import setup_logger


def example_complete_pipeline():
    """Example: Run the complete processing pipeline."""
    print("=" * 60)
    print("Example 1: Complete Pipeline Execution")
    print("=" * 60)
    
    # Path to enhanced configuration
    config_path = Path(__file__).parent / "config_enhanced.toml"
    
    if not config_path.exists():
        print(f"Configuration file not found: {config_path}")
        print("Please ensure config_enhanced.toml exists in the src directory")
        return False
    
    print(f"Using configuration: {config_path}")
    
    # Run complete pipeline
    success = run_complete_pipeline(config_path)
    
    if success:
        print("✅ Complete pipeline executed successfully!")
    else:
        print("❌ Pipeline execution failed. Check logs for details.")
    
    return success


def example_individual_steps():
    """Example: Run individual processing steps."""
    print("\n" + "=" * 60)
    print("Example 2: Individual Step Execution")
    print("=" * 60)
    
    config_path = Path(__file__).parent / "config_enhanced.toml"
    
    if not config_path.exists():
        print(f"Configuration file not found: {config_path}")
        return False
    
    # Step 1: Load and validate configuration
    print("Step 1: Loading configuration...")
    config = load_and_validate_config(config_path)
    
    if config is None:
        print("❌ Configuration loading failed")
        return False
    
    print(f"✅ Configuration loaded successfully")
    print(f"   Project: {config.project_name}")
    print(f"   Log level: {config.processing_options.log_level}")
    
    # Step 2: Run georeferencing only
    print("\nStep 2: Running pixel georeferencing...")
    georef_success = run_georeferencing(config)
    
    if georef_success:
        print("✅ Pixel georeferencing completed successfully")
    else:
        print("❌ Pixel georeferencing failed")
        return False
    
    # Step 3: Run RGB creation only
    print("\nStep 3: Running RGB GeoTIFF creation...")
    rgb_success = run_create_rgb_geotiff(config)
    
    if rgb_success:
        print("✅ RGB GeoTIFF creation completed successfully")
    else:
        print("❌ RGB GeoTIFF creation failed")
    
    return georef_success and rgb_success


def example_sensor_model_usage():
    """Example: Direct usage of SensorModel class."""
    print("\n" + "=" * 60)
    print("Example 3: Direct SensorModel Usage")
    print("=" * 60)
    
    config_path = Path(__file__).parent / "config_enhanced.toml"
    
    if not config_path.exists():
        print(f"Configuration file not found: {config_path}")
        return False
    
    # Load configuration
    config = load_and_validate_config(config_path)
    if config is None:
        return False
    
    # Setup logging
    logger = setup_logger("example", config.processing_options.log_level)
    
    try:
        # Create sensor model (without sensor model file for this example)
        print("Creating SensorModel...")
        sensor_model = SensorModel(config)
        
        print("✅ SensorModel created successfully")
        print(f"   Focal length: {sensor_model.focal_length_mm} mm")
        print(f"   Pixel size: {sensor_model.pixel_size_um} μm")
        print(f"   Boresight angles: R={sensor_model.boresight_roll_deg}°, "
              f"P={sensor_model.boresight_pitch_deg}°, Y={sensor_model.boresight_yaw_deg}°")
        
        # Get uncertainty parameters
        uncertainties = sensor_model.get_uncertainty_parameters()
        print(f"   Uncertainty parameters: {uncertainties}")
        
        # Test coordinate transformations
        import numpy as np
        
        test_vector = np.array([1.0, 0.0, 0.0])
        transformed = sensor_model.transform_sensor_to_body(test_vector)
        print(f"   Test transformation: {test_vector} -> {transformed}")
        
        return True
        
    except Exception as e:
        print(f"❌ SensorModel example failed: {e}")
        return False


def example_configuration_validation():
    """Example: Configuration validation and error handling."""
    print("\n" + "=" * 60)
    print("Example 4: Configuration Validation")
    print("=" * 60)
    
    # Test with valid configuration
    config_path = Path(__file__).parent / "config_enhanced.toml"
    
    if config_path.exists():
        print("Testing valid configuration...")
        config = load_and_validate_config(config_path)
        
        if config:
            print("✅ Valid configuration loaded successfully")
            print(f"   Validation passed for all {len(config.__fields__)} main sections")
        else:
            print("❌ Valid configuration failed to load")
    
    # Test with invalid configuration
    print("\nTesting invalid configuration...")
    invalid_config_path = Path(__file__).parent / "nonexistent_config.toml"
    
    config = load_and_validate_config(invalid_config_path)
    
    if config is None:
        print("✅ Invalid configuration correctly rejected")
    else:
        print("❌ Invalid configuration was incorrectly accepted")
    
    return True


def main():
    """Run all examples."""
    print("HSI Direct Georeferencing Tool - Enhanced Version Examples")
    print("This script demonstrates the usage of the enhanced georeferencing tool.")
    print("\nNote: These examples require actual data files to run successfully.")
    print("The examples will show the API usage even if data files are missing.\n")
    
    # Run examples
    examples = [
        ("Complete Pipeline", example_complete_pipeline),
        ("Individual Steps", example_individual_steps),
        ("SensorModel Usage", example_sensor_model_usage),
        ("Configuration Validation", example_configuration_validation)
    ]
    
    results = []
    
    for name, example_func in examples:
        try:
            success = example_func()
            results.append((name, success))
        except Exception as e:
            print(f"❌ Example '{name}' failed with exception: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("EXAMPLES SUMMARY")
    print("=" * 60)
    
    for name, success in results:
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"{name}: {status}")
    
    successful_count = sum(1 for _, success in results if success)
    total_count = len(results)
    
    print(f"\nOverall: {successful_count}/{total_count} examples completed successfully")
    
    if successful_count == total_count:
        print("🎉 All examples completed successfully!")
    else:
        print("⚠️  Some examples failed. This is expected if data files are missing.")
        print("   The examples demonstrate the API usage regardless of data availability.")


if __name__ == "__main__":
    main()
