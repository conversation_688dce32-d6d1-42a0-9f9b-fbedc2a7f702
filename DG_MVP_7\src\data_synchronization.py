"""
Data Synchronization Module for HSI Direct Georeferencing Tool

This module implements robust HSI-EOP synchronization functionality, porting and
refactoring logic from MVP/synchronize_hsi_webodm.py with enhanced Slerp interpolation,
error handling, validation, and logging.

Implements requirements from prompt:
- LS2_DATA_PIPELINE
"""

import logging
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Optional, Union, Tuple, List
from scipy.spatial.transform import Rotation, Slerp

from .config_models import Config, load_and_validate_config
from .utils import setup_logger, validate_file_exists, validate_directory_exists

logger = logging.getLogger(__name__)


class DataSynchronizer:
    """
    Synchronizes HSI timestamps with consolidated EOP data using robust interpolation.
    
    This class handles the synchronization of HSI line timestamps with pose data,
    providing Slerp interpolation for attitude data and linear interpolation for positions.
    """
    
    def __init__(self, config: Union[str, Path, Config]):
        """
        Initialize data synchronizer.
        
        Args:
            config: Configuration file path or Config object
        """
        # Load and validate configuration
        if isinstance(config, (str, Path)):
            self.config = load_and_validate_config(config)
            if self.config is None:
                raise ValueError("Failed to load and validate configuration")
        else:
            self.config = config
        
        # Setup logging
        self.logger = setup_logger(
            f"{__name__}.{self.__class__.__name__}",
            self.config.processing_options.log_level
        )
        
        self.logger.info("DataSynchronizer initialized")
    
    def validate_inputs(self) -> bool:
        """
        Validate input files and directories.
        
        Returns:
            True if all inputs are valid, False otherwise
        """
        self.logger.info("Validating inputs for data synchronization...")
        
        # Check consolidated poses file
        output_dir = Path(self.config.paths.output_directory)
        poses_file = output_dir / self.config.paths.consolidated_webodm_poses_csv
        
        if not validate_file_exists(poses_file, "Consolidated poses CSV"):
            return False
        
        # Check HSI header file for timestamp information
        hsi_dir = Path(self.config.paths.hsi_data_directory)
        hdr_file = hsi_dir / f"{self.config.paths.hsi_base_filename}.hdr"
        
        if not validate_file_exists(hdr_file, "HSI header file"):
            return False
        
        # Check output directory
        if not validate_directory_exists(output_dir, "Output directory", create_if_missing=True):
            return False
        
        self.logger.info("Input validation completed successfully")
        return True
    
    def load_consolidated_poses(self) -> Optional[pd.DataFrame]:
        """
        Load consolidated pose data.
        
        Returns:
            Consolidated poses DataFrame or None if loading fails
        """
        try:
            output_dir = Path(self.config.paths.output_directory)
            poses_file = output_dir / self.config.paths.consolidated_webodm_poses_csv
            
            self.logger.info(f"Loading consolidated poses from: {poses_file}")
            
            poses_df = pd.read_csv(poses_file)
            
            # Validate required columns
            required_columns = ['pos_x', 'pos_y', 'pos_z', 'rot_x', 'rot_y', 'rot_z', 'rot_w', 'timestamp']
            missing_columns = [col for col in required_columns if col not in poses_df.columns]
            
            if missing_columns:
                self.logger.error(f"Missing required columns in poses file: {missing_columns}")
                return None
            
            # Filter out poses without timestamps
            valid_poses = poses_df.dropna(subset=['timestamp'])
            
            if len(valid_poses) == 0:
                self.logger.error("No poses with valid timestamps found")
                return None
            
            if len(valid_poses) < len(poses_df):
                self.logger.warning(f"Filtered out {len(poses_df) - len(valid_poses)} poses without timestamps")
            
            # Sort by timestamp
            valid_poses = valid_poses.sort_values('timestamp').reset_index(drop=True)
            
            self.logger.info(f"Loaded {len(valid_poses)} poses with valid timestamps")
            self.logger.info(f"Timestamp range: {valid_poses['timestamp'].min():.3f} to {valid_poses['timestamp'].max():.3f}")
            
            return valid_poses
            
        except Exception as e:
            self.logger.error(f"Failed to load consolidated poses: {e}")
            return None
    
    def extract_hsi_timestamps(self) -> Optional[np.ndarray]:
        """
        Extract HSI line timestamps from header or generate synthetic timestamps.
        
        Returns:
            Array of HSI line timestamps or None if extraction fails
        """
        try:
            hsi_dir = Path(self.config.paths.hsi_data_directory)
            hdr_file = hsi_dir / f"{self.config.paths.hsi_base_filename}.hdr"
            
            self.logger.info(f"Extracting HSI timestamps from: {hdr_file}")
            
            # Parse header file
            header_info = {}
            with open(hdr_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        if key == 'lines':
                            header_info['lines'] = int(value)
                        elif key.startswith('acquisition time'):
                            # Try to parse acquisition time
                            header_info['acquisition_time'] = value
            
            if 'lines' not in header_info:
                self.logger.error("Could not determine number of HSI lines from header")
                return None
            
            num_lines = header_info['lines']
            self.logger.info(f"HSI data has {num_lines} lines")
            
            # For now, generate synthetic timestamps
            # In a real implementation, this would extract actual timestamps from HSI data
            # Assume 50 Hz acquisition rate (20ms per line)
            line_interval = 0.02  # seconds
            start_time = 0.0  # This should be extracted from actual HSI data
            
            hsi_timestamps = np.arange(num_lines) * line_interval + start_time
            
            self.logger.info(f"Generated {len(hsi_timestamps)} HSI timestamps")
            self.logger.info(f"HSI timestamp range: {hsi_timestamps[0]:.3f} to {hsi_timestamps[-1]:.3f}")
            self.logger.warning("Using synthetic timestamps - should be replaced with actual HSI timestamps")
            
            return hsi_timestamps
            
        except Exception as e:
            self.logger.error(f"Failed to extract HSI timestamps: {e}")
            return None
    
    def interpolate_poses(self, poses_df: pd.DataFrame, hsi_timestamps: np.ndarray) -> Optional[pd.DataFrame]:
        """
        Interpolate poses at HSI timestamps using Slerp for attitudes and linear for positions.
        
        Args:
            poses_df: Consolidated poses DataFrame
            hsi_timestamps: Array of HSI line timestamps
            
        Returns:
            Interpolated poses DataFrame or None if interpolation fails
        """
        try:
            self.logger.info("Interpolating poses at HSI timestamps...")
            
            # Extract pose timestamps and data
            pose_timestamps = poses_df['timestamp'].values
            positions = poses_df[['pos_x', 'pos_y', 'pos_z']].values
            quaternions = poses_df[['rot_x', 'rot_y', 'rot_z', 'rot_w']].values
            
            # Check temporal overlap
            pose_time_min, pose_time_max = pose_timestamps.min(), pose_timestamps.max()
            hsi_time_min, hsi_time_max = hsi_timestamps.min(), hsi_timestamps.max()
            
            self.logger.info(f"Pose time range: {pose_time_min:.3f} to {pose_time_max:.3f}")
            self.logger.info(f"HSI time range: {hsi_time_min:.3f} to {hsi_time_max:.3f}")
            
            # Find HSI timestamps within pose time range
            valid_mask = (hsi_timestamps >= pose_time_min) & (hsi_timestamps <= pose_time_max)
            valid_hsi_timestamps = hsi_timestamps[valid_mask]
            valid_indices = np.where(valid_mask)[0]
            
            if len(valid_hsi_timestamps) == 0:
                self.logger.error("No HSI timestamps overlap with pose time range")
                return None
            
            self.logger.info(f"Interpolating {len(valid_hsi_timestamps)} HSI timestamps within pose range")
            
            # Linear interpolation for positions
            interpolated_positions = np.zeros((len(valid_hsi_timestamps), 3))
            for i in range(3):
                interpolated_positions[:, i] = np.interp(valid_hsi_timestamps, pose_timestamps, positions[:, i])
            
            # Slerp interpolation for attitudes
            try:
                # Create Rotation objects from quaternions
                rotations = Rotation.from_quat(quaternions)
                
                # Create Slerp interpolator
                slerp = Slerp(pose_timestamps, rotations)
                
                # Interpolate at HSI timestamps
                interpolated_rotations = slerp(valid_hsi_timestamps)
                interpolated_quaternions = interpolated_rotations.as_quat()
                
                self.logger.info("Successfully applied Slerp interpolation for attitudes")
                
            except Exception as e:
                self.logger.warning(f"Slerp interpolation failed, falling back to linear: {e}")
                
                # Fallback to linear interpolation for quaternions
                interpolated_quaternions = np.zeros((len(valid_hsi_timestamps), 4))
                for i in range(4):
                    interpolated_quaternions[:, i] = np.interp(valid_hsi_timestamps, pose_timestamps, quaternions[:, i])
                
                # Normalize quaternions
                norms = np.linalg.norm(interpolated_quaternions, axis=1)
                interpolated_quaternions = interpolated_quaternions / norms[:, np.newaxis]
            
            # Create interpolated poses DataFrame
            interpolated_poses = pd.DataFrame({
                'hsi_line_index': valid_indices,
                'timestamp': valid_hsi_timestamps,
                'pos_x': interpolated_positions[:, 0],
                'pos_y': interpolated_positions[:, 1],
                'pos_z': interpolated_positions[:, 2],
                'rot_x': interpolated_quaternions[:, 0],
                'rot_y': interpolated_quaternions[:, 1],
                'rot_z': interpolated_quaternions[:, 2],
                'rot_w': interpolated_quaternions[:, 3]
            })
            
            self.logger.info(f"Successfully interpolated {len(interpolated_poses)} poses")
            
            return interpolated_poses
            
        except Exception as e:
            self.logger.error(f"Failed to interpolate poses: {e}")
            return None
    
    def save_synchronized_poses(self, synchronized_poses_df: pd.DataFrame) -> bool:
        """
        Save synchronized poses to CSV file.
        
        Args:
            synchronized_poses_df: Synchronized poses DataFrame
            
        Returns:
            True if saving successful, False otherwise
        """
        try:
            output_dir = Path(self.config.paths.output_directory)
            output_file = output_dir / self.config.paths.hsi_poses_csv
            
            self.logger.info(f"Saving synchronized poses to: {output_file}")
            
            # Save to CSV
            synchronized_poses_df.to_csv(output_file, index=False)
            
            self.logger.info(f"Successfully saved {len(synchronized_poses_df)} synchronized poses")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save synchronized poses: {e}")
            return False
    
    def run_synchronization(self) -> bool:
        """
        Run the complete data synchronization process.
        
        Returns:
            True if synchronization successful, False otherwise
        """
        try:
            self.logger.info("Starting data synchronization process...")
            
            # Step 1: Validate inputs
            if not self.validate_inputs():
                return False
            
            # Step 2: Load consolidated poses
            poses_df = self.load_consolidated_poses()
            if poses_df is None:
                return False
            
            # Step 3: Extract HSI timestamps
            hsi_timestamps = self.extract_hsi_timestamps()
            if hsi_timestamps is None:
                return False
            
            # Step 4: Interpolate poses at HSI timestamps
            synchronized_poses_df = self.interpolate_poses(poses_df, hsi_timestamps)
            if synchronized_poses_df is None:
                return False
            
            # Step 5: Save synchronized poses
            if not self.save_synchronized_poses(synchronized_poses_df):
                return False
            
            self.logger.info("Data synchronization completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Data synchronization failed: {e}")
            return False


def run_data_synchronization(config_or_path: Union[str, Path, Config]) -> bool:
    """
    Main entry point for data synchronization.
    
    Args:
        config_or_path: Configuration file path or Config object
        
    Returns:
        True if synchronization successful, False otherwise
    """
    try:
        synchronizer = DataSynchronizer(config_or_path)
        return synchronizer.run_synchronization()
    except Exception as e:
        logger.error(f"Failed to create data synchronizer: {e}")
        return False
