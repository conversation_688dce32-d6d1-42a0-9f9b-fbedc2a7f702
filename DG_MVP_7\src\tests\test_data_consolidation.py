"""
Unit tests for DataConsolidator class.

Tests data consolidation logic, parsing, and validation.
"""

import pytest
import json
import tempfile
import pandas as pd
from pathlib import Path
from unittest.mock import Mock, patch, mock_open

from ..data_consolidation import DataConsolidator, run_data_consolidation
from ..config_models import Config


class TestDataConsolidator:
    """Test DataConsolidator class functionality."""
    
    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration for testing."""
        config = Mock(spec=Config)
        
        # Mock paths
        config.paths.webodm_data_directory = "data/WebODM/"
        config.paths.output_directory = "output/"
        config.paths.shots_geojson_file = "shots.geojson"
        config.paths.haip_files_subdirectory = "haip_files/"
        config.paths.consolidated_webodm_poses_csv = "consolidated_poses.csv"
        
        # Mock parameters
        config.parameters.webodm_consolidation.haip_timestamp_key = "rgb"
        
        # Mock processing options
        config.processing_options.log_level = "INFO"
        
        return config
    
    @pytest.fixture
    def sample_shots_geojson(self):
        """Create sample shots GeoJSON data."""
        return {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "properties": {
                        "id": "shot_001",
                        "filename": "image_001.jpg",
                        "rotation": [0.1, 0.2, 0.3, 0.9]
                    },
                    "geometry": {
                        "type": "Point",
                        "coordinates": [1000.0, 2000.0, 100.0]
                    }
                },
                {
                    "type": "Feature",
                    "properties": {
                        "id": "shot_002",
                        "filename": "image_002.jpg",
                        "rotation": [0.2, 0.1, 0.4, 0.8]
                    },
                    "geometry": {
                        "type": "Point",
                        "coordinates": [1001.0, 2001.0, 101.0]
                    }
                }
            ]
        }
    
    @pytest.fixture
    def sample_haip_data(self):
        """Create sample HAIP data."""
        return [
            {
                "filename": "haip_001.haip",
                "rgb": "1234567890.123",
                "timestamp": 1234567890.123,
                "additional_data": "value1"
            },
            {
                "filename": "haip_002.haip", 
                "rgb": "1234567891.456",
                "timestamp": 1234567891.456,
                "additional_data": "value2"
            }
        ]
    
    def test_data_consolidator_initialization(self, mock_config):
        """Test DataConsolidator initialization."""
        consolidator = DataConsolidator(mock_config)
        
        assert consolidator.config == mock_config
        assert consolidator.logger is not None
    
    def test_load_shots_geojson_success(self, mock_config, sample_shots_geojson):
        """Test successful loading of shots GeoJSON."""
        consolidator = DataConsolidator(mock_config)
        
        # Mock file operations
        with patch('builtins.open', mock_open(read_data=json.dumps(sample_shots_geojson))):
            with patch('pathlib.Path.exists', return_value=True):
                shots_data = consolidator.load_shots_geojson()
        
        assert shots_data is not None
        assert 'features' in shots_data
        assert len(shots_data['features']) == 2
    
    def test_load_shots_geojson_invalid_format(self, mock_config):
        """Test loading of invalid GeoJSON format."""
        consolidator = DataConsolidator(mock_config)
        
        invalid_geojson = {"type": "FeatureCollection"}  # Missing 'features'
        
        with patch('builtins.open', mock_open(read_data=json.dumps(invalid_geojson))):
            with patch('pathlib.Path.exists', return_value=True):
                shots_data = consolidator.load_shots_geojson()
        
        assert shots_data is None
    
    def test_load_haip_files_success(self, mock_config, sample_haip_data):
        """Test successful loading of HAIP files."""
        consolidator = DataConsolidator(mock_config)
        
        # Mock directory listing and file reading
        mock_haip_files = [Path("haip_001.haip"), Path("haip_002.haip")]
        
        def mock_file_content(file_path, *args, **kwargs):
            if "haip_001.haip" in str(file_path):
                return mock_open(read_data=json.dumps(sample_haip_data[0]))(*args, **kwargs)
            elif "haip_002.haip" in str(file_path):
                return mock_open(read_data=json.dumps(sample_haip_data[1]))(*args, **kwargs)
            return mock_open(read_data="{}")(*args, **kwargs)
        
        with patch('pathlib.Path.glob', return_value=mock_haip_files):
            with patch('builtins.open', side_effect=mock_file_content):
                haip_data_list = consolidator.load_haip_files()
        
        assert haip_data_list is not None
        assert len(haip_data_list) == 2
        assert all('timestamp' in data for data in haip_data_list)
    
    def test_load_haip_files_no_files(self, mock_config):
        """Test loading HAIP files when no files exist."""
        consolidator = DataConsolidator(mock_config)
        
        with patch('pathlib.Path.glob', return_value=[]):
            haip_data_list = consolidator.load_haip_files()
        
        assert haip_data_list is None
    
    def test_consolidate_poses_success(self, mock_config, sample_shots_geojson, sample_haip_data):
        """Test successful pose consolidation."""
        consolidator = DataConsolidator(mock_config)
        
        poses_df = consolidator.consolidate_poses(sample_shots_geojson, sample_haip_data)
        
        assert poses_df is not None
        assert len(poses_df) == 2
        
        # Check required columns
        required_columns = ['shot_id', 'filename', 'pos_x', 'pos_y', 'pos_z', 
                          'rot_x', 'rot_y', 'rot_z', 'rot_w', 'timestamp', 'haip_matched']
        for col in required_columns:
            assert col in poses_df.columns
        
        # Check data values
        assert poses_df.iloc[0]['shot_id'] == 'shot_001'
        assert poses_df.iloc[0]['pos_x'] == 1000.0
        assert poses_df.iloc[0]['pos_y'] == 2000.0
        assert poses_df.iloc[0]['pos_z'] == 100.0
    
    def test_consolidate_poses_no_shots(self, mock_config):
        """Test pose consolidation with no shot features."""
        consolidator = DataConsolidator(mock_config)
        
        empty_geojson = {"type": "FeatureCollection", "features": []}
        haip_data = []
        
        poses_df = consolidator.consolidate_poses(empty_geojson, haip_data)
        
        assert poses_df is None
    
    def test_consolidate_poses_invalid_geometry(self, mock_config, sample_haip_data):
        """Test pose consolidation with invalid geometry."""
        consolidator = DataConsolidator(mock_config)
        
        invalid_geojson = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "properties": {"id": "shot_001", "filename": "image_001.jpg", "rotation": [0, 0, 0, 1]},
                    "geometry": {"type": "LineString", "coordinates": [[0, 0], [1, 1]]}  # Invalid geometry
                }
            ]
        }
        
        poses_df = consolidator.consolidate_poses(invalid_geojson, sample_haip_data)
        
        # Should handle invalid geometry gracefully
        assert poses_df is not None
        assert len(poses_df) == 0  # No valid poses
    
    def test_save_consolidated_poses(self, mock_config):
        """Test saving consolidated poses to CSV."""
        consolidator = DataConsolidator(mock_config)
        
        # Create sample DataFrame
        poses_df = pd.DataFrame({
            'shot_id': ['shot_001', 'shot_002'],
            'pos_x': [1000.0, 1001.0],
            'pos_y': [2000.0, 2001.0],
            'pos_z': [100.0, 101.0],
            'rot_x': [0.1, 0.2],
            'rot_y': [0.2, 0.1],
            'rot_z': [0.3, 0.4],
            'rot_w': [0.9, 0.8]
        })
        
        with patch('pandas.DataFrame.to_csv') as mock_to_csv:
            with patch('pathlib.Path.mkdir'):
                success = consolidator.save_consolidated_poses(poses_df)
        
        assert success is True
        mock_to_csv.assert_called_once()
    
    def test_run_consolidation_success(self, mock_config, sample_shots_geojson, sample_haip_data):
        """Test complete consolidation process."""
        consolidator = DataConsolidator(mock_config)
        
        # Mock all the individual methods
        with patch.object(consolidator, 'validate_inputs', return_value=True):
            with patch.object(consolidator, 'load_shots_geojson', return_value=sample_shots_geojson):
                with patch.object(consolidator, 'load_haip_files', return_value=sample_haip_data):
                    with patch.object(consolidator, 'consolidate_poses') as mock_consolidate:
                        with patch.object(consolidator, 'save_consolidated_poses', return_value=True):
                            # Mock consolidate_poses to return a valid DataFrame
                            mock_consolidate.return_value = pd.DataFrame({'test': [1, 2]})
                            
                            success = consolidator.run_consolidation()
        
        assert success is True
    
    def test_run_consolidation_validation_failure(self, mock_config):
        """Test consolidation process with validation failure."""
        consolidator = DataConsolidator(mock_config)
        
        with patch.object(consolidator, 'validate_inputs', return_value=False):
            success = consolidator.run_consolidation()
        
        assert success is False
    
    def test_validate_inputs_missing_files(self, mock_config):
        """Test input validation with missing files."""
        consolidator = DataConsolidator(mock_config)
        
        with patch('src.utils.validate_directory_exists', return_value=True):
            with patch('src.utils.validate_file_exists', return_value=False):
                result = consolidator.validate_inputs()
        
        assert result is False
    
    def test_run_data_consolidation_function(self, mock_config):
        """Test the run_data_consolidation function."""
        with patch('src.data_consolidation.DataConsolidator') as mock_consolidator_class:
            mock_consolidator = Mock()
            mock_consolidator.run_consolidation.return_value = True
            mock_consolidator_class.return_value = mock_consolidator
            
            success = run_data_consolidation(mock_config)
        
        assert success is True
        mock_consolidator_class.assert_called_once_with(mock_config)
        mock_consolidator.run_consolidation.assert_called_once()


def test_placeholder():
    """Placeholder test to ensure pytest runs."""
    assert True
