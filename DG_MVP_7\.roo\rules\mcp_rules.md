# MCP Tool Usage Rules: Filesystem Server

## Rule: Using Full Paths with `read_file` (and similar file operations)

When using tools from the `github.com/modelcontextprotocol/servers/tree/main/src/filesystem` MCP server, such as `read_file` or `read_multiple_files`:

*   **Default to Relative Paths:** Initially, attempt to use paths relative to the current workspace directory (e.g., `research/data/file.txt`).
*   **Switch to Full Absolute Paths if Access Denied:** If you encounter an "Access denied - path outside allowed directories" error, it may indicate that the filesystem MCP server has a strict configuration for its allowed directories, potentially limited to the exact workspace root path provided during its startup.
    *   In such cases, **you MUST switch to using full absolute paths** for the `path` parameter.
    *   Example: Instead of `research/04_synthesis/01_integrated_model.md`, use `C:\Users\<USER>\VSCode\DG_MVP_7\research\04_synthesis\01_integrated_model.md` (adjusting the base path `C:\Users\<USER>\VSCode\DG_MVP_7` to the correct current workspace directory).
*   **Verify Workspace Directory:** Always ensure the base of your absolute path matches the `Current Workspace Directory` provided in the `SYSTEM INFORMATION` or `environment_details`.

This approach helps ensure successful file operations when the MCP server's path resolution or permission model is restrictive.