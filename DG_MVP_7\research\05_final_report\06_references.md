# References

This section lists key concepts, libraries, and generalized types of sources that informed the research and recommendations. Specific citations (e.g., `[S1-1]`) refer to placeholders used during the initial data collection phase ([`research/02_data_collection/01_primary_findings.md`](../../02_data_collection/01_primary_findings.md)) and represent the types of academic papers, technical reports, and documentation consulted via Perplexity AI.

## Key Concepts and Methodologies:

*   **Direct Georeferencing:** The core subject, involving the determination of ground coordinates directly from sensor position and orientation data.
*   **Sensor Modeling:**
    *   **Interior Orientation Parameters (IOPs):** Focal length, principal point, lens distortion (e.g., <PERSON>'s model).
    *   **Exterior Orientation Parameters (EOPs):** Position (X, Y, Z) and attitude (roll, pitch, yaw) of the sensor.
    *   **Boresight Alignment:** Angular relationship between the sensor and IMU reference frames.
    *   **Lever Arm Correction:** Spatial offset vector(s) between GNSS antenna, IMU, and sensor perspective center.
*   **Collinearity Equations:** Fundamental mathematical model relating image coordinates, sensor EOPs, IOPs, and ground coordinates. Modified versions are used for linescan sensors.
*   **INS/GNSS Integration:** Tightly coupled systems using Kalman filtering for optimal navigation solutions.
*   **Ray-DSM Intersection:** Algorithms for finding the intersection point of a sensor's line-of-sight vector with a Digital Surface Model (DSM).
    *   **Digital Differential Analyzer (DDA):** For raster DSMs.
    *   **Ray Marching:** For raster DSMs.
    *   **Möller-Trumbore Algorithm:** For triangulated irregular network (TIN) DSMs.
    *   **Bounding Volume Hierarchy (BVH):** Acceleration structure for ray tracing.
*   **Error Propagation:**
    *   **Covariance Propagation:** Analytical method using Jacobian matrices.
    *   **Monte Carlo Simulation:** Empirical method for error analysis.
*   **Calibration:**
    *   **Laboratory Calibration:** Using specialized equipment and patterns.
    *   **In-Flight Calibration/Validation:** Using Ground Control Points (GCPs), overlapping imagery, or dedicated calibration fields.
*   **Timestamp Synchronization:** Critical for correlating sensor data with navigation data.
*   **Coordinate Reference Systems (CRS):** Consistent handling of transformations between different geodetic and projected coordinate systems.

## Referenced Python Libraries (Examples):

*   **`point_cloud_utils`:** For high-performance ray-mesh intersection (wraps Intel Embree).
*   **`Rasterio` / `GDAL`:** For reading, writing, and manipulating raster geospatial data (including DSMs).
*   **`PyVista` / `Trimesh`:** For 3D mesh creation (e.g., converting DSM tiles to meshes) and manipulation.
*   **`NumPy` / `SciPy`:** For numerical computation, array manipulation, and scientific algorithms.
*   **`PyProj`:** For cartographic projections and coordinate transformations.
*   **`Dask`:** For parallel computing and out-of-core processing of large datasets.
*   **`rtree`:** For R-tree spatial indexing.
*   **`Pydantic`:** For data validation, particularly for configuration files.
*   **`pytest`:** For developing and running unit and integration tests.
*   **`logging` (Python standard library):** For implementing detailed logging.
*   **`tqdm`:** For creating progress bars.

## Types of External Sources Consulted (via Perplexity AI):

*   **Academic Papers and Journals:**
    *   ISPRS Journal of Photogrammetry and Remote Sensing
    *   Remote Sensing (MDPI)
    *   Photogrammetric Engineering & Remote Sensing (PE&RS)
    *   IEEE Transactions on Geoscience and Remote Sensing
*   **Conference Proceedings:**
    *   ISPRS Archives and Annals
    *   IEEE IGARSS (International Geoscience and Remote Sensing Symposium)
*   **Technical Reports and Documentation:**
    *   From sensor manufacturers (e.g., HySpex).
    *   From space agencies or research institutions (e.g., DLR for EnMAP mission).
    *   Software documentation (e.g., PBRT - Physically Based Rendering Toolkit, for ray tracing concepts).
*   **PhD Theses and Dissertations:** On topics related to direct georeferencing, sensor calibration, and error analysis.
*   **Industry Best Practices and White Papers:** From companies involved in airborne mapping and remote sensing.

While specific URLs or DOIs were not retained from the Perplexity AI interactions for this report, the research drew upon the collective knowledge encapsulated in these types of sources to formulate the findings and recommendations. The placeholder citations (e.g., `[S1-1]`) in [`research/02_data_collection/01_primary_findings.md`](../../02_data_collection/01_primary_findings.md) indicate the types of documents that supported each finding during the initial research phase.