import pandas as pd
import numpy as np
from scipy.spatial.transform import Rotation
import toml
import os # Keep for os.path.join if still needed, but prefer Path
import rasterio
# import datetime # No longer explicitly used after removing timestamp from output filename
from scipy.interpolate import RegularGridInterpolator
from scipy.optimize import brentq
import logging
from pathlib import Path
from typing import Optional, Tuple, Any, Dict, Union
from pydantic import ValidationError

# Assuming config_models.py is in the same directory (MVP)
try:
    from config_models import Config
except ImportError: # Fallback for direct execution if <PERSON> is not in PYTHONPATH
    from .config_models import Config


logger = logging.getLogger(__name__)

# --- Coordinate System Definitions ---
# IMU-Body Coordinate System (Assumed FRD - Forward, Right, Down):
#   X-axis: Forward (in the direction of flight of the drone)
#   Y-axis: Right (from the pilot's perspective)
#   Z-axis: Down (towards the Earth)
#
# Sensor Coordinate System (Linescanner - Assumed):
#   Z_sensor-axis: Optical axis (ideally pointing downwards for nadir shots)
#   X_sensor-axis: Along the scan line (across-track)
#   Y_sensor-axis: Along the flight direction of the sensor (along-track, perpendicular to scan line)
#
# --- Positive Rotation Definitions (Right-Hand Rule) ---
# Based on the IMU-Body Coordinate System:
#   Positive Roll: Rotation around the X-body axis.
#   Positive Pitch: Rotation around the Y-body axis.
#   Positive Yaw: Rotation around the Z-body axis.
#
# --- Boresight Angles ---
# These angles define the rotation FROM the Body system TO the Sensor system.
# R_body_to_sensor = Rz(yaw_bs) @ Ry(pitch_bs) @ Rx(roll_bs)
# The script calculates R_sensor_to_body = R_body_to_sensor.T

def load_config_for_georef(config_path_str: str) -> Optional[Config]:
    """Loads and validates the TOML configuration file for georeferencing script."""
    config_path = Path(config_path_str)
    if not config_path.is_absolute():
        base_path = Path(__file__).parent
        config_path = base_path / config_path_str
        logger.info(f"Relative config path '{config_path_str}' resolved to '{config_path.resolve()}' for georef script.")
    
    try:
        logger.info(f"Attempting to load configuration from: {config_path.resolve()}")
        config_data = toml.load(config_path)
        validated_config = Config(**config_data)
        logger.info("Configuration loaded and validated successfully for georeferencing.")

        log_level_str = validated_config.processing_options.log_level
        numeric_level = getattr(logging, log_level_str.upper(), logging.INFO)
        if not logging.getLogger().handlers:
            logging.basicConfig(level=numeric_level, format='%(asctime)s - %(levelname)s - %(module)s - %(message)s')
        else:
            logging.getLogger().setLevel(numeric_level)
        logger.setLevel(numeric_level)
        logger.info(f"Logging level set to: {log_level_str} for georeferencing script.")
        return validated_config
    except FileNotFoundError:
        logger.error(f"Configuration file not found at {config_path.resolve()}")
        return None
    except ValidationError as e:
        logger.error(f"Configuration validation error: {e}")
        return None
    except Exception as e:
        logger.error(f"An unexpected error occurred while loading or validating the configuration: {e}")
        return None

def parse_hsi_header(hdr_file_path: Path) -> Optional[Tuple[int, int, np.ndarray]]:
    """
    Liest die HSI-Header-Datei und extrahiert 'samples', 'lines' und 'lever arm'.

    Args:
        hdr_file_path (Path): Pfad zur .hdr-Datei.

    Returns:
        Optional[Tuple[int, int, np.ndarray]]: (samples, lines, lever_arm_body) or None on error.
               samples (int): Anzahl der Samples pro Zeile.
               lines (int): Anzahl der Zeilen.
               lever_arm_body (np.array): Lever-Arm-Vektor [x, y, z] in Metern.
    """
    header_data: Dict[str, Any] = {}
    try:
        with open(hdr_file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    if key == 'samples':
                        header_data['samples'] = int(value)
                    elif key == 'lines':
                        header_data['lines'] = int(value)
                    elif key.startswith('OffsetBetweenMainAntennaAndTargetPoint'):
                        value = value.strip().replace('(', '').replace(')', '')
                        parts = value.split(',')
                        if len(parts) == 3:
                            header_data['lever_arm'] = np.array([float(p.strip()) / 1000.0 for p in parts])
                        else:
                            logger.warning(f"Could not parse 'OffsetBetweenMainAntennaAndTargetPoint' from '{value}' in {hdr_file_path}.")
                            header_data['lever_arm'] = np.array([0.0, 0.0, 0.0]) 
                    elif key == 'lever arm': 
                        value = value.replace('{', '').replace('}', '').replace(',', ' ')
                        try:
                            header_data['lever_arm'] = np.array([float(v) for v in value.split()])
                        except ValueError:
                            logger.warning(f"Could not parse 'lever arm' from '{value}' in {hdr_file_path}.")
                            header_data['lever_arm'] = np.array([0.0, 0.0, 0.0])
    except FileNotFoundError:
        logger.error(f"HSI Header file not found: {hdr_file_path}")
        return None
    except Exception as e:
        logger.error(f"Error parsing HSI header file {hdr_file_path}: {e}", exc_info=True)
        return None

    if 'samples' not in header_data:
        logger.error(f"'samples' not found in header file: {hdr_file_path}")
        return None
    if 'lines' not in header_data:
        logger.error(f"'lines' not found in header file: {hdr_file_path}")
        return None
    if 'lever_arm' not in header_data:
        logger.warning(f"No 'lever arm' or 'OffsetBetweenMainAntennaAndTargetPoint' found in {hdr_file_path}. Defaulting to [0,0,0].")
        header_data['lever_arm'] = np.array([0.0, 0.0, 0.0])
        
    return header_data['samples'], header_data['lines'], header_data['lever_arm']

def parse_sensor_model(sensor_model_path: Path, num_samples: int) -> Optional[Tuple[np.ndarray, np.ndarray]]:
    """
    Liest das Sensor-Winkelmodell.
    Winkel werden als Radiant interpretiert.
    """
    try:
        sensor_data = pd.read_csv(sensor_model_path, delim_whitespace=True, header=None, skiprows=1,
                                  names=['pixel_index', 'vinkelx_rad', 'vinkely_rad'])
    except Exception:
        try:
            sensor_data = pd.read_csv(sensor_model_path, delim_whitespace=True, header=None,
                                      names=['pixel_index', 'vinkelx_rad', 'vinkely_rad'])
        except pd.errors.ParserError:
            try:
                sensor_data_raw = pd.read_csv(sensor_model_path, delim_whitespace=True, header=None,
                                          names=['vinkelx_rad', 'vinkely_rad'])
                sensor_data = pd.DataFrame({
                    'pixel_index': np.arange(len(sensor_data_raw)),
                    'vinkelx_rad': sensor_data_raw['vinkelx_rad'],
                    'vinkely_rad': sensor_data_raw['vinkely_rad']
                })
            except Exception as final_e:
                logger.error(f"Could not parse sensor model file: {sensor_model_path}. Error: {final_e}", exc_info=True)
                return None
        except Exception as e: 
            logger.error(f"Could not parse sensor model file (attempt 2): {sensor_model_path}. Error: {e}", exc_info=True)
            return None

    if len(sensor_data) != num_samples:
        logger.warning(f"Number of entries in sensor model ({len(sensor_data)}) does not match 'samples' ({num_samples}) from header in {sensor_model_path}.")
        if len(sensor_data) < num_samples:
            logger.error(f"Too few entries in sensor model. Expected: {num_samples}, Found: {len(sensor_data)}. Aborting.")
            return None
        logger.info(f"Taking the first {num_samples} entries from sensor model.")
        sensor_data = sensor_data.iloc[:num_samples]

    logger.info("Assuming 'vinkelx_rad' and 'vinkely_rad' columns from sensor model are in RADIANS.")
    vinkelx_rad_all_pixels = sensor_data['vinkelx_rad'].values.astype(float)
    vinkely_rad_all_pixels = sensor_data['vinkely_rad'].values.astype(float)
    
    return vinkelx_rad_all_pixels, vinkely_rad_all_pixels

def calculate_ray_dsm_intersection(
    P_sensor: np.ndarray, 
    d_world_normalized: np.ndarray, 
    interpolator: RegularGridInterpolator, 
    bounds: rasterio.coords.BoundingBox, 
    nodata_value: Optional[Union[float, int]], 
    max_dist: float, 
    initial_step: float, 
    tolerance: float
) -> Tuple[float, float, float]:
    """
    Calculates the intersection of a 3D ray with a Digital Surface Model (DSM).
    """
    def get_dsm_z(x, y):
        if not (bounds.left <= x <= bounds.right and bounds.bottom <= y <= bounds.top):
            return np.nan
        z_val = interpolator((y, x)) 
        if nodata_value is not None and not np.isnan(nodata_value) and np.isclose(z_val, nodata_value):
            return np.nan
        return z_val

    def func_to_solve(t):
        P_intersect = P_sensor + t * d_world_normalized
        x_ray, y_ray, z_ray = P_intersect[0], P_intersect[1], P_intersect[2]
        z_dsm = get_dsm_z(x_ray, y_ray)
        if np.isnan(z_dsm):
            return z_ray - (P_sensor[2] - 10000) 
        return z_ray - z_dsm

    z_ray_start = P_sensor[2]
    z_dsm_start = get_dsm_z(P_sensor[0], P_sensor[1])
    t_current = 0.0

    if np.isnan(z_dsm_start):
        found_entry = False
        for t_entry_candidate in np.arange(initial_step, max_dist, initial_step):
            p_candidate = P_sensor + t_entry_candidate * d_world_normalized
            z_dsm_candidate = get_dsm_z(p_candidate[0], p_candidate[1])
            if not np.isnan(z_dsm_candidate):
                t_current = t_entry_candidate
                z_ray_start = p_candidate[2]
                z_dsm_start = z_dsm_candidate
                found_entry = True
                break
        if not found_entry:
            return np.nan, np.nan, np.nan

    diff_prev = z_ray_start - z_dsm_start
    t_prev = t_current
    step = initial_step
    t_search = t_current

    while t_search <= max_dist:
        t_search += step
        P_current_ray = P_sensor + t_search * d_world_normalized
        x_ray_curr, y_ray_curr, z_ray_curr = P_current_ray[0], P_current_ray[1], P_current_ray[2]
        z_dsm_curr = get_dsm_z(x_ray_curr, y_ray_curr)

        if np.isnan(z_dsm_curr):
            return np.nan, np.nan, np.nan
        diff_curr = z_ray_curr - z_dsm_curr

        if diff_prev * diff_curr <= 0:
            try:
                val_at_a = func_to_solve(t_prev)
                val_at_b = func_to_solve(t_search)
                if np.isnan(val_at_a) or np.isnan(val_at_b):
                    if np.isnan(val_at_a) and not np.isnan(func_to_solve(t_prev + tolerance / 100)): val_at_a = func_to_solve(t_prev + tolerance/100)
                    if np.isnan(val_at_b) and not np.isnan(func_to_solve(t_search - tolerance/100)): val_at_b = func_to_solve(t_search - tolerance/100)
                    if np.isnan(val_at_a) or np.isnan(val_at_b) or val_at_a * val_at_b > 0:
                        diff_prev = diff_curr
                        t_prev = t_search
                        step = initial_step
                        continue
                if val_at_a * val_at_b > 0:
                    if np.isclose(val_at_a, 0, atol=tolerance):
                        t_intersect = t_prev
                        P_ground = P_sensor + t_intersect * d_world_normalized
                        return P_ground[0], P_ground[1], P_ground[2]
                    if np.isclose(val_at_b, 0, atol=tolerance):
                        t_intersect = t_search
                        P_ground = P_sensor + t_intersect * d_world_normalized
                        return P_ground[0], P_ground[1], P_ground[2]
                    diff_prev = diff_curr
                    t_prev = t_search
                    continue
                t_intersect = brentq(func_to_solve, t_prev, t_search, xtol=tolerance, rtol=tolerance)
                P_ground = P_sensor + t_intersect * d_world_normalized
                return P_ground[0], P_ground[1], P_ground[2]
            except ValueError:
                pass
            except Exception:
                return np.nan, np.nan, np.nan
        diff_prev = diff_curr
        t_prev = t_search
    return np.nan, np.nan, np.nan

def run_georeferencing(config_or_path: Union[str, Config]) -> bool:
    """
    Führt die Georeferenzierung der HSI-Pixel basierend auf der gegebenen Konfigurationsdatei durch.
    """
    if isinstance(config_or_path, str):
        config = load_config_for_georef(config_or_path)
        if config is None:
            return False
    elif isinstance(config_or_path, Config):
        config = config_or_path
        if not logging.getLogger().handlers: 
             logging.basicConfig(level=config.processing_options.log_level.upper(), format='%(asctime)s - %(levelname)s - %(module)s - %(message)s')
        logger.setLevel(config.processing_options.log_level.upper())
    else:
        logger.error("Invalid argument type for config_or_path. Must be a path string or Config object.")
        return False

    logger.info(f"Starting HSI pixel georeferencing...")

    base_hsi_data_dir = Path(config.paths.hsi_data_directory)
    hdr_file_path = base_hsi_data_dir / f"{config.paths.hsi_base_filename}.hdr"
    sensor_model_path = base_hsi_data_dir / config.paths.sensor_model_file
    
    base_output_dir = Path(config.paths.output_directory)
    try:
        base_output_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Output directory: {base_output_dir.resolve()} (ensured)")
    except OSError as e:
        logger.error(f"Error creating output directory {base_output_dir.resolve()}: {e}")
        return False

    poses_file_path = base_output_dir / config.paths.hsi_poses_csv
    logger.info(f"Expecting HSI Poses CSV at: {poses_file_path.resolve()}")
    output_file_path = base_output_dir / config.paths.georeferenced_pixels_csv
    logger.info(f"Output Georeferenced Pixels CSV will be at: {output_file_path.resolve()}")

    logger.info("Loading georeferencing parameters from validated config...")
    boresight_roll_deg = config.sensor_model.boresight_alignment_deg.roll_offset_deg
    boresight_pitch_deg = config.sensor_model.boresight_alignment_deg.pitch_offset_deg
    boresight_yaw_deg = config.sensor_model.boresight_alignment_deg.yaw_offset_deg
    logger.info(f"  Boresight Angles (deg): Roll={boresight_roll_deg}, Pitch={boresight_pitch_deg}, Yaw={boresight_yaw_deg}")

    gnss_to_imu_body = np.array([
        config.sensor_model.lever_arms_meters.gnss_to_imu_x_m,
        config.sensor_model.lever_arms_meters.gnss_to_imu_y_m,
        config.sensor_model.lever_arms_meters.gnss_to_imu_z_m
    ])
    imu_to_sensor_body = np.array([
        config.sensor_model.lever_arms_meters.imu_to_sensor_x_m,
        config.sensor_model.lever_arms_meters.imu_to_sensor_y_m,
        config.sensor_model.lever_arms_meters.imu_to_sensor_z_m
    ])
    logger.info(f"  Lever Arm GNSS to IMU (m): {gnss_to_imu_body}")
    logger.info(f"  Lever Arm IMU to Sensor (m): {imu_to_sensor_body}")
    effective_lever_arm_body = imu_to_sensor_body 
    logger.debug(f"  Using IMU_to_Sensor as 'effective_lever_arm_body' for now: {effective_lever_arm_body}")

    z_ground_method = config.parameters.georeferencing.z_ground_calculation_method
    z_ground_offset = config.parameters.georeferencing.z_ground_offset_meters
    z_ground_fixed = config.parameters.georeferencing.z_ground_fixed_value_meters
    
    dsm_interpolator = None
    dsm_bounds: Optional[rasterio.coords.BoundingBox] = None
    dsm_nodata_value = config.dsm_parameters.nodata_value 
    if dsm_nodata_value is None: 
        dsm_nodata_value = np.nan

    ray_max_dist = config.dsm_parameters.ray_dsm_max_search_dist_m
    ray_initial_step = config.dsm_parameters.ray_dsm_step_m
    ray_bisection_tol = config.dsm_parameters.ray_dsm_bisection_tolerance_m

    scale_vinkel_x = config.parameters.georeferencing.scale_vinkel_x
    offset_vinkel_x = config.parameters.georeferencing.offset_vinkel_x
    logger.info(f"  Sensor Model Correction: scale_vinkel_x={scale_vinkel_x}, offset_vinkel_x={offset_vinkel_x}")
    logger.info(f"  Z_ground Method: {z_ground_method}, Offset: {z_ground_offset}, Fixed Value: {z_ground_fixed}")
    if z_ground_method == "dsm_intersection":
        logger.info(f"  Ray Marching Params: MaxDist={ray_max_dist}m, Step={ray_initial_step}m, Tol={ray_bisection_tol}m")

    if z_ground_method == "dsm_intersection":
        logger.info("Loading DSM for 'dsm_intersection' method...")
        try:
            dsm_path_str = config.dsm_parameters.path 
            dsm_path = Path(dsm_path_str)
            if not dsm_path.is_absolute():
                 dsm_path = Path.cwd() / dsm_path_str
            
            logger.info(f"  Attempting to open DSM: {dsm_path.resolve()}")
            with rasterio.open(dsm_path) as src:
                dsm_array = src.read(1).astype(np.float32)
                dsm_transform = src.transform
                if config.dsm_parameters.nodata_value is not None:
                    dsm_nodata_value = float(config.dsm_parameters.nodata_value)
                elif src.nodatavals[0] is not None:
                    dsm_nodata_value = float(src.nodatavals[0])
                else:
                    dsm_nodata_value = np.nan
                
                dsm_bounds = src.bounds
                logger.info(f"  DSM loaded successfully. Shape: {dsm_array.shape}, Effective NoData for processing: {dsm_nodata_value}")
                logger.info(f"  DSM Transform: {dsm_transform}")
                logger.info(f"  DSM Bounds: {dsm_bounds}")

            x_coords_dsm = np.array([dsm_transform.c + dsm_transform.a * (i + 0.5) for i in range(dsm_array.shape[1])])
            y_coords_dsm_raw = np.array([dsm_transform.f + dsm_transform.e * (i + 0.5) for i in range(dsm_array.shape[0])])
            dsm_array_for_interp = np.copy(dsm_array)

            if dsm_transform.e < 0:
                y_coords_dsm_asc = y_coords_dsm_raw[::-1]
                dsm_array_for_interp = dsm_array_for_interp[::-1, :]
            else:
                y_coords_dsm_asc = y_coords_dsm_raw

            if not np.isnan(dsm_nodata_value):
                nodata_mask = np.isclose(dsm_array_for_interp, dsm_nodata_value)
                dsm_array_for_interp[nodata_mask] = np.nan
            
            dsm_interpolator = RegularGridInterpolator(
                (y_coords_dsm_asc, x_coords_dsm), dsm_array_for_interp,
                method='linear', bounds_error=False, fill_value=np.nan
            )
            logger.info("  DSM Interpolator (RegularGridInterpolator) initialized successfully.")

        except FileNotFoundError:
            logger.error(f"DSM file not found at: {dsm_path.resolve() if 'dsm_path' in locals() else dsm_path_str}")
            return False
        except rasterio.errors.RasterioIOError as e:
            logger.error(f"Rasterio could not open or read DSM file: {dsm_path.resolve() if 'dsm_path' in locals() else dsm_path_str}. Error: {e}")
            return False
        except Exception as e: 
            logger.error(f"An unexpected error occurred while loading or processing the DSM: {e}", exc_info=True)
            return False

    logger.info(f"Reading HSI header file: {hdr_file_path.resolve()}")
    parsed_hdr = parse_hsi_header(hdr_file_path)
    if parsed_hdr is None:
        logger.error(f"Failed to parse HSI header file: {hdr_file_path.resolve()}")
        return False
    num_samples_hdr, num_lines_hdr, lever_arm_from_hdr = parsed_hdr
    logger.info(f"  Header Info: Samples: {num_samples_hdr}, Lines: {num_lines_hdr}, Lever arm from HDR (m): {lever_arm_from_hdr}")
    logger.debug(f"  Effective Lever Arm (m) to be used (IMU_to_Sensor part from config): {effective_lever_arm_body}")

    num_samples = num_samples_hdr
    num_lines = num_lines_hdr

    logger.info(f"Reading sensor model: {sensor_model_path.resolve()}")
    parsed_sensor_model = parse_sensor_model(sensor_model_path, num_samples)
    if parsed_sensor_model is None:
        logger.error(f"Failed to parse sensor model file: {sensor_model_path.resolve()}")
        return False
    vinkelx_rad_all_pixels, vinkely_rad_all_pixels = parsed_sensor_model
    logger.info(f"  Sensor angles loaded for {len(vinkelx_rad_all_pixels)} pixels.")
    logger.debug(f"  Corrected vinkelx_rad range: min={np.min(vinkelx_rad_all_pixels):.3f} rad, max={np.max(vinkelx_rad_all_pixels):.3f} rad")
    
    logger.info(f"Reading poses file: {poses_file_path.resolve()}")
    try:
        poses_df = pd.read_csv(poses_file_path)
    except FileNotFoundError:
        logger.error(f"Poses file not found: {poses_file_path.resolve()}")
        return False
    except Exception as e:
        logger.error(f"Could not read poses file {poses_file_path.resolve()}: {e}", exc_info=True)
        return False
    logger.info(f"  {len(poses_df)} poses loaded.")

    if len(poses_df) != num_lines:
        logger.error(f"Number of poses ({len(poses_df)}) does not match number of HSI lines ({num_lines}).")
        return False
    
    Z_ground_flat_plane = np.nan 
    default_fallback_z_ground = config.parameters.georeferencing.z_ground_fixed_value_meters if config.parameters.georeferencing.z_ground_fixed_value_meters is not None else 100.0

    if 'pos_z' in poses_df.columns and poses_df['pos_z'].notna().any():
        avg_pose_z_val = poses_df['pos_z'].mean()
        offset_for_default = config.parameters.georeferencing.z_ground_offset_meters if config.parameters.georeferencing.z_ground_offset_meters is not None else 20.0
        default_fallback_z_ground = avg_pose_z_val - offset_for_default
    else:
        logger.warning("Column 'pos_z' not in poses DataFrame or contains only NaNs.")
        if config.parameters.georeferencing.z_ground_calculation_method == "avg_pose_z_minus_offset":
            logger.error(f"z_ground_method is 'avg_pose_z_minus_offset', but 'pos_z' is missing or all NaNs. Cannot calculate Z_ground.")
            return False

    if config.parameters.georeferencing.z_ground_calculation_method == "avg_pose_z_minus_offset":
        if 'pos_z' in poses_df.columns and poses_df['pos_z'].notna().any() and config.parameters.georeferencing.z_ground_offset_meters is not None:
            avg_pose_z = poses_df['pos_z'].mean()
            Z_ground_flat_plane = avg_pose_z - config.parameters.georeferencing.z_ground_offset_meters
            logger.info(f"  Z_ground (flat plane) method: 'avg_pose_z_minus_offset'. Avg Z: {avg_pose_z:.3f} m, Offset: {config.parameters.georeferencing.z_ground_offset_meters} m")
        else:
            logger.warning(f"Cannot use 'avg_pose_z_minus_offset', 'pos_z' or 'z_ground_offset_meters' missing/invalid. Using fallback Z_ground: {default_fallback_z_ground:.2f} m")
            Z_ground_flat_plane = default_fallback_z_ground
    elif config.parameters.georeferencing.z_ground_calculation_method == "fixed_value":
        Z_ground_flat_plane = config.parameters.georeferencing.z_ground_fixed_value_meters if config.parameters.georeferencing.z_ground_fixed_value_meters is not None else default_fallback_z_ground
        logger.info(f"  Z_ground (flat plane) method: 'fixed_value'. Value: {Z_ground_flat_plane:.2f} m")
    elif config.parameters.georeferencing.z_ground_calculation_method == "dsm_intersection":
        if dsm_interpolator is None: 
            logger.error("z_ground_method is 'dsm_intersection', but DSM interpolator was not initialized (likely DSM loading error).")
            return False
        logger.info("  Z_ground method: 'dsm_intersection'. Z_ground will be calculated per ray from DSM.")
    else: 
        logger.warning(f"Unknown z_ground_calculation_method: '{config.parameters.georeferencing.z_ground_calculation_method}'. Using fallback Z_ground: {default_fallback_z_ground:.2f} m")
        Z_ground_flat_plane = default_fallback_z_ground

    if config.parameters.georeferencing.z_ground_calculation_method != "dsm_intersection":
        if np.isnan(Z_ground_flat_plane): 
            logger.error("Z_ground_flat_plane could not be determined. Script cannot proceed.")
            return False
        logger.info(f"Using Z_ground_flat_plane for projection (non-DSM methods): {Z_ground_flat_plane:.2f} meters")
    
    R_sensor_to_body = Rotation.from_euler('zyx', 
                                           [config.sensor_model.boresight_alignment_deg.yaw_offset_deg,
                                            config.sensor_model.boresight_alignment_deg.pitch_offset_deg,
                                            config.sensor_model.boresight_alignment_deg.roll_offset_deg],
                                           degrees=True).as_matrix()

    logger.info(f"  Boresight Angles (deg) for Sensor->Body (ZYX): Yaw={config.sensor_model.boresight_alignment_deg.yaw_offset_deg}, Pitch={config.sensor_model.boresight_alignment_deg.pitch_offset_deg}, Roll={config.sensor_model.boresight_alignment_deg.roll_offset_deg}")
    logger.debug(f"  R_sensor_to_body (Sensor -> Body):\n{R_sensor_to_body}")
    
    debug_lines = {0, num_lines // 2, num_lines - 1}
    debug_pixels = {0, num_samples // 2, num_samples - 1}
    logger.info(f"Debugging output enabled for lines: {debug_lines}, pixels: {debug_pixels}")
    
    first_pixel_debug_printed = False
    results = []
    nan_intersection_count = 0
    d_world_z_threshold = 1e-6 

    for i in range(num_lines):
        if i % 100 == 0 and i > 0: 
            logger.info(f"Processing HSI line {i+1}/{num_lines}...")
        current_pose = poses_df.iloc[i]
        P_imu_world = np.array([current_pose['pos_x'], current_pose['pos_y'], current_pose['pos_z']])
        
        q_world_to_body = np.array([current_pose['quat_x'], current_pose['quat_y'], 
                                    current_pose['quat_z'], current_pose['quat_w']])
        
        q_norm = np.linalg.norm(q_world_to_body)
        if q_norm < 1e-9:
            logger.warning(f"Quaternion for line {i} has a norm close to zero ({q_norm}). Skipping this line.")
            continue
        q_world_to_body /= q_norm
        
        try:
            interp_rotation_obj = Rotation.from_quat(q_world_to_body)
        except ValueError as e:
            logger.warning(f"Invalid quaternion for line {i}: {q_world_to_body}. Error: {e}. Skipping line.")
            continue

        R_world_to_body = interp_rotation_obj.as_matrix()
        R_body_to_world = R_world_to_body.T

        P_sensor = P_imu_world + R_body_to_world @ effective_lever_arm_body

        for j in range(num_samples):
            vinkelx_rad_corrected = vinkelx_rad_all_pixels[j] * scale_vinkel_x + offset_vinkel_x
            vinkely_rad_corrected = vinkely_rad_all_pixels[j]

            if not first_pixel_debug_printed and i==0 and j==0:
                 logger.debug(f"  First Pixel (line 0, sample 0): Original vinkelx_rad={vinkelx_rad_all_pixels[j]:.5f}, "
                              f"scaled/offset vinkelx_rad={vinkelx_rad_corrected:.5f}")
                 first_pixel_debug_printed = True
            
            d_sensor = np.array([np.tan(vinkelx_rad_corrected), np.tan(vinkely_rad_corrected), 1.0])
            norm_d_sensor = np.linalg.norm(d_sensor)
            if norm_d_sensor < 1e-9: 
                logger.warning(f"LOS vector in sensor frame has near-zero norm for line {i}, pixel {j}. Skipping pixel.")
                results.append([i, j, np.nan, np.nan, np.nan, P_sensor[0], P_sensor[1], P_sensor[2], np.nan, np.nan, np.nan])
                continue
            d_sensor_normalized = d_sensor / norm_d_sensor
            
            d_body = R_sensor_to_body @ d_sensor_normalized
            d_world = R_body_to_world @ d_body 
            d_world_normalized = d_world / np.linalg.norm(d_world)

            gx, gy, gz = np.nan, np.nan, np.nan

            if z_ground_method == "dsm_intersection":
                if dsm_interpolator and dsm_bounds:
                    gx, gy, gz = calculate_ray_dsm_intersection(
                        P_sensor, d_world_normalized, dsm_interpolator, dsm_bounds, dsm_nodata_value,
                        ray_max_dist, ray_initial_step, ray_bisection_tol
                    )
                    if np.isnan(gx):
                        nan_intersection_count += 1
                else:
                    logger.warning(f"DSM intersection requested but interpolator/bounds not ready for line {i}, pixel {j}.")
                    nan_intersection_count += 1
            else: 
                if abs(d_world_normalized[2]) < d_world_z_threshold:
                    if i in debug_lines and j in debug_pixels:
                         logger.debug(f"  Line {i}, Pixel {j}: Horizontal ray (d_world_z={d_world_normalized[2]:.3e}). No intersection with flat plane.")
                else:
                    t = (Z_ground_flat_plane - P_sensor[2]) / d_world_normalized[2]
                    if t >= 0:
                        P_ground = P_sensor + t * d_world_normalized
                        gx, gy, gz = P_ground[0], P_ground[1], P_ground[2]
                    elif i in debug_lines and j in debug_pixels: 
                        logger.debug(f"  Line {i}, Pixel {j}: Intersection behind sensor (t={t:.2f}).")
            
            results.append([i, j, gx, gy, gz, P_sensor[0], P_sensor[1], P_sensor[2], d_world_normalized[0], d_world_normalized[1], d_world_normalized[2]])

            if i in debug_lines and j in debug_pixels:
                logger.debug(f"  Line {i}, Pixel {j}:")
                logger.debug(f"    P_imu_world: [{P_imu_world[0]:.3f}, {P_imu_world[1]:.3f}, {P_imu_world[2]:.3f}]")
                logger.debug(f"    Quat (x,y,z,w): [{q_world_to_body[0]:.4f}, {q_world_to_body[1]:.4f}, {q_world_to_body[2]:.4f}, {q_world_to_body[3]:.4f}]")
                logger.debug(f"    P_sensor: [{P_sensor[0]:.3f}, {P_sensor[1]:.3f}, {P_sensor[2]:.3f}]")
                logger.debug(f"    vinkelx_rad_corr={vinkelx_rad_corrected:.4f}, vinkely_rad_corr={vinkely_rad_corrected:.4f}")
                logger.debug(f"    d_sensor_norm: [{d_sensor_normalized[0]:.4f}, {d_sensor_normalized[1]:.4f}, {d_sensor_normalized[2]:.4f}]")
                logger.debug(f"    d_body: [{d_body[0]:.4f}, {d_body[1]:.4f}, {d_body[2]:.4f}]")
                logger.debug(f"    d_world_norm: [{d_world_normalized[0]:.4f}, {d_world_normalized[1]:.4f}, {d_world_normalized[2]:.4f}]")
                if z_ground_method == "dsm_intersection":
                    logger.debug(f"    Ground (DSM): [{gx:.3f}, {gy:.3f}, {gz:.3f}]")
                else:
                    logger.debug(f"    Ground (Flat Plane @ {Z_ground_flat_plane:.2f}m): [{gx:.3f}, {gy:.3f}, {gz:.3f}]")

    if nan_intersection_count > 0:
        logger.warning(f"{nan_intersection_count} pixels out of {num_lines * num_samples} had no valid DSM intersection.")

    output_df = pd.DataFrame(results, columns=['hsi_line', 'hsi_pixel', 'X', 'Y', 'Z', 
                                               'sensor_X', 'sensor_Y', 'sensor_Z',
                                               'ray_X', 'ray_Y', 'ray_Z'])
    try:
        output_df.to_csv(output_file_path, index=False, float_format='%.5f')
        logger.info(f"Georeferenced pixels successfully saved to: {output_file_path.resolve()}")
    except Exception as e:
        logger.error(f"Error saving results to {output_file_path.resolve()}: {e}", exc_info=True)
        return False
    
    logger.info("Georeferencing process completed.")
    return True

if __name__ == "__main__":
    # Setup basic logging for standalone execution
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(module)s - %(message)s')
    
    DEFAULT_CONFIG_PATH = 'config.toml' # Assumes it's in the same MVP directory
    logger.info(f"Running HSI Pixel Georeferencing with configuration: {DEFAULT_CONFIG_PATH}")
    
    # For standalone, we pass the path string. The function will handle loading.
    success = run_georeferencing(config_or_path=DEFAULT_CONFIG_PATH)
    
    if success:
        logger.info("HSI Pixel Georeferencing completed successfully.")
    else:
        logger.error("Error during HSI Pixel Georeferencing.")