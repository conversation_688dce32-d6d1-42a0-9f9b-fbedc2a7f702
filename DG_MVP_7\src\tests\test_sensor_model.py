"""
Unit tests for SensorModel class.

Tests sensor model initialization, LOS vector calculations, and coordinate transformations.
"""

import pytest
import numpy as np
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

from ..sensor_model import SensorModel
from ..config_models import Config


class TestSensorModel:
    """Test SensorModel class functionality."""
    
    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration for testing."""
        config = Mock(spec=Config)
        
        # Mock interior orientation
        config.sensor_model.interior_orientation.focal_length_mm = 35.0
        config.sensor_model.interior_orientation.pixel_size_um = 4.5
        config.sensor_model.interior_orientation.principal_point_x_mm = 0.01
        config.sensor_model.interior_orientation.principal_point_y_mm = -0.005
        config.sensor_model.interior_orientation.k1 = 1.2e-5
        config.sensor_model.interior_orientation.k2 = 0.0
        config.sensor_model.interior_orientation.k3 = 0.0
        config.sensor_model.interior_orientation.p1 = 0.0
        config.sensor_model.interior_orientation.p2 = 0.0
        
        # Mock boresight alignment
        config.sensor_model.boresight_alignment_deg.roll_offset_deg = 0.1
        config.sensor_model.boresight_alignment_deg.pitch_offset_deg = -1.35
        config.sensor_model.boresight_alignment_deg.yaw_offset_deg = 0.0
        
        # Mock lever arms
        config.sensor_model.lever_arms_meters.gnss_to_imu_x_m = 0.0
        config.sensor_model.lever_arms_meters.gnss_to_imu_y_m = 0.0
        config.sensor_model.lever_arms_meters.gnss_to_imu_z_m = 0.0
        config.sensor_model.lever_arms_meters.imu_to_sensor_x_m = 0.0
        config.sensor_model.lever_arms_meters.imu_to_sensor_y_m = 0.0
        config.sensor_model.lever_arms_meters.imu_to_sensor_z_m = 0.0
        
        # Mock uncertainties
        config.sensor_model.uncertainties = None
        
        # Mock georeferencing parameters
        config.parameters.georeferencing.scale_vinkel_x = 1.0
        config.parameters.georeferencing.offset_vinkel_x = 0.0
        
        return config
    
    @pytest.fixture
    def sample_sensor_model_file(self):
        """Create a sample sensor model file for testing."""
        # Create sample sensor model data
        sensor_data = []
        num_pixels = 100
        for i in range(num_pixels):
            # Create sample viewing angles (in radians)
            vinkelx = (i - num_pixels/2) * 0.001  # Across-track angle
            vinkely = 0.0  # Along-track angle (typically 0 for line scanners)
            sensor_data.append(f"{i} {vinkelx:.6f} {vinkely:.6f}")
        
        # Write to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as tmp_file:
            tmp_file.write("# Pixel Index, VinkelX (rad), VinkelY (rad)\n")
            for line in sensor_data:
                tmp_file.write(line + "\n")
            tmp_path = tmp_file.name
        
        return Path(tmp_path)
    
    def test_sensor_model_initialization(self, mock_config):
        """Test SensorModel initialization without sensor model file."""
        sensor_model = SensorModel(mock_config)
        
        assert sensor_model.focal_length_mm == 35.0
        assert sensor_model.pixel_size_um == 4.5
        assert sensor_model.boresight_roll_deg == 0.1
        assert sensor_model.boresight_pitch_deg == -1.35
        assert sensor_model.boresight_yaw_deg == 0.0
        
        # Check lever arms
        np.testing.assert_array_equal(sensor_model.gnss_to_imu_body, np.array([0.0, 0.0, 0.0]))
        np.testing.assert_array_equal(sensor_model.imu_to_sensor_body, np.array([0.0, 0.0, 0.0]))
        
        # Check rotation matrices
        assert sensor_model.R_sensor_to_body.shape == (3, 3)
        assert sensor_model.R_body_to_sensor.shape == (3, 3)
    
    def test_sensor_model_with_file(self, mock_config, sample_sensor_model_file):
        """Test SensorModel initialization with sensor model file."""
        try:
            sensor_model = SensorModel(mock_config, sample_sensor_model_file)
            
            assert sensor_model.vinkelx_rad_all_pixels is not None
            assert sensor_model.vinkely_rad_all_pixels is not None
            assert len(sensor_model.vinkelx_rad_all_pixels) == 100
            assert len(sensor_model.vinkely_rad_all_pixels) == 100
            
            # Check that angles are reasonable
            assert np.all(np.abs(sensor_model.vinkelx_rad_all_pixels) < 1.0)  # Should be small angles
            assert np.all(np.abs(sensor_model.vinkely_rad_all_pixels) < 0.1)  # Should be very small
            
        finally:
            sample_sensor_model_file.unlink(missing_ok=True)
    
    def test_calculate_los_vector(self, mock_config, sample_sensor_model_file):
        """Test LOS vector calculation."""
        try:
            sensor_model = SensorModel(mock_config, sample_sensor_model_file)
            
            # Test LOS vector calculation for middle pixel
            pixel_index = 50
            los_vector = sensor_model.calculate_los_vector(pixel_index)
            
            assert isinstance(los_vector, np.ndarray)
            assert los_vector.shape == (3,)
            assert np.isclose(np.linalg.norm(los_vector), 1.0)  # Should be normalized
            assert los_vector[2] < 0  # Z component should be negative (pointing down)
            
        finally:
            sample_sensor_model_file.unlink(missing_ok=True)
    
    def test_calculate_los_vector_invalid_index(self, mock_config, sample_sensor_model_file):
        """Test LOS vector calculation with invalid pixel index."""
        try:
            sensor_model = SensorModel(mock_config, sample_sensor_model_file)
            
            # Test with invalid indices
            with pytest.raises(ValueError):
                sensor_model.calculate_los_vector(-1)
            
            with pytest.raises(ValueError):
                sensor_model.calculate_los_vector(1000)  # Beyond available pixels
                
        finally:
            sample_sensor_model_file.unlink(missing_ok=True)
    
    def test_calculate_los_vector_no_file(self, mock_config):
        """Test LOS vector calculation without sensor model file."""
        sensor_model = SensorModel(mock_config)
        
        with pytest.raises(ValueError, match="Sensor model file not loaded"):
            sensor_model.calculate_los_vector(0)
    
    def test_apply_lens_distortion(self, mock_config):
        """Test lens distortion correction."""
        sensor_model = SensorModel(mock_config)
        
        # Test with a sample vector
        input_vector = np.array([0.1, 0.05, -1.0])
        input_vector = input_vector / np.linalg.norm(input_vector)
        
        corrected_vector = sensor_model.apply_lens_distortion(input_vector)
        
        assert isinstance(corrected_vector, np.ndarray)
        assert corrected_vector.shape == (3,)
        assert np.isclose(np.linalg.norm(corrected_vector), 1.0)  # Should remain normalized
    
    def test_transform_sensor_to_body(self, mock_config):
        """Test transformation from sensor to body frame."""
        sensor_model = SensorModel(mock_config)
        
        # Test with a sample vector
        sensor_vector = np.array([1.0, 0.0, 0.0])
        body_vector = sensor_model.transform_sensor_to_body(sensor_vector)
        
        assert isinstance(body_vector, np.ndarray)
        assert body_vector.shape == (3,)
        
        # Test inverse transformation
        sensor_vector_back = sensor_model.transform_body_to_sensor(body_vector)
        np.testing.assert_array_almost_equal(sensor_vector, sensor_vector_back)
    
    def test_calculate_sensor_position_world(self, mock_config):
        """Test sensor position calculation in world coordinates."""
        sensor_model = SensorModel(mock_config)
        
        # Test with sample data
        gnss_position = np.array([1000.0, 2000.0, 100.0])
        R_body_to_world = np.eye(3)  # Identity matrix for simplicity
        
        sensor_position = sensor_model.calculate_sensor_position_world(gnss_position, R_body_to_world)
        
        assert isinstance(sensor_position, np.ndarray)
        assert sensor_position.shape == (3,)
        
        # With zero lever arms, sensor position should equal GNSS position
        np.testing.assert_array_almost_equal(sensor_position, gnss_position)
    
    def test_get_uncertainty_parameters(self, mock_config):
        """Test uncertainty parameters retrieval."""
        sensor_model = SensorModel(mock_config)
        
        uncertainties = sensor_model.get_uncertainty_parameters()
        
        assert isinstance(uncertainties, dict)
        assert 'focal_length_uncertainty_mm' in uncertainties
        assert 'principal_point_uncertainty_mm' in uncertainties
        assert 'boresight_uncertainty_deg' in uncertainties
        assert 'lever_arm_uncertainty_m' in uncertainties
        
        # Check default values
        assert uncertainties['focal_length_uncertainty_mm'] == 0.1
        assert uncertainties['boresight_uncertainty_deg'] == 0.01


    def test_apply_lens_distortion_enhanced_model(self, mock_config, sample_sensor_model_file):
        """Test the enhanced lens distortion model implementation."""
        try:
            sensor_model = SensorModel(mock_config, sample_sensor_model_file)

            # Test with a sample undistorted vector
            undistorted_vector = np.array([0.1, 0.05, -1.0])
            undistorted_vector = undistorted_vector / np.linalg.norm(undistorted_vector)

            corrected_vector = sensor_model.apply_lens_distortion(undistorted_vector)

            assert isinstance(corrected_vector, np.ndarray)
            assert corrected_vector.shape == (3,)
            assert np.isclose(np.linalg.norm(corrected_vector), 1.0)  # Should remain normalized

            # With non-zero distortion coefficients, the vector should change
            if any([sensor_model.k1, sensor_model.k2, sensor_model.k3, sensor_model.p1, sensor_model.p2]):
                assert not np.allclose(undistorted_vector, corrected_vector)

        finally:
            sample_sensor_model_file.unlink(missing_ok=True)

    def test_apply_lens_distortion_no_distortion(self, mock_config):
        """Test lens distortion with zero distortion coefficients."""
        # Set all distortion coefficients to zero
        mock_config.sensor_model.interior_orientation.k1 = 0.0
        mock_config.sensor_model.interior_orientation.k2 = 0.0
        mock_config.sensor_model.interior_orientation.k3 = 0.0
        mock_config.sensor_model.interior_orientation.p1 = 0.0
        mock_config.sensor_model.interior_orientation.p2 = 0.0

        sensor_model = SensorModel(mock_config)

        # Test with a sample vector
        test_vector = np.array([0.1, 0.05, -1.0])
        test_vector = test_vector / np.linalg.norm(test_vector)

        corrected_vector = sensor_model.apply_lens_distortion(test_vector)

        # With zero distortion, the vector should be very similar (only projection effects)
        assert np.allclose(test_vector, corrected_vector, atol=1e-6)

    def test_apply_lens_distortion_parallel_vector(self, mock_config):
        """Test lens distortion with vector parallel to image plane."""
        sensor_model = SensorModel(mock_config)

        # Vector parallel to image plane (z ≈ 0)
        parallel_vector = np.array([1.0, 0.0, 1e-12])
        parallel_vector = parallel_vector / np.linalg.norm(parallel_vector)

        corrected_vector = sensor_model.apply_lens_distortion(parallel_vector)

        # Should return the original vector unchanged
        np.testing.assert_array_almost_equal(parallel_vector, corrected_vector)

    def test_apply_lens_distortion_edge_cases(self, mock_config):
        """Test lens distortion with edge case vectors."""
        sensor_model = SensorModel(mock_config)

        # Test with nadir-looking vector (straight down)
        nadir_vector = np.array([0.0, 0.0, -1.0])
        corrected_nadir = sensor_model.apply_lens_distortion(nadir_vector)

        assert isinstance(corrected_nadir, np.ndarray)
        assert np.isclose(np.linalg.norm(corrected_nadir), 1.0)

        # For nadir vector, distortion should be minimal (only principal point offset effects)
        assert abs(corrected_nadir[0]) < 0.1  # Small X component
        assert abs(corrected_nadir[1]) < 0.1  # Small Y component
        assert corrected_nadir[2] < 0  # Still pointing down

    def test_apply_lens_distortion_consistency(self, mock_config):
        """Test that lens distortion is consistent and deterministic."""
        sensor_model = SensorModel(mock_config)

        test_vector = np.array([0.2, -0.1, -1.0])
        test_vector = test_vector / np.linalg.norm(test_vector)

        # Apply distortion multiple times
        result1 = sensor_model.apply_lens_distortion(test_vector)
        result2 = sensor_model.apply_lens_distortion(test_vector)

        # Results should be identical
        np.testing.assert_array_almost_equal(result1, result2)


def test_placeholder():
    """Placeholder test to ensure pytest runs."""
    assert True
