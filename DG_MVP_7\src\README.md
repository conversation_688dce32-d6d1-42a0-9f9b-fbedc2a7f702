# HSI Direct Georeferencing Tool - Enhanced Version (LS1)

This directory contains the refactored and enhanced version of the HSI Direct Georeferencing Tool, implementing all recommendations from the research findings and prompts in `prompts_LS1.md`.

## Key Enhancements

### 🏗️ **Modular Architecture**
- **SensorModel Class**: Rigorous sensor geometry handling with IOPs, boresight, and lever arms
- **DSMManager**: Efficient DSM handling with tiling and spatial indexing
- **ErrorPropagationModule**: Per-pixel uncertainty estimation
- **Enhanced Configuration**: Comprehensive Pydantic models with validation

### 🎯 **Accuracy Improvements**
- **Detailed Interior Orientation Parameters (IOPs)**: Focal length, pixel size, principal point, lens distortion
- **Precise Boresight Alignment**: Standardized angular offsets between sensor and IMU frames
- **Complete Lever Arm Chain**: GNSS-to-IMU and IMU-to-Sensor transformations
- **Advanced Ray-DSM Intersection**: Enhanced algorithms with robust error handling

### 📊 **Quality Assessment**
- **Error Propagation**: Simplified uncertainty estimation combining multiple error sources
- **Quality Layers**: Optional uncertainty GeoTIFF generation
- **Comprehensive Metadata**: Detailed GeoTIFF metadata with processing parameters

### 🔧 **Software Engineering**
- **Input Validation**: Comprehensive checks using Pydantic models
- **Logging**: Structured logging with configurable levels
- **Testing Framework**: Unit tests with pytest
- **Error Handling**: Robust error handling with informative messages

## Directory Structure

```
src/
├── __init__.py                 # Package initialization
├── config_models.py           # Enhanced Pydantic configuration models
├── config_enhanced.toml        # Enhanced configuration file template
├── utils.py                    # Common utility functions
├── sensor_model.py             # Rigorous SensorModel class
├── dsm_manager.py              # DSM handling with tiling support
├── error_propagation.py        # Uncertainty estimation module
├── georeferencing.py           # Main georeferencing processor
├── rgb_creation.py             # Enhanced RGB GeoTIFF creation
├── main_pipeline.py            # Pipeline orchestration
├── tests/                      # Testing framework
│   ├── __init__.py
│   ├── test_config_loading.py
│   └── test_sensor_model.py
└── README.md                   # This file
```

## Configuration Enhancements

The enhanced configuration (`config_enhanced.toml`) includes:

### Interior Orientation Parameters (IOPs)
```toml
[sensor_model.interior_orientation]
focal_length_mm = 35.0
pixel_size_um = 4.5
principal_point_x_mm = 0.01
principal_point_y_mm = -0.005
k1 = 1.2e-5  # Radial distortion
k2 = -3.4e-8
k3 = 0.0
p1 = 7.6e-7  # Tangential distortion
p2 = -2.1e-7
```

### Boresight Alignment
```toml
[sensor_model.boresight_alignment_deg]
roll_offset_deg = 0.1
pitch_offset_deg = -1.35
yaw_offset_deg = 0.0
```

### Lever Arms (GNSS-IMU-Sensor Chain)
```toml
[sensor_model.lever_arms_meters]
gnss_to_imu_x_m = 0.0
gnss_to_imu_y_m = 0.0
gnss_to_imu_z_m = 0.0
imu_to_sensor_x_m = 0.0
imu_to_sensor_y_m = 0.0
imu_to_sensor_z_m = 0.0
```

### Uncertainty Parameters
```toml
[sensor_model.uncertainties]
iop_focal_length_uncertainty_mm = 0.1
iop_principal_point_uncertainty_mm = 0.01
boresight_uncertainty_deg = 0.01
lever_arm_uncertainty_m = 0.005
```

### DSM Parameters
```toml
[dsm_parameters]
path = "data/WebODM/dsm.tif"
type = "raster"
default_vertical_uncertainty_m = 0.5
nodata_value = -9999.0
ray_dsm_max_search_dist_m = 2000.0
ray_dsm_step_m = 5.0
ray_dsm_bisection_tolerance_m = 0.01
```

## Usage

### 1. Basic Usage
```python
from src.main_pipeline import run_complete_pipeline

# Run complete pipeline
success = run_complete_pipeline("src/config_enhanced.toml")
```

### 2. Individual Components
```python
from src.georeferencing import run_georeferencing
from src.rgb_creation import run_create_rgb_geotiff

# Run only georeferencing
success = run_georeferencing("src/config_enhanced.toml")

# Run only RGB creation
success = run_create_rgb_geotiff("src/config_enhanced.toml")
```

### 3. Command Line
```bash
# Run complete pipeline
python -m src.main_pipeline src/config_enhanced.toml

# Run individual components
python -c "from src.georeferencing import run_georeferencing; run_georeferencing('src/config_enhanced.toml')"
```

### 4. Advanced Usage
```python
from src.config_models import load_and_validate_config
from src.georeferencing import GeoreferencingProcessor
from src.sensor_model import SensorModel

# Load configuration
config = load_and_validate_config("src/config_enhanced.toml")

# Create sensor model
sensor_model = SensorModel(config, sensor_model_path)

# Calculate LOS vector for pixel
los_vector = sensor_model.calculate_los_vector(pixel_index=50)

# Run georeferencing with custom processor
processor = GeoreferencingProcessor(config)
success = processor.run_georeferencing()
```

## Testing

Run the test suite:
```bash
# Install pytest if not already installed
pip install pytest

# Run all tests
pytest src/tests/

# Run specific test file
pytest src/tests/test_sensor_model.py

# Run with verbose output
pytest -v src/tests/
```

## Dependencies

The enhanced version requires additional dependencies:
```
numpy
pandas
scipy
rasterio
pyproj
pydantic
scikit-learn
toml
pytest (for testing)
rtree (optional, for spatial indexing)
```

## Migration from MVP

To migrate from the MVP version:

1. **Update Configuration**: Use the enhanced `config_enhanced.toml` template
2. **Update Imports**: Change imports to use the new `src` package
3. **Update Function Calls**: Use the new function signatures
4. **Review Parameters**: Check that all required parameters are configured

## Output Enhancements

The enhanced version produces:

### Georeferenced Pixels CSV
- Additional uncertainty columns: `x_uncertainty_m`, `y_uncertainty_m`, `z_uncertainty_m`, `pos_uncertainty_m`
- Improved coordinate accuracy from enhanced sensor model

### RGB GeoTIFF
- Comprehensive metadata tags
- Optional uncertainty layer (`georeferenced_uncertainty.tif`)
- Improved georeferencing accuracy

### Logging
- Structured logging with timestamps
- Configurable log levels
- Processing statistics and timing information

## Performance Considerations

### DSM Tiling
- Large DSMs (>500MB) are automatically tiled
- Spatial indexing for efficient tile access
- Memory-efficient processing

### Error Handling
- Graceful degradation for missing data
- Comprehensive input validation
- Informative error messages

## Future Enhancements

The modular architecture supports easy addition of:
- Atmospheric correction module
- Advanced ray-DSM intersection algorithms (Intel Embree)
- Monte Carlo uncertainty propagation
- Real-time processing capabilities
- Additional output formats

## Support

For questions or issues with the enhanced version, refer to:
- Configuration validation errors: Check Pydantic model definitions
- Processing errors: Review log files with DEBUG level
- Performance issues: Consider DSM tiling parameters
- Accuracy concerns: Verify sensor calibration parameters
