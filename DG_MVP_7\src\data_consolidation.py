"""
Data Consolidation Module for HSI Direct Georeferencing Tool

This module implements robust data consolidation functionality, porting and refactoring
logic from MVP/create_consolidated_webodm_poses.py with enhanced error handling,
validation, and logging.

Implements requirements from prompt:
- LS2_DATA_PIPELINE
"""

import json
import logging
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Optional, Dict, List, Union, Tuple
from datetime import datetime

from .config_models import Config, load_and_validate_config
from .utils import setup_logger, validate_file_exists, validate_directory_exists

logger = logging.getLogger(__name__)


class DataConsolidator:
    """
    Consolidates EOP (Exterior Orientation Parameters) data from various sources.
    
    This class handles the consolidation of pose data from WebODM and other sources,
    providing robust parsing, validation, and output generation.
    """
    
    def __init__(self, config: Union[str, Path, Config]):
        """
        Initialize data consolidator.
        
        Args:
            config: Configuration file path or Config object
        """
        # Load and validate configuration
        if isinstance(config, (str, Path)):
            self.config = load_and_validate_config(config)
            if self.config is None:
                raise ValueError("Failed to load and validate configuration")
        else:
            self.config = config
        
        # Setup logging
        self.logger = setup_logger(
            f"{__name__}.{self.__class__.__name__}",
            self.config.processing_options.log_level
        )
        
        self.logger.info("DataConsolidator initialized")
    
    def validate_inputs(self) -> bool:
        """
        Validate input files and directories.
        
        Returns:
            True if all inputs are valid, False otherwise
        """
        self.logger.info("Validating inputs for data consolidation...")
        
        # Check WebODM data directory
        webodm_dir = Path(self.config.paths.webodm_data_directory)
        if not validate_directory_exists(webodm_dir, "WebODM data directory"):
            return False
        
        # Check shots.geojson file
        shots_file = webodm_dir / self.config.paths.shots_geojson_file
        if not validate_file_exists(shots_file, "Shots GeoJSON file"):
            return False
        
        # Check HAIP files directory
        haip_dir = webodm_dir / self.config.paths.haip_files_subdirectory
        if not validate_directory_exists(haip_dir, "HAIP files directory"):
            return False
        
        # Check output directory
        output_dir = Path(self.config.paths.output_directory)
        if not validate_directory_exists(output_dir, "Output directory", create_if_missing=True):
            return False
        
        self.logger.info("Input validation completed successfully")
        return True
    
    def load_shots_geojson(self) -> Optional[Dict]:
        """
        Load and parse shots.geojson file.
        
        Returns:
            Parsed GeoJSON data or None if loading fails
        """
        try:
            webodm_dir = Path(self.config.paths.webodm_data_directory)
            shots_file = webodm_dir / self.config.paths.shots_geojson_file
            
            self.logger.info(f"Loading shots GeoJSON from: {shots_file}")
            
            with open(shots_file, 'r', encoding='utf-8') as f:
                shots_data = json.load(f)
            
            # Validate GeoJSON structure
            if 'features' not in shots_data:
                self.logger.error("Invalid GeoJSON: missing 'features' field")
                return None
            
            num_features = len(shots_data['features'])
            self.logger.info(f"Loaded {num_features} shot features from GeoJSON")
            
            return shots_data
            
        except Exception as e:
            self.logger.error(f"Failed to load shots GeoJSON: {e}")
            return None
    
    def load_haip_files(self) -> Optional[List[Dict]]:
        """
        Load and parse HAIP files from the specified directory.
        
        Returns:
            List of parsed HAIP data or None if loading fails
        """
        try:
            webodm_dir = Path(self.config.paths.webodm_data_directory)
            haip_dir = webodm_dir / self.config.paths.haip_files_subdirectory
            
            self.logger.info(f"Loading HAIP files from: {haip_dir}")
            
            # Find all .haip files
            haip_files = list(haip_dir.glob("*.haip"))
            
            if not haip_files:
                self.logger.error(f"No .haip files found in {haip_dir}")
                return None
            
            self.logger.info(f"Found {len(haip_files)} HAIP files")
            
            haip_data_list = []
            timestamp_key = self.config.parameters.webodm_consolidation.haip_timestamp_key
            
            for haip_file in haip_files:
                try:
                    with open(haip_file, 'r', encoding='utf-8') as f:
                        haip_content = f.read().strip()
                    
                    # Parse HAIP content (assuming JSON format)
                    if haip_content.startswith('{'):
                        haip_data = json.loads(haip_content)
                    else:
                        # Try parsing as key-value pairs
                        haip_data = {}
                        for line in haip_content.split('\n'):
                            if '=' in line:
                                key, value = line.split('=', 1)
                                haip_data[key.strip()] = value.strip()
                    
                    # Extract timestamp
                    if timestamp_key in haip_data:
                        haip_data['timestamp'] = float(haip_data[timestamp_key])
                        haip_data['filename'] = haip_file.name
                        haip_data_list.append(haip_data)
                    else:
                        self.logger.warning(f"No timestamp key '{timestamp_key}' found in {haip_file.name}")
                
                except Exception as e:
                    self.logger.warning(f"Failed to parse HAIP file {haip_file.name}: {e}")
                    continue
            
            if not haip_data_list:
                self.logger.error("No valid HAIP files could be parsed")
                return None
            
            # Sort by timestamp
            haip_data_list.sort(key=lambda x: x['timestamp'])
            
            self.logger.info(f"Successfully loaded {len(haip_data_list)} HAIP files")
            return haip_data_list
            
        except Exception as e:
            self.logger.error(f"Failed to load HAIP files: {e}")
            return None
    
    def consolidate_poses(self, shots_data: Dict, haip_data_list: List[Dict]) -> Optional[pd.DataFrame]:
        """
        Consolidate pose data from shots and HAIP files.
        
        Args:
            shots_data: Parsed shots GeoJSON data
            haip_data_list: List of parsed HAIP data
            
        Returns:
            Consolidated poses DataFrame or None if consolidation fails
        """
        try:
            self.logger.info("Consolidating pose data...")
            
            consolidated_poses = []
            
            # Process each shot feature
            for feature in shots_data['features']:
                try:
                    properties = feature.get('properties', {})
                    geometry = feature.get('geometry', {})
                    
                    # Extract shot information
                    shot_id = properties.get('id', 'unknown')
                    filename = properties.get('filename', '')
                    
                    # Extract position from geometry
                    if geometry.get('type') == 'Point':
                        coordinates = geometry.get('coordinates', [])
                        if len(coordinates) >= 3:
                            pos_x, pos_y, pos_z = coordinates[:3]
                        else:
                            self.logger.warning(f"Incomplete coordinates for shot {shot_id}")
                            continue
                    else:
                        self.logger.warning(f"Invalid geometry type for shot {shot_id}")
                        continue
                    
                    # Extract rotation (quaternion) from properties
                    rotation = properties.get('rotation', [])
                    if len(rotation) >= 4:
                        rot_x, rot_y, rot_z, rot_w = rotation[:4]
                    else:
                        self.logger.warning(f"Incomplete rotation for shot {shot_id}")
                        continue
                    
                    # Find corresponding HAIP data by filename matching
                    corresponding_haip = None
                    for haip_data in haip_data_list:
                        haip_filename = haip_data.get('filename', '')
                        # Simple filename matching (could be enhanced)
                        if filename in haip_filename or haip_filename in filename:
                            corresponding_haip = haip_data
                            break
                    
                    # Create consolidated pose entry
                    pose_entry = {
                        'shot_id': shot_id,
                        'filename': filename,
                        'pos_x': pos_x,
                        'pos_y': pos_y,
                        'pos_z': pos_z,
                        'rot_x': rot_x,
                        'rot_y': rot_y,
                        'rot_z': rot_z,
                        'rot_w': rot_w,
                        'timestamp': corresponding_haip['timestamp'] if corresponding_haip else None,
                        'haip_matched': corresponding_haip is not None
                    }
                    
                    # Add additional HAIP data if available
                    if corresponding_haip:
                        for key, value in corresponding_haip.items():
                            if key not in ['timestamp', 'filename']:
                                pose_entry[f'haip_{key}'] = value
                    
                    consolidated_poses.append(pose_entry)
                    
                except Exception as e:
                    self.logger.warning(f"Failed to process shot feature: {e}")
                    continue
            
            if not consolidated_poses:
                self.logger.error("No poses could be consolidated")
                return None
            
            # Create DataFrame
            poses_df = pd.DataFrame(consolidated_poses)
            
            # Log statistics
            total_poses = len(poses_df)
            matched_poses = poses_df['haip_matched'].sum()
            
            self.logger.info(f"Consolidated {total_poses} poses")
            self.logger.info(f"HAIP matching: {matched_poses}/{total_poses} poses matched")
            
            # Sort by timestamp if available
            if 'timestamp' in poses_df.columns and poses_df['timestamp'].notna().any():
                poses_df = poses_df.sort_values('timestamp').reset_index(drop=True)
                self.logger.info("Poses sorted by timestamp")
            
            return poses_df
            
        except Exception as e:
            self.logger.error(f"Failed to consolidate poses: {e}")
            return None
    
    def save_consolidated_poses(self, poses_df: pd.DataFrame) -> bool:
        """
        Save consolidated poses to CSV file.
        
        Args:
            poses_df: Consolidated poses DataFrame
            
        Returns:
            True if saving successful, False otherwise
        """
        try:
            output_dir = Path(self.config.paths.output_directory)
            output_file = output_dir / self.config.paths.consolidated_webodm_poses_csv
            
            self.logger.info(f"Saving consolidated poses to: {output_file}")
            
            # Save to CSV
            poses_df.to_csv(output_file, index=False)
            
            self.logger.info(f"Successfully saved {len(poses_df)} consolidated poses")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save consolidated poses: {e}")
            return False
    
    def run_consolidation(self) -> bool:
        """
        Run the complete data consolidation process.
        
        Returns:
            True if consolidation successful, False otherwise
        """
        try:
            self.logger.info("Starting data consolidation process...")
            
            # Step 1: Validate inputs
            if not self.validate_inputs():
                return False
            
            # Step 2: Load shots GeoJSON
            shots_data = self.load_shots_geojson()
            if shots_data is None:
                return False
            
            # Step 3: Load HAIP files
            haip_data_list = self.load_haip_files()
            if haip_data_list is None:
                return False
            
            # Step 4: Consolidate poses
            poses_df = self.consolidate_poses(shots_data, haip_data_list)
            if poses_df is None:
                return False
            
            # Step 5: Save consolidated poses
            if not self.save_consolidated_poses(poses_df):
                return False
            
            self.logger.info("Data consolidation completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Data consolidation failed: {e}")
            return False


def run_data_consolidation(config_or_path: Union[str, Path, Config]) -> bool:
    """
    Main entry point for data consolidation.
    
    Args:
        config_or_path: Configuration file path or Config object
        
    Returns:
        True if consolidation successful, False otherwise
    """
    try:
        consolidator = DataConsolidator(config_or_path)
        return consolidator.run_consolidation()
    except Exception as e:
        logger.error(f"Failed to create data consolidator: {e}")
        return False
